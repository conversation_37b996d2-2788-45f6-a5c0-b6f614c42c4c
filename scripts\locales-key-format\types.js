/**
 * @typedef {{ incorrectKey: string, correctedKey: string, line: number }} incorrectKey
 */

/**
 * @typedef {Object.<string, incorrectKey[]>} incorrectKeys
 */

/**
 * @typedef {{ checkKeys: boolean, checkFileNames: boolean, verbose: boolean, languages: string[] }} options
 */

/**
 * @typedef {{ incorrectFileName: string, correctedFileName: string }} incorrectFileName
 */

/**
 * @typedef {"camelCase" | "kebab-case" | "PascalCase" | "snake_case" | "UPPER_SNAKE_CASE" | "Pascal_Snake_Case"} format
 */
