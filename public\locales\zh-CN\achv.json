{"achievements": {"name": "成就"}, "locked": {"name": "未解锁"}, "moneyAchv": {"description": "累计获得 ₽{{moneyAmount}}"}, "10KMoney": {"name": "小有积蓄", "name_female": "小有积蓄"}, "100KMoney": {"name": "大户人家", "name_female": "富豪"}, "1MMoney": {"name": "百万富翁", "name_female": "百万富翁"}, "10MMoney": {"name": "暴发户", "name_female": "暴发户"}, "damageAchv": {"description": "在单次攻击中造成\n {{damageAmount}} 点伤害"}, "250Dmg": {"name": "重拳出击"}, "1000Dmg": {"name": "神拳猛击", "name_female": "神拳猛击"}, "2500Dmg": {"name": "夺少？", "name_female": "夺少？"}, "10000Dmg": {"name": "一拳超人", "name_female": "一拳超人"}, "healAchv": {"description": "通过技能、能力或携带的道具\n一次性治疗 {{healAmount}} {{HP}}点"}, "250Heal": {"name": "新手奶妈", "name_female": "新手奶妈"}, "1000Heal": {"name": "治疗担当", "name_female": "治疗担当"}, "2500Heal": {"name": "牧师", "name_female": "牧师"}, "10000Heal": {"name": "泉水", "name_female": "泉水"}, "levelAchv": {"description": "将一只宝可梦提升到 Lv{{level}}"}, "lv100": {"name": "别急，后面还有"}, "lv250": {"name": "精英", "name_female": "精英"}, "lv1000": {"name": "天外有天"}, "ribbonAchv": {"description": "累计获得 {{ribbonAmount}} 个勋章"}, "10Ribbons": {"name": "宝可梦联盟冠军", "name_female": "宝可梦联盟冠军"}, "25Ribbons": {"name": "超级球联盟冠军", "name_female": "超级球联盟冠军"}, "50Ribbons": {"name": "高级球联盟冠军", "name_female": "高级球联盟冠军"}, "75Ribbons": {"name": "肉鸽球联盟冠军", "name_female": "肉鸽球联盟冠军"}, "100Ribbons": {"name": "大师球联盟冠军", "name_female": "大师球联盟冠军"}, "transferMaxStatStage": {"name": "团队协作", "description": "在一项属性强化至最大时用\n接力棒传递给其他宝可梦"}, "maxFriendship": {"name": "亲密无间", "description": "使一只宝可梦的亲密度\n达到最大值"}, "megaEvolve": {"name": "大变身", "description": "超级进化一只宝可梦"}, "gigantamax": {"name": "这位更是重量级", "description": "极巨化一只宝可梦"}, "terastallize": {"name": "本系爱好者", "description": "太晶化一只宝可梦"}, "stellarTerastallize": {"name": "隐藏属性", "description": "星晶化一只宝可梦"}, "splice": {"name": "无限融合", "description": "使用基因之楔将两只宝可梦\n融合在一起"}, "miniBlackHole": {"name": "一大洞的道具", "description": "获得一个迷你黑洞"}, "catchMythical": {"name": "神秘礼物", "description": "捕捉一只幻之宝可梦"}, "catchSubLegendary": {"name": "二级传说", "description": "捕捉一只二级传说宝可梦"}, "catchLegendary": {"name": "传说", "description": "捕捉一只传说宝可梦"}, "seeShiny": {"name": "闪耀夺目", "description": "在野外找到一只闪光宝可梦"}, "shinyParty": {"name": "呕心沥血", "name_female": "呕心沥血", "description": "拥有一支由闪光宝可梦组成\n的满员队伍"}, "hatchMythical": {"name": "幻兽蛋", "description": "从蛋中孵化出一只幻之宝可梦"}, "hatchSubLegendary": {"name": "二级传说蛋", "description": "从蛋中孵化出一只二级传说\n宝可梦"}, "hatchLegendary": {"name": "传说蛋", "description": "从蛋中孵化出一只传说宝可梦"}, "hatchShiny": {"name": "金色传说！", "description": "从蛋中孵化出一只闪光宝可梦"}, "hiddenAbility": {"name": "隐藏实力", "description": "捕捉一只拥有隐藏特性\n的宝可梦"}, "perfectIvs": {"name": "合格证", "description": "获得一只拥有完美个体值\n的宝可梦"}, "classicVictory": {"name": "战无不胜", "name_female": "战无不胜", "description": "在经典模式中通关游戏"}, "unevolvedClassicVictory": {"name": "带孩上班", "description": "通关经典模式时队伍中至少有\n一名未进化的宝可梦"}, "monoGenOne": {"name": "最初的劲敌", "description": "完成仅限第一世代的挑战"}, "monoGenTwo": {"name": "1.5世代", "description": "完成仅限第二世代的挑战"}, "monoGenThree": {"name": "“水太多了”", "description": "完成仅限第三世代的挑战"}, "monoGenFour": {"name": "她真是最强冠军吗？", "description": "完成仅限第四世代的挑战"}, "monoGenFive": {"name": "完全原创", "description": "完成仅限第五世代的挑战"}, "monoGenSix": {"name": "女大公", "description": "完成仅限第六世代的挑战"}, "monoGenSeven": {"name": "首届冠军", "description": "完成仅限第七世代的挑战"}, "monoGenEight": {"name": "冠军时刻！", "description": "完成仅限第八世代的挑战"}, "monoGenNine": {"name": "她又放水了", "description": "完成仅限第九世代的挑战"}, "monoType": {"description": "完成 {{type}} 单属性挑战"}, "monoNormal": {"name": "异乎寻常的寻常"}, "monoFighting": {"name": "我有真功夫"}, "monoFlying": {"name": "愤怒的小鸟"}, "monoPoison": {"name": "关都地区特色"}, "monoGround": {"name": "地震预报"}, "monoRock": {"name": "坚如磐石"}, "monoBug": {"name": "音箱蟀侠"}, "monoGhost": {"name": "捉鬼敢死队"}, "monoSteel": {"name": "铁巨人"}, "monoFire": {"name": "搓火球解决一切"}, "monoWater": {"name": "当雨来临，倾盆而下"}, "monoGrass": {"name": "别踏这个青"}, "monoElectric": {"name": "瞄准大岩蛇的角！"}, "monoPsychic": {"name": "脑洞大开"}, "monoIce": {"name": "如履薄冰"}, "monoDragon": {"name": "准神俱乐部"}, "monoDark": {"name": "总有叛逆期"}, "monoFairy": {"name": "林克，醒醒！"}, "freshStart": {"name": "初次尝试！", "description": "完成初次尝试挑战"}, "inverseBattle": {"name": "镜子子镜", "description": "完成逆转之战挑战\n战挑战之转逆成完"}, "flipStats": {"name": "等价交换", "description": "完成能力互换挑战"}, "flipInverse": {"name": "了转反", "description": "同时完成能力互换和逆转之战挑战"}, "nuzlocke": {"name": "Nuzlocke挑战！", "description": "在同时激活初次尝试、硬核与捕获受限挑战的情况下完成游戏。"}, "breedersInSpace": {"name": "饲养员上太空？", "description": "在太空中击败(专业)宝可梦培育家"}, "dailyVictory": {"name": "早起晨跑", "description": "完成每日挑战一次。"}}