{"pikachu": "普通", "pikachuCosplay": "換裝", "pikachuCoolCosplay": "搖滾巨星", "pikachuBeautyCosplay": "貴婦", "pikachuCuteCosplay": "流行偶像", "pikachuSmartCosplay": "博士", "pikachuToughCosplay": "面罩摔跤手", "pikachuPartner": "搭檔", "eevee": "普通", "eeveePartner": "搭檔", "pichu": "普通", "pichuSpiky": "刺刺耳", "unownA": "A", "unownB": "B", "unownC": "C", "unownD": "D", "unownE": "E", "unownF": "F", "unownG": "G", "unownH": "H", "unownI": "I", "unownJ": "J", "unownK": "K", "unownL": "L", "unownM": "M", "unownN": "N", "unownO": "O", "unownP": "P", "unownQ": "Q", "unownR": "R", "unownS": "S", "unownT": "T", "unownU": "U", "unownV": "V", "unownW": "W", "unownX": "X", "unownY": "Y", "unownZ": "Z", "unownExclamation": "!", "unownQuestion": "?", "castform": "普通", "castformSunny": "晴天", "castformRainy": "雨天", "castformSnowy": "雪天", "deoxysNormal": "普通", "deoxysAttack": "攻擊", "deoxysDefense": "防禦", "deoxysSpeed": "速度", "burmyPlant": "草木蓑衣", "burmySandy": "砂土蓑衣", "burmyTrash": "垃圾蓑衣", "cherubiOvercast": "陰天", "cherubiSunshine": "晴天", "shellosEast": "東海", "shellosWest": "西海", "rotom": "普通", "rotomHeat": "加熱", "rotomWash": "清洗", "rotomFrost": "結冰", "rotomFan": "旋轉", "rotomMow": "切割", "dialga": "普通", "dialgaOrigin": "起源", "palkia": "普通", "palkiaOrigin": "起源", "giratinaAltered": "別種", "giratinaOrigin": "起源", "shayminLand": "陸上", "shayminSky": "天空", "basculinRedStriped": "紅條紋", "basculinBlueStriped": "藍條紋", "basculinWhiteStriped": "白條紋", "darumaka": "普通模式", "darumakaZen": "達摩模式", "deerlingSpring": "春天", "deerlingSummer": "夏天", "deerlingAutumn": "秋天", "deerlingWinter": "冬天", "tornadusIncarnate": "化身", "tornadusTherian": "靈獸", "thundurusIncarnate": "化身", "thundurusTherian": "靈獸", "landorusIncarnate": "化身", "landorusTherian": "靈獸", "kyurem": "普通", "kyuremBlack": "闇黑", "kyuremWhite": "焰白", "keldeoOrdinary": "平常的樣子", "keldeoResolute": "覺悟的樣子", "meloettaAria": "歌聲", "meloettaPirouette": "舞步", "genesect": "普通", "genesectShock": "閃電卡帶", "genesectBurn": "火焰卡帶", "genesectChill": "冰凍卡帶", "genesectDouse": "水流卡帶", "froakie": "普通", "froakieBattleBond": "牽絆變身", "froakieAsh": "小智", "scatterbugMeadow": "花園花紋", "scatterbugIcySnow": "冰雪花紋", "scatterbugPolar": "雪國花紋", "scatterbugTundra": "雪原花紋", "scatterbugContinental": "大陸花紋", "scatterbugGarden": "庭園花紋", "scatterbugElegant": "高雅花紋", "scatterbugModern": "摩登花紋", "scatterbugMarine": "大海花紋", "scatterbugArchipelago": "群島花紋", "scatterbugHighPlains": "荒野花紋", "scatterbugSandstorm": "沙塵花紋", "scatterbugRiver": "大河花紋", "scatterbugMonsoon": "驟雨花紋", "scatterbugSavanna": "熱帶草原花紋", "scatterbugSun": "太陽花紋", "scatterbugOcean": "大洋花紋", "scatterbugJungle": "熱帶雨林花紋", "scatterbugFancy": "幻彩花紋", "scatterbugPokeBall": "球球花紋", "flabebeRed": "紅花", "flabebeYellow": "黃花", "flabebeOrange": "橙花", "flabebeBlue": "藍花", "flabebeWhite": "白花", "furfrou": "野生的樣子", "furfrouHeart": "心形造型", "furfrouStar": "星形造型", "furfrouDiamond": "菱形造型", "furfrouDebutante": "淑女造型", "furfrouMatron": "貴婦造型", "furfrouDandy": "紳士造型", "furfrouLaReine": "女王造型", "furfrouKabuki": "歌舞伎造型", "furfrouPharaoh": "國王造型", "espurrMale": "雄性", "espurrFemale": "雌性", "honedgeShield": "盾牌", "honedgeBlade": "刀劍", "pumpkaboo": "普通尺寸", "pumpkabooSmall": "小尺寸", "pumpkabooLarge": "大尺寸", "pumpkabooSuper": "特大尺寸", "xerneasNeutral": "放松模式", "xerneasActive": "活躍模式", "zygarde50": "５０％", "zygarde10": "１０％", "zygarde50Pc": "５０％ 群聚變形", "zygarde10Pc": "１０％ 群聚變形", "zygardeComplete": "完全體", "hoopa": "懲戒", "hoopaUnbound": "解放", "oricorioBaile": "熱辣熱辣風格", "oricorioPompom": "啪滋啪滋風格", "oricorioPau": "呼拉呼拉風格", "oricorioSensu": "輕盈輕盈風格", "rockruff": "普通", "rockruffOwnTempo": "特殊岩狗狗", "rockruffMidday": "白晝的樣子", "rockruffMidnight": "黑夜的樣子", "rockruffDusk": "黃昏的樣子", "wishiwashi": "單獨的樣子", "wishiwashiSchool": "魚群的樣子", "typeNullNormal": "屬性：一般", "typeNullFighting": "屬性：戰鬥", "typeNullFlying": "屬性：飛翔", "typeNullPoison": "屬性：毒", "typeNullGround": "屬性：大地", "typeNullRock": "屬性：岩石", "typeNullBug": "屬性：蟲子", "typeNullGhost": "屬性：幽靈", "typeNullSteel": "屬性：鋼鐵", "typeNullFire": "屬性：火焰", "typeNullWater": "屬性：清水", "typeNullGrass": "屬性：青草", "typeNullElectric": "屬性：電子", "typeNullPsychic": "屬性：精神", "typeNullIce": "屬性：冰雪", "typeNullDragon": "屬性：龍", "typeNullDark": "屬性：黑暗", "typeNullFairy": "屬性：妖精", "miniorRedMeteor": "紅色流星的樣子", "miniorOrangeMeteor": "橙色流星的樣子", "miniorYellowMeteor": "黃色流星的樣子", "miniorGreenMeteor": "綠色流星的樣子", "miniorBlueMeteor": "淺藍色流星的樣子", "miniorIndigoMeteor": "藍色流星的樣子", "miniorVioletMeteor": "紫色流星的樣子", "miniorRed": "紅色核心", "miniorOrange": "橙色核心", "miniorYellow": "黃色核心", "miniorGreen": "綠色核心", "miniorBlue": "淺藍色核心", "miniorIndigo": "藍色核心", "miniorViolet": "紫色核心", "mimikyuDisguised": "化形的樣子", "mimikyuBusted": "現形的樣子", "cosmog": "普通", "cosmogRadiantSun": "旭日狀態", "cosmogFullMoon": "满月状态", "necrozma": "普通", "necrozmaDuskMane": "黃昏之鬃", "necrozmaDawnWings": "拂曉之翼", "necrozmaUltra": "究極", "magearna": "普通", "magearnaOriginal": "500年前的顔色", "marshadow": "普通", "marshadowZenith": "全力", "cramorant": "普通", "cramorantGulping": "一口吞的樣子", "cramorantGorging": "大口吞的樣子", "toxelAmped": "高調的樣子", "toxelLowkey": "低調的樣子", "sinisteaPhony": "赝品", "sinisteaAntique": "真品", "milceryVanillaCream": "奶香香草", "milceryRubyCream": "奶香紅鑽", "milceryMatchaCream": "奶香抹茶", "milceryMintCream": "奶香薄荷", "milceryLemonCream": "奶香檸檬", "milcerySaltedCream": "奶香雪鹽", "milceryRubySwirl": "紅鑽綜合", "milceryCaramelSwirl": "焦糖綜合", "milceryRainbowSwirl": "三色綜合", "eiscue": "結凍頭", "eiscueNoIce": "解凍頭", "indeedeeMale": "雄性", "indeedeeFemale": "雌性", "morpekoFullBelly": "滿腹花紋", "morpekoHangry": "空腹花紋", "zacianHeroOfManyBattles": "百戰勇者", "zacianCrowned": "劍之王", "zamazentaHeroOfManyBattles": "百戰勇者", "zamazentaCrowned": "盾之王", "kubfuSingleStrike": "一擊流", "kubfuRapidStrike": "連擊流", "zarude": "普通", "zarudeDada": "老爹", "calyrex": "普通", "calyrexIce": "騎白馬的樣子", "calyrexShadow": "騎黑馬的樣子", "basculinMale": "雄性", "basculinFemale": "雌性", "enamorusIncarnate": "化身", "enamorusTherian": "靈獸", "lechonkMale": "雄性", "lechonkFemale": "雌性", "tandemausFour": "四隻家庭", "tandemausThree": "三隻家庭", "squawkabillyGreenPlumage": "綠羽毛", "squawkabillyBluePlumage": "藍羽毛", "squawkabillyYellowPlumage": "黃羽毛", "squawkabillyWhitePlumage": "白羽毛", "finizenZero": "平凡", "finizenHero": "全能", "revavroomSeginStarmobile": "閣道二天星隊車", "revavroomSchedarStarmobile": "王良四天星隊車", "revavroomNaviStarmobile": "策星天星隊車", "revavroomRuchbahStarmobile": "閣道三天星隊車", "revavroomCaphStarmobile": "王良一天星隊車", "tatsugiriCurly": "上弓姿勢", "tatsugiriDroopy": "下垂姿勢", "tatsugiriStretchy": "平挺姿勢", "dunsparceTwoSegment": "二節", "dunsparceThreeSegment": "三節", "gimmighoulChest": "寶箱形態", "gimmighoulRoaming": "徒步形態", "koraidonApexBuild": "頂尖形態", "koraidonLimitedBuild": "限制形態", "koraidonSprintingBuild": "沖刺形態", "koraidonSwimmingBuild": "遊泳形態", "koraidonGlidingBuild": "滑翔形態", "miraidonUltimateMode": "極限模式", "miraidonLowPowerMode": "節能模式", "miraidonDriveMode": "駕駛模式", "miraidonAquaticMode": "水上模式", "miraidonGlideMode": "滑翔模式", "poltchageistCounterfeit": "冒牌貨的樣子", "poltchageistArtisan": "高檔貨的樣子", "poltchageistUnremarkable": "凡作的樣子", "poltchageistMasterpiece": "傑作的樣子", "ogerponTealMask": "碧草面具", "ogerponTealMaskTera": "碧草面具太晶化", "ogerponWellspringMask": "水井面具", "ogerponWellspringMaskTera": "水井面具太晶化", "ogerponHearthflameMask": "火灶面具", "ogerponHearthflameMaskTera": "火灶面具太晶化", "ogerponCornerstoneMask": "礎石面具", "ogerponCornerstoneMaskTera": "礎石面具太晶化", "terapagos": "普通", "terapagosTerastal": "太晶", "terapagosStellar": "星晶", "galarDarumaka": "普通模式", "galarDarumakaZen": "達摩模式", "paldeaTaurosCombat": "鬥戰種", "paldeaTaurosBlaze": "火熾種", "paldeaTaurosAqua": "水瀾種", "floetteEternalFlower": "永恆之花", "ursalunaBloodmoon": "赫月", "regionalForm": {"alola": "阿羅拉的樣子", "galar": "伽勒爾的樣子", "hisui": "洗翠的樣子", "paldea": "帕底亞的樣子"}, "appendForm": {"generic": "{{pokemonName}} ({{formName}})", "alola": "阿羅拉{{pokemonName}}", "galar": "伽勒爾{{pokemonName}}", "hisui": "洗翠{{pokemon<PERSON>ame}}", "paldea": "帕底亞{{pokemonName}}", "eternal": "永恆之花{{pokemonName}}", "bloodmoon": "赫月{{pokemonName}}"}, "battleForm": {"mega": "超級", "megaX": "超級X", "megaY": "超級Y", "primal": "原始回歸", "gigantamax": "超極巨化", "gigantamaxSingle": "超極巨會心一擊", "gigantamaxRapid": "超極巨流水連擊", "eternamax": "無極巨化"}}