{"trappedDesc": "束縛", "flinchedDesc": "畏縮", "confusedDesc": "混亂", "infatuatedDesc": "著迷", "seedDesc": "寄生種子", "nightmareDesc": "惡夢", "ingrainDesc": "扎根", "drowsyDesc": "瞌睡", "rechargingLapse": "{{pokemonNameWithAffix}}因攻擊的反作用力而無法動彈！", "trappedOnAdd": "{{pokemonNameWithAffix}}不能逃跑！", "trappedOnRemove": "{{pokemonNameWithAffix}}擺脫了{{moveName}}！", "flinchedLapse": "{{pokemonNameWithAffix}}畏縮了！", "confusedOnAdd": "{{pokemonNameWithAffix}}混亂了！", "confusedOnRemove": "{{pokemonNameWithAffix}}的混亂解除了！", "confusedOnOverlap": "{{pokemonNameWithAffix}}已經混亂了。", "confusedLapse": "{{pokemonNameWithAffix}}正在混亂中！", "confusedLapseHurtItself": "不知所以地攻擊了自己！", "destinyBondLapseIsBoss": "{{pokemonNameWithAffix}}不再受到同命的影響", "destinyBondLapse": "{{pokemonNameWithAffix}} 和{{pokemonNameWithAffix2}} 同歸於盡了！", "laserFocusOnAdd": "{{pokemonNameWithAffix}}\n集中了精神！", "infatuatedOnAdd": "{{pokemonNameWithAffix}}對{{sourcePokemonName}}著迷了！", "infatuatedOnOverlap": "{{pokemonNameWithAffix}}已經著迷了！", "infatuatedLapse": "{{pokemonNameWithAffix}}對{{sourcePokemonName}}著迷中！", "infatuatedLapseImmobilize": "{{pokemonNameWithAffix}} 不會著迷！", "infatuatedOnRemove": "{{pokemonNameWithAffix}} 治癒了著迷狀態！", "seededOnAdd": "將種子種植在了{{pokemonNameWithAffix}}身上！", "seededLapse": "{{pokemonNameWithAffix}}被寄生種子吸取了體力！", "seededLapseShed": "{{pokemonNameWithAffix}}吸到了污泥漿！", "nightmareOnAdd": "{{pokemonNameWithAffix}}開始做惡夢了！", "nightmareOnOverlap": "{{pokemonNameWithAffix}}已經被惡夢纏身！", "nightmareLapse": "{{pokemonNameWithAffix}}正被惡夢纏身！", "encoreOnAdd": "{{pokemonNameWithAffix}}接受了再來一次！", "encoreOnRemove": "{{pokemonNameWithAffix}}的再來一次狀態解除了！", "helpingHandOnAdd": "{{pokemonNameWithAffix}}擺出了幫助{{pokemonName}} 的架勢！", "ingrainLapse": "{{pokemonNameWithAffix}}用扎根回復了體力！", "ingrainOnTrap": "{{pokemonNameWithAffix}}扎根了！", "aquaRingOnAdd": "{{pokemonNameWithAffix}}用水流環包裹了自己！", "aquaRingLapse": "{{moveName}}回復了{{pokemonName}}的體力！", "drowsyOnAdd": "{{pokemonNameWithAffix}}產生睡意了！", "damagingTrapLapse": "{{pokemonNameWithAffix}}受到了{{moveName}}的傷害！", "bindOnTrap": "{{pokemonNameWithAffix}}被{{sourcePokemonName}}的 {{moveName}}緊緊束縛住了！", "wrapOnTrap": "{{pokemonNameWithAffix}}被{{sourcePokemonName}}綁緊了！", "vortexOnTrap": "{{pokemonNameWithAffix}}被困在了旋渦之中！", "clampOnTrap": "{{sourcePokemonNameWithAffix}}用貝殼夾住了{{pokemonName}}！", "sandTombOnTrap": "{{pokemonNameWithAffix}}被{{moveName}}困住了！", "magmaStormOnTrap": "{{pokemonNameWithAffix}}被困在了熔岩風暴之中！", "snapTrapOnTrap": "{{pokemonNameWithAffix}}被捕獸夾困住了！", "thunderCageOnTrap": "{{sourcePokemonNameWithAffix}}困住了{{pokemonNameWithAffix}}！", "infestationOnTrap": "{{pokemonNameWithAffix}}受到了{{sourcePokemonNameWithAffix}}的死纏爛打！", "protectedOnAdd": "{{pokemonNameWithAffix}}擺出了防守的架勢！", "protectedLapse": "{{pokemonNameWithAffix}}在攻擊中保護了自己！", "enduringOnAdd": "{{pokemonNameWithAffix}}擺出了挺住攻擊的架勢！", "enduringLapse": "{{pokemonNameWithAffix}}挺住了攻擊！", "sturdyLapse": "{{pokemonNameWithAffix}}挺住了攻擊！", "perishSongLapse": "{{pokemonNameWithAffix}} 的滅亡計時變成{{turnCount}}了！", "centerOfAttentionOnAdd": "{{pokemonNameWithAffix}}\n變得萬眾矚目了！", "truantLapse": "{{pokemonNameWithAffix}}正在偷懶！", "slowStartOnAdd": "{{pokemonNameWithAffix}}無法拿出平時的水平！", "slowStartOnRemove": "{{pokemonNameWithAffix}}恢復了平時的水平！", "highestStatBoostOnAdd": "{{pokemonNameWithAffix}}的{{statName}}升高了！", "highestStatBoostOnRemove": "{{pokemonNameWithAffix}}的{{abilityName}}效果解除了！", "magnetRisenOnAdd": "{{pokemonNameWithAffix}}\n因電磁力浮了起來！", "magnetRisenOnRemove": "{{pokemonNameWithAffix}}的\n電磁力消失了！", "critBoostOnAdd": "{{pokemonNameWithAffix}}現在幹勁十足！", "critBoostOnRemove": "{{pokemonNameWithAffix}}如釋重負似地放鬆了下來。", "saltCuredOnAdd": "{{pokemonNameWithAffix}} 陷入了鹽腌狀態！", "saltCuredLapse": "{{pokemonNameWithAffix}} 受到了{{moveName}}的傷害！", "cursedOnAdd": "{{pokemonNameWithAffix}}削減了自己的體力，並詛咒了{{pokemonName}}！", "cursedLapse": "{{pokemonNameWithAffix}}正受到詛咒！", "stockpilingOnAdd": "{{pokemonNameWithAffix}}蓄力了{{stockpiledCount}}次！", "disabledOnAdd": "封住了{{pokemonNameWithAffix}}的\n{moveName}}！", "disabledLapse": "{{pokemonNameWithAffix}}的\n定身法解除了！", "tarShotOnAdd": "{{pokemonNameWithAffix}}\n變得怕火了！", "shedTailOnAdd": "{{pokemonNameWithAffix}}\n截斷尾巴，把它做成了替身！", "substituteOnAdd": "{{pokemonNameWithAffix}}的\n替身出現了！", "substituteOnHit": "替身代替{{pokemonNameWithAffix}}承受了攻擊！", "substituteOnRemove": "{{pokemonNameWithAffix}}的\n替身消失了……", "tormentOnAdd": "{{pokemonNameWithAffix}}\n遭到了無理取鬧！", "tauntOnAdd": "{{pokemonNameWithAffix}}\n中了挑釁！", "tauntOnRemove": "{{pokemonNameWithAffix}}的\n挑釁效果解除了！", "imprisonOnAdd": "{{pokemonNameWithAffix}}\n封印了對手的招式！", "autotomizeOnAdd": "{{pokemonNameWithAffix}}\n變得身輕如燕了！", "syrupBombOnAdd": "{{pokemonNameWithAffix}}陷入了\n滿身糖狀態", "syrupBombLapse": "满身糖狀態讓{{pokemonNameWithAffix}}\n變慢了！", "telekinesisOnAdd": "讓{{pokemonNameWithAffix}}\n浮在了空中！", "electrifiedOnAdd": "因為輸電，{{pokemonNameWithAffix}}的\n招式變成了電屬性！", "powerTrickActive": "{{pokemonNameWithAffix}}\n互換了攻擊和防禦！", "powderOnAdd": "向{{pokemonNameWithAffix}}\n拋灑了粉塵！", "powderLapse": "和{{moveName}}起了反應，\n粉塵爆炸了！", "grudgeOnAdd": "{{pokemonNameWithAffix}}\n想向對手施放怨念！", "grudgeLapse": "因為怨念，{{pokemonNameWithAffix}}失去了\n{{moveName}}的所有PP！", "magicCoatOnAdd": "{{pokemonNameWithAffix}}\n獲得了魔法反射的效果！"}