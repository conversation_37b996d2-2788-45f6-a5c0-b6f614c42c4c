{"trappedDesc": "束缚", "flinchedDesc": "畏缩", "confusedDesc": "混乱", "infatuatedDesc": "着迷", "seedDesc": "寄生种子", "nightmareDesc": "恶梦", "ingrainDesc": "扎根", "drowsyDesc": "瞌睡", "rechargingLapse": "{{pokemonNameWithAffix}}\n因攻击的反作用力而无法动弹！", "trappedOnAdd": "{{pokemonNameWithAffix}}不能逃跑！", "trappedOnRemove": "{{pokemonNameWithAffix}}\n摆脱了{{moveName}}！", "flinchedLapse": "{{pokemonNameWithAffix}}\n畏缩了，无法使出招式！", "confusedOnAdd": "{{pokemonNameWithAffix}}\n混乱了！", "confusedOnRemove": "{{pokemonNameWithAffix}}\n的混乱解除了！", "confusedOnOverlap": "{{pokemonNameWithAffix}}\n已经混乱了。", "confusedLapse": "{{pokemonNameWithAffix}}\n正在混乱中！", "confusedLapseHurtItself": "不知所以地攻击了自己！", "destinyBondLapseIsBoss": "{{pokemonNameWithAffix}}\n不再受到同命的影响", "destinyBondLapse": "{{pokemonNameWithAffix}}\n和{{pokemonNameWithAffix2}}同归于尽了！", "laserFocusOnAdd": "{{pokemonNameWithAffix}}\n磨砺了精神！", "infatuatedOnAdd": "{{pokemonNameWithAffix}}\n对{{sourcePokemonName}}着迷了！", "infatuatedOnOverlap": "{{pokemonNameWithAffix}}\n已经着迷了！", "infatuatedLapse": "{{pokemonNameWithAffix}}\n对{{sourcePokemonName}}着迷中！", "infatuatedLapseImmobilize": "{{pokemonNameWithAffix}}\n不会着迷！", "infatuatedOnRemove": "{{pokemonNameWithAffix}}\n治愈了着迷状态！", "seededOnAdd": "将种子种植在了\n{{pokemonNameWithAffix}}的身上！", "seededLapse": "{{pokemonNameWithAffix}}\n被寄生种子吸取了体力！", "seededLapseShed": "{{pokemonNameWithAffix}}\n吸到了污泥浆！", "nightmareOnAdd": "{{pokemonNameWithAffix}}\n开始做恶梦了！", "nightmareOnOverlap": "{{pokemonNameWithAffix}}\n已经被恶梦缠身！", "nightmareLapse": "{{pokemonNameWithAffix}}\n正被恶梦缠身！", "encoreOnAdd": "{{pokemonNameWithAffix}}\n接受了再来一次！", "encoreOnRemove": "{{pokemonNameWithAffix}}\n的再来一次状态解除了！", "helpingHandOnAdd": "{{pokemonNameWithAffix}}\n摆出了帮助{{pokemonName}}的架势！", "ingrainLapse": "{{pokemonNameWithAffix}}\n用扎根回复了体力！", "ingrainOnTrap": "{{pokemonNameWithAffix}}\n扎根了！", "aquaRingOnAdd": "{{pokemonNameWithAffix}}\n用水流环包裹了自己！", "aquaRingLapse": "{{moveName}}回复了\n{{pokemonName}}的体力！", "drowsyOnAdd": "{{pokemonNameWithAffix}}\n产生睡意了！", "damagingTrapLapse": "{{pokemonNameWithAffix}}受到了\n{{moveName}}的伤害！", "bindOnTrap": "{{pokemonNameWithAffix}}被\n{{sourcePokemonName}}的{{moveName}}紧紧束缚住了！", "wrapOnTrap": "{{pokemonNameWithAffix}}被\n{{sourcePokemonName}}绑紧了！", "vortexOnTrap": "{{pokemonNameWithAffix}}\n被困在了旋涡之中！", "clampOnTrap": "{{sourcePokemonNameWithAffix}}用贝壳\n夹住了{{pokemonName}}！", "sandTombOnTrap": "{{pokemonNameWithAffix}}\n被{{moveName}}困住了！", "magmaStormOnTrap": "{{pokemonNameWithAffix}}\n被困在了熔岩风暴之中！", "snapTrapOnTrap": "{{pokemonNameWithAffix}}\n被捕兽夹困住了！", "thunderCageOnTrap": "{{sourcePokemonNameWithAffix}}困住了\n{{pokemonNameWithAffix}}！", "infestationOnTrap": "{{pokemonNameWithAffix}}受到了\n{{sourcePokemonNameWithAffix}}的死缠烂打！", "protectedOnAdd": "{{pokemonNameWithAffix}}\n摆出了防守的架势！", "protectedLapse": "{{pokemonNameWithAffix}}\n在攻击中保护了自己！", "enduringOnAdd": "{{pokemonNameWithAffix}}\n摆出了挺住攻击的架势！", "enduringLapse": "{{pokemonNameWithAffix}}\n挺住了攻击！", "sturdyLapse": "{{pokemonNameWithAffix}}\n挺住了攻击！", "perishSongLapse": "{{pokemonNameWithAffix}}\n的灭亡计时变成{{turnCount}}了！", "centerOfAttentionOnAdd": "{{pokemonNameWithAffix}}\n变得万众瞩目了！", "truantLapse": "{{pokemonNameWithAffix}}\n正在偷懒！", "slowStartOnAdd": "{{pokemonNameWithAffix}}\n无法拿出平时的水平！", "slowStartOnRemove": "{{pokemonNameWithAffix}}\n恢复了平时的水平！", "highestStatBoostOnAdd": "{{pokemonNameWithAffix}}的\n{{statName}}提高了！", "highestStatBoostOnRemove": "{{pokemonNameWithAffix}}的\n{{abilityName}}效果解除了！", "magnetRisenOnAdd": "{{pokemonNameWithAffix}}\n因电磁力浮了起来！", "magnetRisenOnRemove": "{{pokemonNameWithAffix}}的\n电磁力消失了！", "critBoostOnAdd": "{{pokemonNameWithAffix}}\n现在干劲十足！", "critBoostOnRemove": "{{pokemonNameWithAffix}}\n如释重负似地放松了下来。", "saltCuredOnAdd": "{{pokemonNameWithAffix}}\n陷入了盐腌状态！", "saltCuredLapse": "{{pokemonNameWithAffix}}\n受到了{{moveName}}的伤害！", "cursedOnAdd": "{{pokemonNameWithAffix}}削减了自己的体力，\n并诅咒了{{pokemonName}}！", "cursedLapse": "{{pokemonNameWithAffix}}\n正受到诅咒！", "stockpilingOnAdd": "{{pokemonNameWithAffix}}蓄力了{{stockpiledCount}}次！", "disabledOnAdd": "封住了{{pokemonNameWithAffix}}的\n{{moveName}}！", "disabledLapse": "{{pokemonNameWithAffix}}的\n定身法解除了！", "tarShotOnAdd": "{{pokemonNameWithAffix}}\n变得怕火了！", "shedTailOnAdd": "{{pokemonNameWithAffix}}\n断掉尾巴并将其作为替身了！", "substituteOnAdd": "{{pokemonNameWithAffix}}的\n替身出现了！", "substituteOnHit": "替身代替{{pokemonNameWithAffix}}\n承受了攻击！", "substituteOnRemove": "{{pokemonNameWithAffix}}的\n替身消失了……", "tormentOnAdd": "{{pokemonNameWithAffix}}遭到了无理取闹！", "tauntOnAdd": "{{pokemonNameWithAffix}}中了挑衅！", "tauntOnRemove": "{{pokemonNameWithAffix}}的\n挑衅效果解除了！", "imprisonOnAdd": "{{pokemonNameWithAffix}}封印了对手的招式！", "autotomizeOnAdd": "{{pokemonNameWithAffix}}变得身轻如燕了！", "syrupBombOnAdd": "{{pokemonNameWithAffix}}陷入了\n满身糖状态！", "syrupBombLapse": "满身糖让{{pokemonNameWithAffix}}\n变慢了！", "telekinesisOnAdd": "{{pokemonNameWithAffix}}浮在了空中！", "electrifiedOnAdd": "{{pokemonNameWithAffix}}的招式变成了电属性！", "powerTrickActive": "{{pokemonNameWithAffix}} 交换了它的攻击和防御！", "powderOnAdd": "{{pokemonNameWithAffix}} 被粉尘覆盖着！", "powderLapse": "火焰触碰到宝可梦身上的粉尘，引发了爆炸！", "grudgeOnAdd": "{{pokemonNameWithAffix}}想向对手施放怨念！", "grudgeLapse": "因为怨念，{{pokemonNameWithAffix}}失去了{{moveName}}的所有PP！", "magicCoatOnAdd": "{{pokemonNameWithAffix}}获得了魔法反射的效果！"}