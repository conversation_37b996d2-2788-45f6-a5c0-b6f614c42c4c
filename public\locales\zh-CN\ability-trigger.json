{"blockRecoilDamage": "{{pokemonName}}的{{abilityName}}\n抵消了反作用力！", "badDreams": "{{pokemon<PERSON>ame}}被折磨着！", "costar": "{{pokemonName}}复制了{{allyName}}的能力变化！", "iceFaceAvoidedDamage": "{{pokemonNameWithAffix}}因为{{abilityName}}\n避免了伤害！", "perishBody": "因为{{pokemonName}}的{{abilityName}}\n双方将在3回合后灭亡！", "poisonHeal": "{{pokemonName}}因{{abilityName}}\n回复了少许HP！", "trace": "{{pokemonName}}复制了{{targetName}}的\n{{abilityName}}！", "windPowerCharged": "受{{moveName}}的影响，{{pokemonName}}提升了能力！", "quickDraw": "因为速击效果发动，\n{{pokemonName}}比平常出招更快了！", "illusionBreak": "{{pokemon<PERSON>ame}}造成的\n幻觉被解除了！", "disguiseAvoidedDamage": "{{pokemonNameWithAffix}}的画皮脱落了！", "blockItemTheft": "{{pokemonNameWithAffix}}的{{abilityName}}\n阻止了对方夺取道具！", "typeImmunityHeal": "{{pokemonNameWithAffix}}因{{abilityName}}\n回复了少许HP！", "nonSuperEffectiveImmunity": "{{pokemonNameWithAffix}}因{{abilityName}}\n避免了伤害！", "fullHpResistType": "{{pokemonNameWithAffix}}\n让甲壳发出光辉，使属性相克发生扭曲！", "moveImmunity": "对{{pokemonNameWithAffix}}没有效果！", "reverseDrain": "{{pokemonNameWithAffix}}\n吸到了污泥浆！", "postDefendTypeChange": "{{pokemonNameWithAffix}}因{{abilityName}}\n变成了{{typeName}}属性！", "postDefendContactDamage": "{{pokemonNameWithAffix}}的{{abilityName}}\n使对方受到了伤害！", "postDefendAbilitySwap": "{{pokemonNameWithAffix}}\n互换了各自的特性！", "postDefendAbilityGive": "因为{{pokemonNameWithAffix}}\n对方的特性变成了{{abilityName}}！", "postDefendMoveDisable": "封住了{{pokemonNameWithAffix}}的\n{{moveName}}！", "pokemonTypeChange": "{{pokemonNameWithAffix}}\n变成了{{moveType}}属性！", "pokemonTypeChangeRevert": "{{pokemonNameWithAffix}}变回了\n原本的属性！", "postAttackStealHeldItem": "{{pokemonNameWithAffix}}从{{defenderName}}那里\n夺取了{{stolenItemType}}！", "postDefendStealHeldItem": "{{pokemonNameWithAffix}}从{{attackerName}}那里\n夺取了{{stolenItemType}}！", "copyFaintedAllyAbility": "继承了{{pokemonNameWithAffix}}的\n{{abilityName}}！", "intimidateImmunity": "{{pokemonNameWithAffix}}因{{abilityName}}没有受到威吓！", "postSummonAllyHeal": "{{pokemonNameWithAffix}}喝光了\n{{pokemonName}}泡的茶！", "postSummonClearAllyStats": "{{pokemonNameWithAffix}}的\n能力变化消失了！", "postSummonTransform": "{{pokemonNameWithAffix}}\n变身成了{{targetName}}！", "protectStat": "因{{pokemonNameWithAffix}}的{{abilityName}}\n{{statName}}不会降低！", "statusEffectImmunityWithName": "{{pokemonNameWithAffix}}因{{abilityName}}\n{{statusEffectName}}没有效果！", "statusEffectImmunity": "{{pokemonNameWithAffix}}因{{abilityName}}\n异常状态没有效果！", "battlerTagImmunity": "{{pokemonNameWithAffix}}因{{abilityName}}\n而不会{{battlerTagName}}！", "typeImmunityPowerBoost": "{{pokemonNameWithAffix}}的{{typeName}}属性的威力上升了！", "forewarn": "{{pokemonNameWithAffix}}读取了\n{{moveName}}！", "frisk": "{{pokemonNameWithAffix}}察觉到了\n{{opponentName}}的{{opponentAbilityName}}！", "postWeatherLapseHeal": "{{pokemonNameWithAffix}}因{{abilityName}}\n回复了少许HP！", "postWeatherLapseDamage": "{{pokemonNameWithAffix}}\n因{{abilityName}}而受到了伤害！", "postTurnLootCreateEatenBerry": "{{pokemonNameWithAffix}}\n收获了{{berryName}}！", "postTurnHeal": "{{pokemonNameWithAffix}}因{{abilityName}}\n回复了少许HP！", "fetchBall": "{{pokemonNameWithAffix}}\n捡回了{{pokeballName}}！", "healFromBerryUse": "{{pokemonNameWithAffix}}因{{abilityName}}\n回复了HP！", "arenaTrap": "因{{pokemonNameWithAffix}}的{{abilityName}}\n而无法进行替换！", "postBattleLoot": "{{pokemonNameWithAffix}}捡到了\n{{itemName}}！", "postFaintContactDamage": "{{pokemonNameWithAffix}}的{{abilityName}}\n使对方受到了伤害！", "postFaintHpDamage": "{{pokemonNameWithAffix}}的{{abilityName}}\n使对方受到了伤害！", "postSummonPressure": "从{{pokemonNameWithAffix}}的身上\n感到了一种压迫感！", "weatherEffectDisappeared": "天气的影响消失了！", "postSummonMoldBreaker": "{{pokemonNameWithAffix}}\n打破了常规！", "postSummonAnticipation": "{{pokemonNameWithAffix}}\n发抖了！", "postSummonTurboblaze": "{{pokemonNameWithAffix}}\n正在释放炽焰气场！", "postSummonTeravolt": "{{pokemonNameWithAffix}}\n正在释放溅射气场！", "postSummonDarkAura": "{{pokemonNameWithAffix}}\n正在释放暗黑气场！", "postSummonFairyAura": "{{pokemonNameWithAffix}}\n正在释放妖精气场！", "postSummonAuraBreak": "{{pokemonNameWithAffix}}\n压制了所有气场！", "postSummonNeutralizingGas": "周围充满了\n{{pokemonNameWithAffix}}的化学变化气体！", "postSummonAsOneGlastrier": "{{pokemonNameWithAffix}}\n同时拥有了两种特性！", "postSummonAsOneSpectrier": "{{pokemonNameWithAffix}}\n同时拥有了两种特性！", "postSummonVesselOfRuin": "{{pokemonNameWithAffix}}的灾祸之鼎\n令周围的宝可梦的{{statName}}减弱了！", "postSummonSwordOfRuin": "{{pokemonNameWithAffix}}的灾祸之剑\n令周围的宝可梦的{{statName}}减弱了！", "postSummonTabletsOfRuin": "{{pokemonNameWithAffix}}的灾祸之简\n令周围的宝可梦的{{statName}}减弱了！", "postSummonBeadsOfRuin": "{{pokemonNameWithAffix}}的灾祸之玉\n令周围的宝可梦的{{statName}}减弱了！", "preventBerryUse": "{{pokemonNameWithAffix}}因太紧张\n而无法食用树果！"}