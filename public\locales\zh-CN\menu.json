{"cancel": "取消", "continue": "继续", "dailyRun": "每日挑战", "runHistory": "历史记录", "loadGame": "加载游戏", "newGame": "新游戏", "settings": "设置", "selectGameMode": "选择一个游戏模式", "logInOrCreateAccount": "登录或创建账户以开始游戏。无需邮箱！", "username": "用户名", "password": "密码", "login": "登录", "loginBeta": "登录 (Beta)", "orUse": "或使用", "register": "注册", "changePassword": "更改密码", "emptyUsername": "用户名不能为空", "invalidLoginUsername": "输入的用户名无效", "invalidRegisterUsername": "用户名只能包含字母、数字或下划线", "invalidLoginPassword": "输入的密码无效", "invalidRegisterPassword": "密码必须至少包含 6 个字符", "usernameAlreadyUsed": "输入的用户名已被使用", "accountNonExistent": "输入的用户不存在", "unmatchingPassword": "输入的密码不匹配", "passwordNotMatchingConfirmPassword": "密码必须与确认密码一致", "confirmPassword": "确认密码", "registrationAgeWarning": "注册即表示您确认您已年满 13 岁。", "backToLogin": "返回登录", "changeLanguage": "变更语言", "showUsernames": "显示用户名", "failedToLoadSaveData": "读取存档数据失败。请重新加载页面。如果\n问题仍然存在，请联系管理员。", "serverCommunicationFailed": "服务器连接失败。\n游戏将重启。", "sessionSuccess": "会话加载成功。", "failedToLoadSession": "无法加载您的会话数据。它可能已损坏。", "boyOrGirl": "你是男孩还是女孩？", "evolving": "咦？\n{{pokemon<PERSON>ame}} 开始进化了！", "stoppedEvolving": "{{pokemon<PERSON>ame}} 停止了进化。", "pauseEvolutionsQuestion": "你确定要停止 {{pokemonName}} 的进化吗？\n你可以在队伍界面中重新进化。", "evolutionsPaused": "{{pokemon<PERSON>ame}} 的进化停止了。", "evolutionDone": "恭喜！\n你的 {{pokemonName}} 进化成了 {{evolvedPokemonName}}！", "dailyRankings": "每日排名", "weeklyRankings": "每周排名", "noRankings": "无排名", "positionIcon": "#", "usernameScoreboard": "用户名", "score": "分数", "wave": "层数", "loading": "加载中…", "loadingAsset": "加载资源： {{assetName}}", "playersOnline": "在线玩家", "yes": "是", "no": "否", "disclaimer": "免责声明", "disclaimerDescription": "这个游戏尚未完成; 可能存在游戏性问题（包括潜在的丢档风险）、\n 不经通知的调整、 未来可能会更新或完成更多内容", "choosePokemon": "选择一只宝可梦。", "renamePokemon": "给宝可梦起名", "renameRun": "重命名此局游戏", "rename": "起名", "renameHelpEmoji": "使用\"/\"来浏览表情库，输入数字来选择表情（例：/1是第一个表情，/6是最后一个）", "nickname": "昵称", "runName": "本局游戏名", "errorServerDown": "糟糕！访问服务器时发生了错误。\n\n你可以保持页面开启，\n游戏会自动重新连接。", "noSaves": "你没有任何记录文件！", "tooManySaves": "你的记录文件太多了！", "eventTimer": "活动截止：{{days}}天 {{hours}}时 {{mins}}分 {{secs}}秒"}