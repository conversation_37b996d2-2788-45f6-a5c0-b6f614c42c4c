{"stats": "{{username}}的状态", "playTime": "游戏时间", "totalBattles": "总战斗次数", "starters": "初始宝可梦", "shinyStarters": "闪光初始宝可梦", "speciesEncountered": "战斗的数量", "speciesSeen": "遇到的种类", "speciesCaught": "捕捉的种类", "ribbonsOwned": "拥有缎带数", "classicRuns": "经典模式次数", "classicWins": "经典模式通关次数", "dailyRunAttempts": "每日挑战次数", "dailyRunWins": "每日挑战通关次数", "endlessRuns": "无尽模式挑战次数", "highestWaveEndless": "最高层数(无尽)", "highestMoney": "最多金钱", "highestDamage": "最高伤害", "highestHPHealed": "最多治疗", "pokemonEncountered": "遇敌数量", "pokemonDefeated": "打倒数量", "pokemonCaught": "捕捉数量", "eggsHatched": "孵蛋数量", "subLegendsSeen": "遇到的二级神宝可梦", "subLegendsCaught": "捕捉的二级神宝可梦", "subLegendsHatched": "孵化的二级神宝可梦", "legendsSeen": "遇到的传说宝可梦", "legendsCaught": "捕捉的传说宝可梦", "legendsHatched": "孵化的传说宝可梦", "mythicalsSeen": "遇到的幻兽宝可梦", "mythicalsCaught": "捕捉的幻兽宝可梦", "mythicalsHatched": "孵化的幻兽宝可梦", "shiniesSeen": "遇到的闪光宝可梦", "shiniesCaught": "捕捉的闪光宝可梦", "shiniesHatched": "孵化的闪光宝可梦", "pokemonFused": "融合宝可梦次数", "trainersDefeated": "打败的训练师数", "eggsPulled": "总扭蛋次数", "rareEggsPulled": "稀有扭蛋数", "epicEggsPulled": "史诗扭蛋数", "legendaryEggsPulled": "传说扭蛋数", "manaphyEggsPulled": "玛娜霏扭蛋数"}