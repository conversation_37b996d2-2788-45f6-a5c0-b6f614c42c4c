{"yourTeam": "我方隊伍", "opposingTeam": "敵方隊伍", "arenaOnRemove": "{{moveName}}的效果消失了！", "arenaOnRemovePlayer": "{{moveName}}在我方的效果消失了！", "arenaOnRemoveEnemy": "{{moveName}}在敵方的效果消失了！", "mistOnAdd": "{{pokemonNameWithAffix}}的一方被\n白霧包圍了！", "mistApply": "正受到白霧的保護\n能力不會被降低！", "reflectOnAdd": "反射壁使\n物理抗性提高了！", "reflectOnAddPlayer": "反射壁使我方的\n物理抗性提高了！", "reflectOnAddEnemy": "反射壁使敵方的\n物理抗性提高了！", "lightScreenOnAdd": "光牆使\n特殊抗性提高了！", "lightScreenOnAddPlayer": "光牆使我方的\n特殊抗性提高了！", "lightScreenOnAddEnemy": "光牆使敵方的\n特殊抗性提高了！", "auroraVeilOnAdd": "極光幕使\n物理和特殊抗性提高了！", "auroraVeilOnAddPlayer": "極光幕使我方的\n物理和特殊抗性提高了！", "auroraVeilOnAddEnemy": "極光幕使敵方的\n物理和特殊抗性提高了！", "conditionalProtectOnAdd": "{{moveName}}\n保護了！", "conditionalProtectOnAddPlayer": "{{moveName}}\n保護了我方！", "conditionalProtectOnAddEnemy": "{{moveName}}\n保護了敵方！", "conditionalProtectApply": "{{moveName}}\n保護了{{pokemonNameWithAffix}}！", "matBlockOnAdd": "{{pokemonNameWithAffix}}正在\n伺機使出掀榻榻米！", "noCritOnAddPlayer": "{{moveName}}保護了你的\n隊伍不被擊中要害！", "noCritOnAddEnemy": "{{moveName}}保護了對方的\n隊伍不被擊中要害！", "noCritOnRemove": "{{pokemonNameWithAffix}}的{{moveName}}\n效果消失了！", "wishTagOnAdd": "{{pokemonNameWithAffix}}的\n祈願實現了！", "mudSportOnAdd": "電氣的威力減弱了！", "mudSportOnRemove": "玩泥巴的效果消失了！", "waterSportOnAdd": "火焰的威力減弱了！", "waterSportOnRemove": "玩水的效果消失了！", "plasmaFistsOnAdd": "等離子雨傾盆而下！", "spikesOnAdd": "{{opponentDesc}}腳下\n散落著{{moveName}}！", "spikesActivateTrap": "{{pokemonNameWithAffix}}\n受到了撒菱的傷害！", "toxicSpikesOnAdd": "{{opponentDesc}}腳下\n散落著{{moveName}}！", "toxicSpikesActivateTrapPoison": "{{pokemonNameWithAffix}}\n吸收了{{moveName}}！", "stealthRockOnAdd": "{{opponentDesc}}周圍\n開始浮現出尖銳的岩石！", "stealthRockActivateTrap": "尖銳的岩石紮進了\n{{pokemonNameWithAffix}}的體內！", "stickyWebActivateTrap": "{{pokemon<PERSON>ame}}\n被黏黏網粘住了！", "trickRoomOnAdd": "{{pokemonNameWithAffix}}\n扭曲了時空！", "trickRoomOnRemove": "扭曲的時空複原了！", "gravityOnAdd": "重力變強了！", "gravityOnRemove": "重力複原了！", "tailwindOnAdd": "從身後\n吹起了順風！", "tailwindOnAddPlayer": "從我方身後\n吹起了順風！", "tailwindOnAddEnemy": "從敵方身後\n吹起了順風！", "tailwindOnRemove": "順風停止了！", "tailwindOnRemovePlayer": "我方的順風停止了！", "tailwindOnRemoveEnemy": "敵方的順風停止了！", "happyHourOnAdd": "大家被歡樂的\n氣氛包圍了！", "happyHourOnRemove": "氣氛回複到平常了。", "safeguardOnAdd": "整個場地被\n神秘之幕包圍了！", "safeguardOnAddPlayer": "我方被\n神秘之幕包圍了！", "safeguardOnAddEnemy": "對手被\n神秘之幕包圍了！", "safeguardOnRemove": "包圍整個場地的\n神秘之幕消失了！", "safeguardOnRemovePlayer": "包圍我方的\n神秘之幕消失了！", "safeguardOnRemoveEnemy": "包圍對手的\n神秘之幕消失了！", "fairyLockOnAdd": "下回合無法逃走！", "neutralizingGasOnAdd": "周圍充滿了化學變化氣體！", "neutralizingGasOnRemove": "化學變化氣體的效果消失了！"}