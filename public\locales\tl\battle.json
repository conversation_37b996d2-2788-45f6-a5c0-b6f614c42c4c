{"bossAppeared": "Lu<PERSON><PERSON> si {{boss<PERSON>ame}}!", "trainerAppeared": "Nais ma<PERSON>\nni {{trainerName}}!", "trainerAppearedDouble": "Nais ma<PERSON>\nni {{trainerName}}!", "trainerSendOut": "Ipinatawag ni {{trainerName}}\nsi {{pokemonName}}!", "singleWildAppeared": "Isang ligaw na {{pokemonName}}\nang lumabas!", "multiWildAppeared": "Isang ligaw na {{pokemonName1}}\nat {{pokemonName2}} ang lumabas!", "playerComeBack": "Sige na, {{pokemonName}}!", "trainerComeBack": "Nag-withdraw si {{trainerName}}\nng {{pokemonName}}!", "playerGo": "Tara na!\n{{pokemon<PERSON>ame}}!", "trainerGo": "Ipinatawag ni {{trainerName}} si {{pokemonName}}!", "pokemonDraggedOut": "Napilitang lumabas si {{pokemonName}}!", "switchQuestion": "Papalitan mo ba si\n{{pokemonName}}?", "trainerDefeated": "<PERSON>o mo si\n{{<PERSON><PERSON><PERSON>}}!", "moneyWon": "Na<PERSON><PERSON><PERSON> ka ng\n₽{{moneyAmount}} sa panalo mo!", "moneyPickedUp": "<PERSON><PERSON><PERSON><PERSON> ka ng ₽{{moneyAmount}}!", "pokemonCaught": "Nahuli si {{pokemon<PERSON>ame}}!", "pokemonObtained": "<PERSON><PERSON><PERSON> mo si {{pokemonName}}!", "pokemonBrokeFree": "Ay!\nKumawala ang Pokémon!", "pokemonFled": "Nakatakas ang ligaw\nna {{pokemonName}}!", "playerFled": "Umat<PERSON> ka kay {{pokemonName}}!", "addedAsAStarter": "Naging panimula\nsi {{pokemonName}}!", "partyFull": "Puno ang party.\nIlabas ang Pokémon para kay {{pokemonName}}?", "pokemon": "Pokémon", "sendOutPokemon": "Tara na!\n{{pokemon<PERSON>ame}}!", "hitResultCriticalHit": "Isang critical hit!", "hitResultSuperEffective": "Sobrang epektibo!", "hitResultNotVeryEffective": "Hindi masyadong epektibo...", "hitResultNoEffect": "Walang epekto kay {{pokemonName}}!", "hitResultImmune": "Hindi apektado si {{pokemonName}}!", "hitResultOneHitKO": "<PERSON><PERSON> hit KO!", "attackFailed": "Pero palpak!", "attackMissed": "<PERSON><PERSON><PERSON><PERSON> ni {{pokemonNameWithAffix}}\nang atake!", "attackHitsCount": "Hit nang {{count}} beses!", "rewardGain": "Naka<PERSON><PERSON> ka ng\n{{modifierName}}!", "rewardGainCount": "<PERSON><PERSON><PERSON><PERSON> ka ng\n{{count}} {{modifierName}}!", "expGain": "<PERSON><PERSON><PERSON><PERSON> si {{pokemonName}}\nng {{exp}} EXP. Points!", "levelUp": "Umakyat sa Lv. {{level}}\nsi {{pokemonName}}!", "learnMove": "<PERSON><PERSON> si {{pokemonName}}\nng {{moveName}}!", "learnMovePrompt": "<PERSON><PERSON> ni {{pokemonName}} matuto\nng {{moveName}}.", "learnMoveLimitReached": "<PERSON><PERSON>, may apat nang galaw\nsi {{pokemonName}}.", "learnMoveReplaceQuestion": "<PERSON>to mo bang kalimutan ang isang galaw\nkapalit ng {{moveName}}?", "learnMoveStopTeaching": "Itigil na ang pagtuturo ng \n{{moveName}}?", "learnMoveNotLearned": "Hindi natuto si {{pokemonName}}\nng {{moveName}}.", "learnMoveForgetQuestion": "Aling galaw ang dapat kalimutan?", "learnMoveForgetSuccess": "Na<PERSON><PERSON><PERSON>n ni {{pokemonName}} \nkung paano gamitin ang {{moveName}}.", "countdownPoof": "@d{32}1, @d{15}2, and@d{15}… @d{15}… @d{15}… @d{15}@s{se/pb_bounce_1}Poof!", "learnMoveAnd": "At...", "levelCapUp": "Tumaas ang level cap\nhanggang sa antas {{levelCap}}!", "moveNotImplemented": "HIndi pa nailalagay ang {{moveName}\nat hindi ito maaaring piliin.", "moveNoPP": "Wala nang PP para sa\ngalaw na ito!", "moveDisabled": "Naka-diseybol ang {{moveName}}!", "moveDisabledTorment": "Hindi maaaring gumamit si {{pokemonNameWithAffix}} ng parehong galaw nang dalawang beses sunod-sunod dahil sa Torment!", "moveDisabledTaunt": "Hindi maaaring gumamit si {{pokemonNameWithAffix}}\nng {{moveName}} matapos ang taunt!", "moveDisabledHealBlock": "Hindi kayang gumamit si {{pokemonNameWithAffix}}\nng {{moveName}} dahil sa {{healBlockName}}!", "moveDisabledImprison": "Hindi magamit ni {{pokemonNameWithAffix}}\nang naka-selyo nitong {{moveName}}!", "canOnlyUseMove": "Si {{pokemonName}} ay {{moveName}} lang ang kayang gamitin!", "moveCannotBeSelected": "Hindi maaaring piliin\nang {{moveName}}!", "disableInterruptedMove": "Naka-diseybol ang {{moveName}} ni {{pokemonNameWithAffix}}!", "throatChopInterruptedMove": "<PERSON><PERSON> sa <PERSON>rot Tsap, hindi makagamit\nng ilang galaw si {{pokemonName}}!", "noPokeballForce": "May pumipigil na lihim na kapangy<PERSON>han\nsa paggamit ng Poké Ball.", "noPokeballTrainer": "Hoy! Hindi mo puwedent hulihin ang Pokémon\nng ibang trenyer!", "noPokeballMulti": "Makakapag-hagis ka lang ng Poké Ball\nkapag isa na lang ang Pokémon sa laban!", "noPokeballStrong": "Masyadong malakas ang target na Pokémon para hulihin!\nKailangan mo itong pahinain muna!", "noPokeballMysteryEncounter": "Hindi mo kayang hulihin \nang Pokémon na ito!", "noEscapeForce": "May pumipigil na lihim \nna kapangy<PERSON>han sa pagtakas.", "noEscapeTrainer": "Hindi ka makakatakas \nsa laban ng trenyer!", "noEscapePokemon": "<PERSON><PERSON> sa {{moveName}} ni {{pokemonName}}, hindi ka na {{escapeVerb}}!", "noEscapeSwitch": "Hindi maaaring palitan ang Pokémon!", "noEscapeFlee": "Hindi na makakatakas ang Pokémon!", "runAwaySuccess": "Nakatakas ka nang ligtas!", "runAwayCannotEscape": "Hindi ka makakatakas!", "escapeVerbSwitch": "palitan", "escapeVerbFlee": "ma<PERSON><PERSON><PERSON>", "notDisabled": "Disebyol na ang {{pokemonName}} \nni {{moveName}}! Wala na!", "turnEndHpRestore": "Na<PERSON>lik ang HP ni {{pokemonName}}!", "hpIsFull": "Puno na ang \nHP ni {{pokemonName}}!", "skipItemQuestion": "<PERSON><PERSON><PERSON> ka bang gusto mong laktawan ang item?", "itemStackFull": "Puno na ang stack ng {{fullItemName}}.\n{{itemName}} ang matatanggap mo.", "eggHatching": "O?", "eggSkipPrompt": "May {{eggsToHatch}} na Itlog na handa nang mapisa.\nLaktawan papunta sa buod ng Itlog?", "ivScannerUseQuestion": "Gamitin ang IV Scanner kay {{pokemonName}}?", "wildPokemonWithAffix": "ligaw na {{pokemonName}}", "foePokemonWithAffix": "kalaban {{pokemon<PERSON>ame}}", "useMove": "Ginamit ni {{pokemonNameWithAffix}}\nang {{moveName}}!", "magicCoatActivated": "Binalik ni {{pokemonNameWithAffix}}\nang {{moveName}}!", "drainMessage": "Nahigop ang enerhiya\nni {{pokemonName}}!", "regainHealth": "Bumalik ang HP\nni {{pokemonName}}!", "stealEatBerry": "<PERSON><PERSON> at kinain ni {{pokemonName}}\nang {{berryName}} ni {{targetName}}!", "ppHealBerry": "Isinauli ni {{pokemonNameWithAffix}} ang PP ng {{moveName}} gamit ang {{berryName}} nito!", "hpHealBerry": "Ibinalik ni {{pokemonNameWithAffix}\nang HP nito gamit ang {{berryName}} nito!", "fainted": "<PERSON><PERSON><PERSON> si {{pokemonNameWithAffix}}!", "statsAnd": "at", "stats": "Stats", "statRose_one": "<PERSON><PERSON><PERSON> ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statRose_other": "<PERSON><PERSON><PERSON> ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statSharplyRose_one": "<PERSON><PERSON><PERSON> nang matindi ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statSharplyRose_other": "<PERSON><PERSON><PERSON> nang matindi ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statRoseDrastically_one": "<PERSON><PERSON><PERSON> nang malaki ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statRoseDrastically_other": "<PERSON><PERSON><PERSON> nang malaki ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statWontGoAnyHigher_one": "Hindi na tataas ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statWontGoAnyHigher_other": "Hindi na tataas ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statFell_one": "<PERSON><PERSON>ba ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statFell_other": "<PERSON><PERSON>ba ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statHarshlyFell_one": "<PERSON><PERSON><PERSON> nang matindi ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statHarshlyFell_other": "<PERSON><PERSON><PERSON> nang matindi ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statSeverelyFell_one": "<PERSON><PERSON><PERSON> nang lubha and {{stats}}\nni {{pokemonNameWithAffix}}!", "statSeverelyFell_other": "<PERSON><PERSON><PERSON> nang lubha and {{stats}}\nni {{pokemonNameWithAffix}}!", "statWontGoAnyLower_one": "Hindi na bababa ang {{stats}}\nni {{pokemonNameWithAffix}}!", "statWontGoAnyLower_other": "Hindi na bababa ang {{stats}}\nni {{pokemonNameWithAffix}}!", "transformedIntoType": "Nagbago si {{pokemonName}}\nat naging {{type}} na type!", "retryBattle": "Gusto mo bang subukang muli mula sa simula ng laban?", "unlockedSomething": "Na-unlock na ang \n{{unlockedThing}}.", "congratulations": "Magaling!", "beatModeFirstTime": "<PERSON><PERSON> ni {{speciesName}} ang {{gameMode}} Mode sa unang pagkakataon! Nakakuha ka ng {{newModifier}}!", "ppReduced": "<PERSON><PERSON><PERSON>an ng {{reduction}} ang PP ng {{moveName}}\nni {{targetName}}!", "mysteryEncounterAppeared": "Ano ’to?", "battlerTagsHealBlock": "Hindi maibalik ni {{pokemonNameWithAffix}}\nang HP nito!", "battlerTagsHealBlockOnRemove": "Naibabalik na ni {{pokemonNameWithAffix}}\nang HP nito!", "pokemonTerastallized": "Nag-terakristalisado si {{pokemonNameWithAffix}}\nat naging {{type}} na type!"}