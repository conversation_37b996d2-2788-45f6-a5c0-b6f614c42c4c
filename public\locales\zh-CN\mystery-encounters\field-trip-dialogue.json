{"intro": "是一位老师和几个学生！", "speaker": "人民教师", "introDialogue": "你好！你能为我的学生们抽出一点时间吗？\n我正在教他们宝可梦的招式！\n最好能有个示范来展示一下。$你介意向我们展示一下\n你的宝可梦所使用的招式吗？", "title": "外出实践", "description": "一位老师请求进行宝可梦招式演示。\n根据你选择的招式，她可能会给你\n一些有用的东西作为报酬。", "query": "你要展示哪种类型的招式？", "option": {"1": {"label": "物理招式", "tooltip": "(+)物理类奖励"}, "2": {"label": "特殊招式", "tooltip": "(+)特殊类奖励"}, "3": {"label": "变化招式", "tooltip": "(+)变化类奖励"}, "selected": "{{pokeName}}完美地展示了{{move}}！"}, "secondOptionPrompt": "选择一个宝可梦的招式。", "incorrect": "…$那好像不是{{moveCategory}}招式！\n对不起，我不打算给你任何东西了。$来吧孩子们，我们去别的地方\n找找更好的示范。", "incorrectExp": "看来你学到了宝贵的一课？$你的宝可梦也获得了一些经验。", "correct": "感谢您的好心！\n这些东西希望对你有用！", "correctExp": "{{poke<PERSON>ame}}同样也获得了一些经验！", "status": "变化类", "physical": "物理类", "special": "特殊类"}