{"bulbasaurCategory": "Tohum Pokémon", "ivysaurCategory": "Tohum Pokémon", "venusaurCategory": "Tohum Pokémon", "charmanderCategory": "Kertenkele Pokémon", "charmeleonCategory": "Alev Pokémon", "charizardCategory": "Alev Pokémon", "squirtleCategory": "Minik <PERSON>ğa Pokémon", "wartortleCategory": "Kaplumbağa Pokémon", "blastoiseCategory": "Kabuklu Pokémon", "caterpieCategory": "Tırtıl Pokémon", "metapodCategory": "Koza Pokémon", "butterfreeCategory": "Kelebek Pokémon", "weedleCategory": "Kıllı Böcek Pokémon", "kakunaCategory": "Koza Pokémon", "beedrillCategory": "Zehirli Arı Pokémon", "pidgeyCategory": "Minik Ku<PERSON>", "pidgeottoCategory": "Kuş Pokémon", "pidgeotCategory": "Kuş Pokémon", "rattataCategory": "Fare Pokémon", "raticateCategory": "Fare Pokémon", "spearowCategory": "Minik Ku<PERSON>", "fearowCategory": "Gagalı Pokémon", "ekansCategory": "Yılan Pokémon", "arbokCategory": "Kobra Pokémon", "pikachuCategory": "Fare Pokémon", "raichuCategory": "Fare Pokémon", "sandshrewCategory": "Fare Pokémon", "sandslashCategory": "Fare Pokémon", "nidoranFCategory": "Zehirli İğneli Pokémon", "nidorinaCategory": "Zehirli İğneli Pokémon", "nidoqueenCategory": "Delici Pokémon", "nidoranMCategory": "Zehirli İğneli Pokémon", "nidorinoCategory": "Zehirli İğneli Pokémon", "nidokingCategory": "Delici Pokémon", "clefairyCategory": "Peri Pokémon", "clefableCategory": "Peri Pokémon", "vulpixCategory": "Tilki Pokémon", "ninetalesCategory": "Tilki Pokémon", "jigglypuffCategory": "Balon Pokémon", "wigglytuffCategory": "Balon Pokémon", "zubatCategory": "Yarasa Pokémon", "golbatCategory": "Yarasa Pokémon", "oddishCategory": "Ot Pokémon", "gloomCategory": "Ot Pokémon", "vileplumeCategory": "Çiçek Pokémon", "parasCategory": "Mantar Pokémon", "parasectCategory": "Mantar Pokémon", "venonatCategory": "Böcek Pokémon", "venomothCategory": "Toksik Güve Pokémon", "diglettCategory": "Köstebek Pokémon", "dugtrioCategory": "Köstebek Pokémon", "meowthCategory": "Tırmalayıcı Kedi Pokémon", "persianCategory": "<PERSON><PERSON>", "psyduckCategory": "Ördek Pokémon", "golduckCategory": "Ördek Pokémon", "mankeyCategory": "<PERSON><PERSON>", "primeapeCategory": "<PERSON><PERSON>", "growlitheCategory": "Köpek Yavrusu Pokémon", "arcanineCategory": "Efsanevi Pokémon", "poliwagCategory": "İribaş Pokémon", "poliwhirlCategory": "İribaş Pokémon", "poliwrathCategory": "İribaş Pokémon", "abraCategory": "Psişik Pokémon", "kadabraCategory": "Psişik Pokémon", "alakazamCategory": "Psişik Pokémon", "machopCategory": "<PERSON><PERSON><PERSON> Güçlü Pokémon", "machokeCategory": "<PERSON><PERSON><PERSON> Güçlü Pokémon", "machampCategory": "<PERSON><PERSON><PERSON> Güçlü Pokémon", "bellsproutCategory": "Çiçek Pokémon", "weepinbellCategory": "Sinekkapan Pokémon", "victreebelCategory": "Sinekkapan Pokémon", "tentacoolCategory": "Denizanası Pokémon", "tentacruelCategory": "Denizanası Pokémon", "geodudeCategory": "Kaya Pokémon", "gravelerCategory": "Kaya Pokémon", "golemCategory": "Megaton Pokémon", "ponytaCategory": "Ateş Atı Pokémon", "rapidashCategory": "Ateş Atı Pokémon", "slowpokeCategory": "Uyuşuk Pokémon", "slowbroCategory": "Pavurya Pokémon", "magnemiteCategory": "Mıknatıs Pokémon", "magnetonCategory": "Mıknatıs Pokémon", "farfetchdCategory": "<PERSON>ban Ö<PERSON>ği Pokémon", "doduoCategory": "İkiz <PERSON>", "dodrioCategory": "Üçüz Kuş Pokémon", "seelCategory": "<PERSON><PERSON>", "dewgongCategory": "<PERSON><PERSON>", "grimerCategory": "Balçık Pokémon", "mukCategory": "Balçık Pokémon", "shellderCategory": "Çift Kabuklu Pokémon", "cloysterCategory": "Çift Kabuklu Pokémon", "gastlyCategory": "Gaz Pokémon", "haunterCategory": "Gaz Pokémon", "gengarCategory": "<PERSON><PERSON><PERSON>", "onixCategory": "<PERSON><PERSON>", "drowzeeCategory": "Hipnoz Pokémon", "hypnoCategory": "Hipnoz Pokémon", "krabbyCategory": "<PERSON><PERSON><PERSON>", "kinglerCategory": "Kıskaçlı Pokémon", "voltorbCategory": "Top Pokémon", "electrodeCategory": "Top Pokémon", "exeggcuteCategory": "Yumurta Pokémon", "exeggutorCategory": "Hindistan Cevizi Pokémon", "cuboneCategory": "Yalnız Pokémon", "marowakCategory": "Kemik Toplayıcısı Pokémon", "hitmonleeCategory": "Tekmeci Pokémon", "hitmonchanCategory": "Yumrukçu Pokémon", "lickitungCategory": "Yalayan Pokémon", "koffingCategory": "Zehirli Gaz Pokémon", "weezingCategory": "Zehirli Gaz Pokémon", "rhyhornCategory": "Boynuzlu Pokémon", "rhydonCategory": "Delici Pokémon", "chanseyCategory": "Yumurta Pokémon", "tangelaCategory": "Sarmaşık Pokémon", "kangaskhanCategory": "Ebeveyn Pokémon", "horseaCategory": "Ejderha Pokémon", "seadraCategory": "Ejderha Pokémon", "goldeenCategory": "Japon Balığı Pokémon", "seakingCategory": "Japon Balığı Pokémon", "staryuCategory": "Yıldız Biçimli Pokémon", "starmieCategory": "Gizemli Pokémon", "mrMimeCategory": "Bariyer Pokémon", "scytherCategory": "Peygamber Devesi Pokémon", "jynxCategory": "<PERSON>nsan <PERSON>", "electabuzzCategory": "Elektrik Pokémon", "magmarCategory": "Ateş Tükürücü Pokémon", "pinsirCategory": "Makaslı Böcek Pokémon", "taurosCategory": "Yaban Öküzü Pokémon", "magikarpCategory": "Balık Pokémon", "gyaradosCategory": "Öfkeli Pokémon", "laprasCategory": "Ulaştırmacı Pokémon", "dittoCategory": "Dönüşen Pokémon", "eeveeCategory": "Evrim Geçiren Pokémon", "vaporeonCategory": "Baloncuk Jetli Pokémon", "jolteonCategory": "Şimşek Pokémon", "flareonCategory": "Alev Pokémon", "porygonCategory": "Sanal Pokémon", "omanyteCategory": "Spiral Pokémon", "omastarCategory": "Spiral Pokémon", "kabutoCategory": "Kabuklu Pokémon", "kabutopsCategory": "Kabuklu Pokémon", "aerodactylCategory": "Fosil Pokémon", "snorlaxCategory": "Uykucu Pokémon", "articunoCategory": "Dondurucu Pokémon", "zapdosCategory": "Elektrik Pokémon", "moltresCategory": "Alev Pokémon", "dratiniCategory": "Ejderha Pokémon", "dragonairCategory": "Ejderha Pokémon", "dragoniteCategory": "Ejderha Pokémon", "mewtwoCategory": "Genetik Pokémon", "mewCategory": "<PERSON><PERSON>", "chikoritaCategory": "Yaprak Pokémon", "bayleefCategory": "Yaprak Pokémon", "meganiumCategory": "Bitki Pokémon", "cyndaquilCategory": "Ateş <PERSON>esi <PERSON>", "quilavaCategory": "Volkan Pokémon", "typhlosionCategory": "Volkan Pokémon", "totodileCategory": "Koca <PERSON>li Pokémon", "croconawCategory": "Koca <PERSON>li Pokémon", "feraligatrCategory": "Koca <PERSON>li Pokémon", "sentretCategory": "İzci Pokémon", "furretCategory": "Uzun <PERSON>li <PERSON>", "hoothootCategory": "Baykuş Pokémon", "noctowlCategory": "Baykuş Pokémon", "ledybaCategory": "Beş Yıldızlı Pokémon", "ledianCategory": "Beş Yıldızlı Pokémon", "spinarakCategory": "<PERSON><PERSON>", "ariadosCategory": "Uzun Bacaklı Pokémon", "crobatCategory": "Yarasa Pokémon", "chinchouCategory": "<PERSON><PERSON>", "lanturnCategory": "Işık Pokémon", "pichuCategory": "Minik Fare Pokémon", "cleffaCategory": "Yıldız Biçimli Pokémon", "igglybuffCategory": "Balon Pokémon", "togepiCategory": "İğneli Top Pokémon", "togeticCategory": "Mutluluk Pokémon", "natuCategory": "Minik Ku<PERSON>", "xatuCategory": "Mistik Pokémon", "mareepCategory": "<PERSON>ün <PERSON>", "flaaffyCategory": "<PERSON>ün <PERSON>", "ampharosCategory": "Işık Pokémon", "bellossomCategory": "Çiçek Pokémon", "marillCategory": "Su Faresi Pokémon", "azumarillCategory": "Su Tavşanı Pokémon", "sudowoodoCategory": "Taklitçi Pokémon", "politoedCategory": "Kurbağa Pokémon", "hoppipCategory": "<PERSON>uk <PERSON>", "skiploomCategory": "<PERSON>uk <PERSON>", "jumpluffCategory": "<PERSON>uk <PERSON>", "aipomCategory": "Uzun Ku<PERSON>uk<PERSON> Pokémon", "sunkernCategory": "Tohum Pokémon", "sunfloraCategory": "Güneş Pokémon", "yanmaCategory": "<PERSON>", "wooperCategory": "Tatlı Su Balığı Pokémon", "quagsireCategory": "Tatlı Su Balığı Pokémon", "espeonCategory": "Güneş Pokémon", "umbreonCategory": "<PERSON><PERSON>", "murkrowCategory": "Gececi Pokémon", "slowkingCategory": "Asil <PERSON>", "misdreavusCategory": "Çığlıkçı Pokémon", "unownCategory": "Sembol Pokémon", "wobbuffetCategory": "Sabırlı Pokémon", "girafarigCategory": "Uzun <PERSON>lu Pokémon", "pinecoCategory": "Torbalı Güve Pokémon", "forretressCategory": "Torbalı Güve Pokémon", "dunsparceCategory": "Kara Yılanı Pokémon", "gligarCategory": "Uçan Akrep Pokémon", "steelixCategory": "<PERSON><PERSON>", "snubbullCategory": "Peri Pokémon", "granbullCategory": "Peri Pokémon", "qwilfishCategory": "Balon Pokémon", "scizorCategory": "Kıskaçlı Pokémon", "shuckleCategory": "<PERSON><PERSON><PERSON>", "heracrossCategory": "Tek Boynuzlu Pokémon", "sneaselCategory": "<PERSON><PERSON>", "teddiursaCategory": "Minik Ayı Pokémon", "ursaringCategory": "Kış Uykusuna Yatan Pokémon", "slugmaCategory": "Lav Pokémon", "magcargoCategory": "Lav Pokémon", "swinubCategory": "<PERSON><PERSON><PERSON><PERSON>", "piloswineCategory": "Domuz Pokémon", "corsolaCategory": "Mercan Pokémon", "remoraidCategory": "Jet Pokémon", "octilleryCategory": "Jet Pokémon", "delibirdCategory": "Teslimatçı Pokémon", "mantineCategory": "Uçurtma Pokémon", "skarmoryCategory": "Zırhlı Kuş Pokémon", "houndourCategory": "Karanlık Pokémon", "houndoomCategory": "Karanlık Pokémon", "kingdraCategory": "Ejderha Pokémon", "phanpyCategory": "Uzun Burunlu Pokémon", "donphanCategory": "Zırh Pokémon", "porygon2Category": "Sanal Pokémon", "stantlerCategory": "<PERSON><PERSON>", "smeargleCategory": "Ressam Pokémon", "tyrogueCategory": "Kavgacı Pokémon", "hitmontopCategory": "Amutçu Pokémon", "smoochumCategory": "Öpücük Pokémon", "elekidCategory": "Elektrik Pokémon", "magbyCategory": "Canlı Kömür Pokémon", "miltankCategory": "Süt İneği Pokémon", "blisseyCategory": "Mutluluk Pokémon", "raikouCategory": "Gök Gürültüsü Pokémon", "enteiCategory": "Volkan Pokémon", "suicuneCategory": "Aurora Pokémon", "larvitarCategory": "<PERSON><PERSON>", "pupitarCategory": "Sert Kabuklu Pokémon", "tyranitarCategory": "Zırh Pokémon", "lugiaCategory": "Dalgıç Pokémon", "hoOhCategory": "Gökkuşağı Pokémon", "celebiCategory": "Zaman Yolcusu Pokémon", "treeckoCategory": "Ağaç Kertenkelesi Pokémon", "grovyleCategory": "Ağaç Kertenkelesi Pokémon", "sceptileCategory": "Orman Pokémon", "torchicCategory": "Civciv Pokémon", "combuskenCategory": "<PERSON><PERSON>ç <PERSON>", "blazikenCategory": "Yanıcı Pokémon", "mudkipCategory": "<PERSON><PERSON>ur <PERSON>", "marshtompCategory": "<PERSON><PERSON>ur <PERSON>", "swampertCategory": "<PERSON><PERSON>ur <PERSON>", "poochyenaCategory": "<PERSON><PERSON>ran <PERSON>", "mightyenaCategory": "<PERSON><PERSON>ran <PERSON>", "zigzagoonCategory": "Minik Rakun Pokémon", "linooneCategory": "Aceleci Pokémon", "wurmpleCategory": "Tırtıl Pokémon", "silcoonCategory": "Koza Pokémon", "beautiflyCategory": "Kelebek Pokémon", "cascoonCategory": "Koza Pokémon", "dustoxCategory": "Toksik Güve Pokémon", "lotadCategory": "<PERSON>tu Pokémon", "lombreCategory": "Keyifli Pokémon", "ludicoloCategory": "Tasasız Pokémon", "seedotCategory": "Palamut Pokémon", "nuzleafCategory": "Kurnaz Pokémon", "shiftryCategory": "Kötü Pokémon", "taillowCategory": "Minik Kırlangıç Pokémon", "swellowCategory": "Kırlangıç Pokémon", "wingullCategory": "Martı Pokémon", "pelipperCategory": "Su Kuşu Pokémon", "raltsCategory": "Hisli Pokémon", "kirliaCategory": "Duygu Pokémon", "gardevoirCategory": "Kucaklayan Pokémon", "surskitCategory": "Gölet Kayakçısı Pokémon", "masquerainCategory": "Göz Bebeği Pokémon", "shroomishCategory": "Mantar Pokémon", "breloomCategory": "Mantar Pokémon", "slakothCategory": "Miskin Pokémon", "vigorothCategory": "Yabani Maymun Pokémon", "slakingCategory": "Tembel Pokémon", "nincadaCategory": "Öğrenci Pokémon", "ninjaskCategory": "Ninja Pokémon", "shedinjaCategory": "Kabuk Böceği Pokémon", "whismurCategory": "Fısıltı Pokémon", "loudredCategory": "Koca Sesli Pokémon", "exploudCategory": "Y<PERSON>ksek <PERSON>li <PERSON>", "makuhitaCategory": "Cesaretli Pokémon", "hariyamaCategory": "<PERSON><PERSON>", "azurillCategory": "Benekli Pokémon", "nosepassCategory": "Pusula Pokémon", "skittyCategory": "<PERSON><PERSON><PERSON>", "delcattyCategory": "Ciddi Pokémon", "sableyeCategory": "Gececi Pokémon", "mawileCategory": "Kandırıcı Pokémon", "aronCategory": "<PERSON><PERSON>", "laironCategory": "<PERSON><PERSON>", "aggronCategory": "<PERSON><PERSON>", "medititeCategory": "Meditasyon Pokémon", "medichamCategory": "Meditasyon Pokémon", "electrikeCategory": "Şimşek Pokémon", "manectricCategory": "Elektrik Boşalımlı Pokémon", "plusleCategory": "Neşeli Pokémon", "minunCategory": "Neşeli Pokémon", "volbeatCategory": "Ateş Böceği Pokémon", "illumiseCategory": "Ateş Böceği Pokémon", "roseliaCategory": "Dikenli Pokémon", "gulpinCategory": "Mide Pokémon", "swalotCategory": "<PERSON><PERSON><PERSON>", "carvanhaCategory": "Gaddar Pokémon", "sharpedoCategory": "Zalim Pokémon", "wailmerCategory": "Top Balinası Pokémon", "wailordCategory": "Düz Balina Pokémon", "numelCategory": "Hissiz Pokémon", "cameruptCategory": "Volkanik Patlayan Pokémon", "torkoalCategory": "Kömür Pokémon", "spoinkCategory": "Seken Pokémon", "grumpigCategory": "Manipülatör Pokémon", "spindaCategory": "Benekli <PERSON>", "trapinchCategory": "<PERSON><PERSON><PERSON> Ka<PERSON>ıncası Pokémon", "vibravaCategory": "Titreşen Pokémon", "flygonCategory": "Mistik Pokémon", "cacneaCategory": "Kaktüs Pokémon", "cacturneCategory": "Korkuluk Pokémon", "swabluCategory": "<PERSON>uk <PERSON>", "altariaCategory": "Mırıldanan Pokémon", "zangooseCategory": "<PERSON><PERSON>", "seviperCategory": "Dişli <PERSON>ılan Pokémon", "lunatoneCategory": "Meteorit Pokémon", "solrockCategory": "Meteorit Pokémon", "barboachCategory": "Bıyıklı Pokémon", "whiscashCategory": "Bıyıklı Pokémon", "corphishCategory": "Kabadayı Pokémon", "crawdauntCategory": "Düzenbaz Pokémon", "baltoyCategory": "Kil Bebek Pokémon", "claydolCategory": "Kil Bebek Pokémon", "lileepCategory": "<PERSON><PERSON>", "cradilyCategory": "Midye Pokémon", "anorithCategory": "Yaşlı Karides Pokémon", "armaldoCategory": "Levha Pokémon", "feebasCategory": "Balık Pokémon", "miloticCategory": "Yumuşakça Pokémon", "castformCategory": "<PERSON><PERSON>", "kecleonCategory": "Renk Değiştiren Pokémon", "shuppetCategory": "Kukla Pokémon", "banetteCategory": "Kara Kukla Pokémon", "duskullCategory": "Ayin <PERSON>", "dusclopsCategory": "İşaretçi Pokémon", "tropiusCategory": "Meyve Pokémon", "chimechoCategory": "Rüzg<PERSON>r <PERSON>", "absolCategory": "Felaket Tellalı Pokémon", "wynautCategory": "Işıltılı Pokémon", "snoruntCategory": "<PERSON><PERSON>", "glalieCategory": "Yüz Pokémon", "sphealCategory": "Alkış Pokémon", "sealeoCategory": "Top Yuvarlayıcı Pokémon", "walreinCategory": "Buz Kırıcı Pokémon", "clamperlCategory": "Çift Kabuklu Pokémon", "huntailCategory": "<PERSON><PERSON>", "gorebyssCategory": "<PERSON><PERSON><PERSON>", "relicanthCategory": "Uzun Ömürlü Pokémon", "luvdiscCategory": "Randevu Pokémon", "bagonCategory": "<PERSON><PERSON>", "shelgonCategory": "Dayanıklı Pokémon", "salamenceCategory": "Ejderha Pokémon", "beldumCategory": "<PERSON><PERSON>", "metangCategory": "<PERSON><PERSON>", "metagrossCategory": "<PERSON><PERSON>", "regirockCategory": "<PERSON><PERSON>i <PERSON>", "regiceCategory": "Buz Dağı Pokémon", "registeelCategory": "Demir <PERSON>", "latiasCategory": "Ebedi Pokémon", "latiosCategory": "Ebedi Pokémon", "kyogreCategory": "<PERSON><PERSON>", "groudonCategory": "Kıta Pokémon", "rayquazaCategory": "Gök Yüksekliği Pokémon", "jirachiCategory": "Dilek Pokémon", "deoxysCategory": "DNA Pokémon", "turtwigCategory": "Minik Yaprak Pokémon", "grotleCategory": "<PERSON><PERSON>", "torterraCategory": "Kıta Pokémon", "chimcharCategory": "Şempanze Pokémon", "monfernoCategory": "Oyuncu Pokémon", "infernapeCategory": "Alev Pokémon", "piplupCategory": "Penguen Pokémon", "prinplupCategory": "Penguen Pokémon", "empoleonCategory": "İmparator Pokémon", "starlyCategory": "Sığırcık Pokémon", "staraviaCategory": "Sığırcık Pokémon", "staraptorCategory": "Yırtıcı Pokémon", "bidoofCategory": "Tombul Fare Pokémon", "bibarelCategory": "Kunduz Pokémon", "kricketotCategory": "Cırcır Böceği Pokémon", "kricketuneCategory": "Cırcır Böceği Pokémon", "shinxCategory": "Flaş Pokémon", "luxioCategory": "Kıvılcım Pokémon", "luxrayCategory": "Pırıltılı Pokémon", "budewCategory": "Tomurcuk Pokémon", "roseradeCategory": "Buket Pokémon", "cranidosCategory": "<PERSON><PERSON>", "rampardosCategory": "<PERSON><PERSON>", "shieldonCategory": "<PERSON><PERSON>an <PERSON>", "bastiodonCategory": "<PERSON><PERSON>an <PERSON>", "burmyCategory": "Torbalı Güve Pokémon", "wormadamCategory": "Torbalı Güve Pokémon", "mothimCategory": "Güve Pokémon", "combeeCategory": "Minik Arı Pokémon", "vespiquenCategory": "<PERSON><PERSON>ı <PERSON>", "pachirisuCategory": "Elektrikli Sincap Pokémon", "buizelCategory": "<PERSON><PERSON>", "floatzelCategory": "<PERSON><PERSON>", "cherubiCategory": "Vişne Pokémon", "cherrimCategory": "Çiçek Açan Pokémon", "shellosCategory": "<PERSON><PERSON>", "gastrodonCategory": "<PERSON><PERSON>", "ambipomCategory": "Uzun Ku<PERSON>uk<PERSON> Pokémon", "drifloonCategory": "Balon Pokémon", "drifblimCategory": "<PERSON><PERSON><PERSON>", "bunearyCategory": "Tavşan Pokémon", "lopunnyCategory": "Tavşan Pokémon", "mismagiusCategory": "Sihirli Pokémon", "honchkrowCategory": "Büyük Patron Pokémon", "glameowCategory": "Kedi Gibi <PERSON>", "puruglyCategory": "Kaplan Kedi Pokémon", "chinglingCategory": "Çan <PERSON>", "stunkyCategory": "Kokarca Pokémon", "skuntankCategory": "Kokarca Pokémon", "bronzorCategory": "Bronz Pokémon", "bronzongCategory": "Bronz Çan Pokémon", "bonslyCategory": "Bonsai Pokémon", "mimeJrCategory": "Pandomim Pokémon", "happinyCategory": "Oyun Evi Pokémon", "chatotCategory": "Müzik Notası Pokémon", "spiritombCategory": "Yasak Pokémon", "gibleCategory": "Kara Köpekbalığı Pokémon", "gabiteCategory": "Mağarasever Pokémon", "garchompCategory": "Mak Pokémon", "munchlaxCategory": "Koca Obur Pokémon", "rioluCategory": "Duygu Yayan Pokémon", "lucarioCategory": "Aura Pokémon", "hippopotasCategory": "Hipopotam Pokémon", "hippowdonCategory": "<PERSON><PERSON><PERSON><PERSON>", "skorupiCategory": "Akrep Pokémon", "drapionCategory": "<PERSON>", "croagunkCategory": "Toksik Ağızlı Pokémon", "toxicroakCategory": "Toksik Ağızlı Pokémon", "carnivineCategory": "Böcek Avcısı Pokémon", "finneonCategory": "Kanat Balığı Pokémon", "lumineonCategory": "Neon Pokémon", "mantykeCategory": "Uçurtma Pokémon", "snoverCategory": "Buz Ağacı Pokémon", "abomasnowCategory": "Buz Ağacı Pokémon", "weavileCategory": "<PERSON><PERSON>", "magnezoneCategory": "Manyetik Alanlı Pokémon", "lickilickyCategory": "Yalayan Pokémon", "rhyperiorCategory": "Delici Pokémon", "tangrowthCategory": "Sarmaşık Pokémon", "electivireCategory": "Yıldırım Pokémon", "magmortarCategory": "Patlayan Pokémon", "togekissCategory": "Şen Pokémon", "yanmegaCategory": "<PERSON>", "leafeonCategory": "Yeşillikli Pokémon", "glaceonCategory": "<PERSON><PERSON>", "gliscorCategory": "Diş Akrebi Pokémon", "mamoswineCategory": "İkiz Diş Pokémon", "porygonZCategory": "Sanal Pokémon", "galladeCategory": "Bıçaklı Pokémon", "probopassCategory": "Pusula Pokémon", "dusknoirCategory": "<PERSON><PERSON>rayan <PERSON>", "froslassCategory": "Donmuş Pokémon", "rotomCategory": "Plazma Pokémon", "uxieCategory": "Bilgili Pokémon", "mespritCategory": "Duygu Pokémon", "azelfCategory": "İradeli Pokémon", "dialgaCategory": "Zamansal Pokémon", "palkiaCategory": "Uzamsal Pokémon", "heatranCategory": "<PERSON><PERSON>", "regigigasCategory": "Devasa Pokémon", "giratinaCategory": "Sürgün Pokémon", "cresseliaCategory": "Hilal Pokémon", "phioneCategory": "Başıboş Pokémon", "manaphyCategory": "Gemici Pokémon", "darkraiCategory": "Zifiri Pokémon", "shayminCategory": "Minnettar Pokémon", "arceusCategory": "Alfa Pokémon", "victiniCategory": "Zafer Pokémon", "snivyCategory": "Çimen Yılanı Pokémon", "servineCategory": "Çimen Yılanı Pokémon", "serperiorCategory": "Görkemli Pokémon", "tepigCategory": "Ateş Domuzu Pokémon", "pigniteCategory": "Ateş Domuzu Pokémon", "emboarCategory": "Mega Ateş Domuzu Pokémon", "oshawottCategory": "Su Samuru Pokémon", "dewottCategory": "Disiplinli Pokémon", "samurottCategory": "Heybetli Pokémon", "patratCategory": "İzci Pokémon", "watchogCategory": "Gözcü Pokémon", "lillipupCategory": "Köpek Yavrusu Pokémon", "herdierCategory": "Sadık Köpek Pokémon", "stoutlandCategory": "Cömert Pokémon", "purrloinCategory": "Aldatıcı Pokémon", "liepardCategory": "Acımasız Pokémon", "pansageCategory": "<PERSON><PERSON><PERSON>", "simisageCategory": "<PERSON><PERSON><PERSON>", "pansearCategory": "Yüksek Isılı Pokémon", "simisearCategory": "Köz Pokémon", "panpourCategory": "Püskürten Pokémon", "simipourCategory": "Gayzer Pokémon", "munnaCategory": "<PERSON><PERSON><PERSON>", "musharnaCategory": "Uyuklayan Pokémon", "pidoveCategory": "Minik Güvercin Pokémon", "tranquillCategory": "Yabani Güvercin Pokémon", "unfezantCategory": "Gururlu Pokémon", "blitzleCategory": "Elektrikli Pokémon", "zebstrikaCategory": "Yıldırım Pokémon", "roggenrolaCategory": "Taş Pokémon", "boldoreCategory": "Cevher Pokémon", "gigalithCategory": "Basınçlı Pokémon", "woobatCategory": "Yarasa Pokémon", "swoobatCategory": "<PERSON><PERSON>", "drilburCategory": "Köstebek Pokémon", "excadrillCategory": "Toprak Altı Pokémon", "audinoCategory": "Duyan <PERSON>", "timburrCategory": "Adaleli Pokémon", "gurdurrCategory": "Adaleli Pokémon", "conkeldurrCategory": "Adaleli Pokémon", "tympoleCategory": "İribaş Pokémon", "palpitoadCategory": "Titreşen Pokémon", "seismitoadCategory": "Titreşen Pokémon", "throhCategory": "Judocu Pokémon", "sawkCategory": "Karateci Pokémon", "sewaddleCategory": "Terzi Pokémon", "swadloonCategory": "Yaprak Sarılı Pokémon", "leavannyCategory": "Besleyici Pokémon", "venipedeCategory": "Çıyan Pokémon", "whirlipedeCategory": "Yuvarlanan Pokémon", "scolipedeCategory": "Mega Çıyan Pokémon", "cottoneeCategory": "Pamuk Topu Pokémon", "whimsicottCategory": "Uçuşan Pokémon", "petililCategory": "Kök Pokémon", "lilligantCategory": "Çiçeklenen Pokémon", "basculinCategory": "Düşman Pokémon", "sandileCategory": "Çöl Timsahı Pokémon", "krokorokCategory": "Çöl Timsahı Pokémon", "krookodileCategory": "Göz Korkutucu Pokémon", "darumakaCategory": "Zen Tılsımlı Pokémon", "darmanitanCategory": "Alevli Pokémon", "maractusCategory": "Kaktüs Pokémon", "dwebbleCategory": "<PERSON><PERSON>", "crustleCategory": "Taş Evli Pokémon", "scraggyCategory": "Saçıntılı Pokémon", "scraftyCategory": "Serseri Pokémon", "sigilyphCategory": "Kuşumsu Pokémon", "yamaskCategory": "<PERSON><PERSON>", "cofagrigusCategory": "Lahit Pokémon", "tirtougaCategory": "Proto Kaplumbağa Pokémon", "carracostaCategory": "Proto Kaplumbağa Pokémon", "archenCategory": "İlk Kuş Pokémon", "archeopsCategory": "İlk Kuş Pokémon", "trubbishCategory": "Çöp <PERSON>bası Pokémon", "garbodorCategory": "Çöp Yığını Pokémon", "zoruaCategory": "Numaracı Tilki Pokémon", "zoroarkCategory": "Aldatıcı Tilki Pokémon", "minccinoCategory": "Çinçilla Pokémon", "cinccinoCategory": "Eşarp Pokémon", "gothitaCategory": "Saplantılı Pokémon", "gothoritaCategory": "Manipülatör Pokémon", "gothitelleCategory": "Astral Bedenli Pokémon", "solosisCategory": "Hücre Pokémon", "duosionCategory": "Mitoz <PERSON>", "reuniclusCategory": "Çoğalan Pokémon", "ducklettCategory": "Su Kuşu Pokémon", "swannaCategory": "<PERSON>az <PERSON>", "vanilliteCategory": "<PERSON><PERSON>", "vanillishCategory": "Buzlu Kar Pokémon", "vanilluxeCategory": "Kar <PERSON>", "deerlingCategory": "Mevsimlik Pokémon", "sawsbuckCategory": "Mevsimlik Pokémon", "emolgaCategory": "Gök Sincabı Pokémon", "karrablastCategory": "Kıstıran Pokémon", "escavalierCategory": "Süvari Pokémon", "foongusCategory": "Mantar Pokémon", "amoongussCategory": "Mantar Pokémon", "frillishCategory": "Yüzen Pokémon", "jellicentCategory": "Yüzen Pokémon", "alomomolaCategory": "Şefkatli Pokémon", "joltikCategory": "Yapışan Pokémon", "galvantulaCategory": "Elektrikli Örümcek Pokémon", "ferroseedCategory": "<PERSON><PERSON><PERSON>", "ferrothornCategory": "Diken Kabu<PERSON>lu <PERSON>", "klinkCategory": "Dişli Pokémon", "klangCategory": "Dişli Pokémon", "klinklangCategory": "Dişli Pokémon", "tynamoCategory": "Yılanbalığı Pokémon", "eelektrikCategory": "Yılanbalığı Pokémon", "eelektrossCategory": "Yılanbalığı Pokémon", "elgyemCategory": "Zihinsel Pokémon", "beheeyemCategory": "Zihinsel Pokémon", "litwickCategory": "Mum Pokémon", "lampentCategory": "Fener <PERSON>", "chandelureCategory": "Akıl Çelen Pokémon", "axewCategory": "Uzun Dişli Pokémon", "fraxureCategory": "Balta Çeneli Pokémon", "haxorusCategory": "Balta Çeneli Pokémon", "cubchooCategory": "Üşüten Pokémon", "bearticCategory": "Donduran Pokémon", "cryogonalCategory": "Kristal Pokémon", "shelmetCategory": "Salyangoz Pokémon", "accelgorCategory": "Yarı Kabuklu Pokémon", "stunfiskCategory": "Tuzakçı Pokémon", "mienfooCategory": "Dövüş Ustası Pokémon", "mienshaoCategory": "Dövüş Ustası Pokémon", "druddigonCategory": "Mağarasever Pokémon", "golettCategory": "Otomaton Pokémon", "golurkCategory": "Otomaton Pokémon", "pawniardCategory": "Keskin Bıçak Pokémon", "bisharpCategory": "Kılıç Bıçak Pokémon", "bouffalantCategory": "<PERSON><PERSON><PERSON>", "ruffletCategory": "Kartal Yavrusu Pokémon", "braviaryCategory": "Yiğit Pokémon", "vullabyCategory": "Bezli Pokémon", "mandibuzzCategory": "Kemik Akbaba Pokémon", "heatmorCategory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "durantCategory": "<PERSON><PERSON>", "deinoCategory": "Hiddetli Pokémon", "zweilousCategory": "Düşman Pokémon", "hydreigonCategory": "Zalim Pokémon", "larvestaCategory": "Meşale Pokémon", "volcaronaCategory": "Güneş Pokémon", "cobalionCategory": "<PERSON><PERSON>", "terrakionCategory": "Oyuksever Pokémon", "virizionCategory": "Çayırsever Pokémon", "tornadusCategory": "Hortum Pokémon", "thundurusCategory": "Yıldı<PERSON><PERSON><PERSON> Çarpan Pokémon", "reshiramCategory": "Büyük Beyaz Pokémon", "zekromCategory": "Simsiyah Pokémon", "landorusCategory": "Bereketli Pokémon", "kyuremCategory": "Sınır Pokémon", "keldeoCategory": "Sıpa Pokémon", "meloettaCategory": "Melodi Pokémon", "genesectCategory": "Paleozoik Pokémon", "chespinCategory": "<PERSON><PERSON><PERSON>", "quilladinCategory": "<PERSON><PERSON>li <PERSON>", "chesnaughtCategory": "<PERSON><PERSON>li <PERSON>", "fennekinCategory": "Tilki Pokémon", "braixenCategory": "Tilki Pokémon", "delphoxCategory": "Tilki Pokémon", "froakieCategory": "Baloncuklu Kurbağa Pokémon", "frogadierCategory": "Baloncuklu Kurbağa Pokémon", "greninjaCategory": "Ninja Pokémon", "bunnelbyCategory": "Kazıcı Pokémon", "diggersbyCategory": "Kazıcı Pokémon", "fletchlingCategory": "Küçük Bülbül Pokémon", "fletchinderCategory": "Köz Pokémon", "talonflameCategory": "Kavurucu Pokémon", "scatterbugCategory": "Toz Püskürten Pokémon", "spewpaCategory": "Toz Püskürten Pokémon", "vivillonCategory": "Pul Pokémon", "litleoCategory": "<PERSON><PERSON>", "pyroarCategory": "Asil <PERSON>", "flabebeCategory": "Tek Çiçekli Pokémon", "floetteCategory": "Tek Çiçekli Pokémon", "florgesCategory": "Bahçıvan Pokémon", "skiddoCategory": "Binek Pokémon", "gogoatCategory": "Binek Pokémon", "panchamCategory": "Oyuncu Pokémon", "pangoroCategory": "Ürkütücü Pokémon", "furfrouCategory": "Kaniş Pokémon", "espurrCategory": "Kısıtlayıcı Pokémon", "meowsticCategory": "Baskıcı Pokémon", "honedgeCategory": "Kılıç Pokémon", "doubladeCategory": "Kılıç Pokémon", "aegislashCategory": "<PERSON><PERSON>", "spritzeeCategory": "Kokulu Pokémon", "aromatisseCategory": "Parfümlü Pokémon", "swirlixCategory": "<PERSON><PERSON>", "slurpuffCategory": "Beze Pokémon", "inkayCategory": "Dönen <PERSON>", "malamarCategory": "<PERSON><PERSON>", "binacleCategory": "<PERSON>ki <PERSON>", "barbaracleCategory": "Kolektif Pokémon", "skrelpCategory": "<PERSON><PERSON><PERSON>", "dragalgeCategory": "<PERSON><PERSON><PERSON>", "clauncherCategory": "Su Tabancası Pokémon", "clawitzerCategory": "<PERSON>van <PERSON>u <PERSON>", "helioptileCategory": "Jeneratör Pokémon", "helioliskCategory": "Jeneratör Pokémon", "tyruntCategory": "Hükü<PERSON><PERSON>", "tyrantrumCategory": "Despot Pokémon", "amauraCategory": "Tundra Pokémon", "aurorusCategory": "Tundra Pokémon", "sylveonCategory": "Sarılan Pokémon", "hawluchaCategory": "Güreşçi Pokémon", "dedenneCategory": "Antenli Pokémon", "carbinkCategory": "Mücevher Pokémon", "goomyCategory": "Yumuşak Dokulu Pokémon", "sliggooCategory": "Yumuşak Dokulu Pokémon", "goodraCategory": "Ejderha Pokémon", "klefkiCategory": "Anahtarlık Pokémon", "phantumpCategory": "Kütük Pokémon", "trevenantCategory": "Yaşlı Ağaç Pokémon", "pumpkabooCategory": "Balkabağı Pokémon", "gourgeistCategory": "Balkabağı Pokémon", "bergmiteCategory": "Buz Yığını Pokémon", "avaluggCategory": "Buz Dağı Pokémon", "noibatCategory": "Ses Dal<PERSON>", "noivernCategory": "Ses Dal<PERSON>", "xerneasCategory": "<PERSON><PERSON><PERSON>", "yveltalCategory": "İmha <PERSON>", "zygardeCategory": "Nizam Pokémon", "diancieCategory": "Mücevher Pokémon", "hoopaCategory": "Haylaz Pokémon", "volcanionCategory": "Buharlı Pokémon", "eternalFloetteCategory": "Tek Çiçekli Pokémon", "rowletCategory": "<PERSON><PERSON>", "dartrixCategory": "Bıçak Tüylü Pokémon", "decidueyeCategory": "<PERSON>", "littenCategory": "Ateş Kedi <PERSON>", "torracatCategory": "Ateş Kedi <PERSON>", "incineroarCategory": "Alçak Pokémon", "popplioCategory": "<PERSON><PERSON>", "brionneCategory": "Pop Yıldızı Pokémon", "primarinaCategory": "Solist Pokémon", "pikipekCategory": "Ağaçkakan Pokémon", "trumbeakCategory": "Boru <PERSON>lı <PERSON>", "toucannonCategory": "Topçu Pokémon", "yungoosCategory": "<PERSON>re Pokémon", "gumshoosCategory": "Yakın <PERSON> Pokémon", "grubbinCategory": "Larva Pokémon", "charjabugCategory": "Pil Pokémon", "vikavoltCategory": "Makaslı Böcek Pokémon", "crabrawlerCategory": "Boksör Pokémon", "crabominableCategory": "Kürklü Yengeç Pokémon", "oricorioCategory": "<PERSON><PERSON>", "cutieflyCategory": "Arı Sineği Pokémon", "ribombeeCategory": "Arı Sineği Pokémon", "rockruffCategory": "Köpek Yavrusu Pokémon", "lycanrocCategory": "<PERSON>", "wishiwashiCategory": "Küçük Balık Pokémon", "mareanieCategory": "Vahşi Yıldız Pokémon", "toxapexCategory": "Vahşi Yıldız Pokémon", "mudbrayCategory": "Eşek Pokémon", "mudsdaleCategory": "Koşum Atı Pokémon", "dewpiderCategory": "Su Kabarcığı Pokémon", "araquanidCategory": "Su Kabarcığı Pokémon", "fomantisCategory": "Orak Bitkisi Pokémon", "lurantisCategory": "Çiçek Orağı Pokémon", "morelullCategory": "Aydınlatıcı Pokémon", "shiinoticCategory": "Aydınlatıcı Pokémon", "salanditCategory": "<PERSON><PERSON><PERSON><PERSON>", "salazzleCategory": "<PERSON><PERSON><PERSON><PERSON>", "stuffulCategory": "Savuran Pokémon", "bewearCategory": "Güçlü Kollu Pokémon", "bounsweetCategory": "Meyve Pokémon", "steeneeCategory": "Meyve Pokémon", "tsareenaCategory": "Meyve Pokémon", "comfeyCategory": "Çiçek Toplayan Pokémon", "oranguruCategory": "Bilge Pokémon", "passimianCategory": "İşbirlikçi Pokémon", "wimpodCategory": "Ürkek Pokémon", "golisopodCategory": "<PERSON><PERSON>", "sandygastCategory": "<PERSON><PERSON>", "palossandCategory": "<PERSON><PERSON><PERSON>", "pyukumukuCategory": "<PERSON><PERSON>", "typeNullCategory": "Sentetik Pokémon", "silvallyCategory": "Sentetik Pokémon", "miniorCategory": "Meteor Pokémon", "komalaCategory": "Uyuklayan Pokémon", "turtonatorCategory": "Patlayan Ka<PERSON>lumbağa Pokémon", "togedemaruCategory": "Tombiş Pokémon", "mimikyuCategory": "Kılık Değiştiren Pokémon", "bruxishCategory": "Diş Gıcırdatan Pokémon", "drampaCategory": "Uysal Pokémon", "dhelmiseCategory": "<PERSON><PERSON>", "jangmoOCategory": "Pullu Pokémon", "hakamoOCategory": "Pullu Pokémon", "kommoOCategory": "Pullu Pokémon", "tapuKokoCategory": "Toprak Ruhu Pokémon", "tapuLeleCategory": "Toprak Ruhu Pokémon", "tapuBuluCategory": "Toprak Ruhu Pokémon", "tapuFiniCategory": "Toprak Ruhu Pokémon", "cosmogCategory": "Nebula Pokémon", "cosmoemCategory": "Proto Yıldız Pokémon", "solgaleoCategory": "<PERSON><PERSON><PERSON><PERSON>", "lunalaCategory": "<PERSON><PERSON>", "nihilegoCategory": "Parazit Pokémon", "buzzwoleCategory": "Kaslı Pokémon", "pheromosaCategory": "Çevik <PERSON>", "xurkitreeCategory": "Parlayan Pokémon", "celesteelaCategory": "Roket Pokémon", "kartanaCategory": "Çekilmiş Kılıç Pokémon", "guzzlordCategory": "Obur <PERSON>", "necrozmaCategory": "Prizma Pokémon", "magearnaCategory": "Yapay Pokémon", "marshadowCategory": "Kasvetli Pokémon", "poipoleCategory": "Zehirli İğneli Pokémon", "naganadelCategory": "Zehirli İğneli Pokémon", "stakatakaCategory": "Sur Pokémon", "blacephalonCategory": "<PERSON><PERSON><PERSON>", "zeraoraCategory": "Gök <PERSON>ü<PERSON>mesi Pokémon", "meltanCategory": "<PERSON>ıgen Somun Pokémon", "melmetalCategory": "<PERSON>ıgen Somun Pokémon", "grookeyCategory": "Şempanze Pokémon", "thwackeyCategory": "Ritim Pokémon", "rillaboomCategory": "Davulcu Pokémon", "scorbunnyCategory": "Tavşan Pokémon", "rabootCategory": "Tavşan Pokémon", "cinderaceCategory": "Forvet Pokémon", "sobbleCategory": "<PERSON>", "drizzileCategory": "<PERSON>", "inteleonCategory": "<PERSON><PERSON><PERSON>", "skwovetCategory": "Yanaklı Pokémon", "greedentCategory": "Açgözlü Pokémon", "rookideeCategory": "Minik Ku<PERSON>", "corvisquireCategory": "Kuzgun Pokémon", "corviknightCategory": "Kuzgun Pokémon", "blipbugCategory": "Larva Pokémon", "dottlerCategory": "Radom Pokémon", "orbeetleCategory": "Yedi Noktalı Pokémon", "nickitCategory": "Tilki Pokémon", "thievulCategory": "Tilki Pokémon", "gossifleurCategory": "Çiçeklenen Pokémon", "eldegossCategory": "Pamuk Saçan Pokémon", "woolooCategory": "Koyun Pokémon", "dubwoolCategory": "Koyun Pokémon", "chewtleCategory": "Kapan Pokémon", "drednawCategory": "<PERSON><PERSON>ran <PERSON>", "yamperCategory": "Köpek Yavrusu Pokémon", "boltundCategory": "Köpek Pokémon", "rolycolyCategory": "Kömür Pokémon", "carkolCategory": "Kömür Pokémon", "coalossalCategory": "Kömür Pokémon", "applinCategory": "Elma Çekirdeği Pokémon", "flappleCategory": "Kanatlı Elma Pokémon", "appletunCategory": "Elma Nektarı Pokémon", "silicobraCategory": "<PERSON><PERSON>", "sandacondaCategory": "<PERSON><PERSON>", "cramorantCategory": "Yutkunan Pokémon", "arrokudaCategory": "Hücumcu Pokémon", "barraskewdaCategory": "Şiş Pokémon", "toxelCategory": "Bebek Pokémon", "toxtricityCategory": "Punk Pokémon", "sizzlipedeCategory": "Işıyıcı Pokémon", "centiskorchCategory": "Işıyıcı Pokémon", "clobbopusCategory": "Aksi Pokémon", "grapploctCategory": "Jujitsu Pokémon", "sinisteaCategory": "Siyah Çay Pokémon", "polteageistCategory": "Siyah Çay Pokémon", "hatennaCategory": "Sakin Pokémon", "hattremCategory": "Durgun Pokémon", "hattereneCategory": "Sessiz Pokémon", "impidimpCategory": "Kurnaz Pokémon", "morgremCategory": "Aldatıcı Pokémon", "grimmsnarlCategory": "Güçlenen Pokémon", "obstagoonCategory": "Bloklayan Pokémon", "perrserkerCategory": "Viking Pokémon", "cursolaCategory": "Mercan Pokémon", "sirfetchdCategory": "<PERSON>ban Ö<PERSON>ği Pokémon", "mrRimeCategory": "Komedyen Pokémon", "runerigusCategory": "Kinci Pokémon", "milceryCategory": "Krema Pokémon", "alcremieCategory": "Krema Pokémon", "falinksCategory": "Dizili Pokémon", "pincurchinCategory": "Denizkestanesi Pokémon", "snomCategory": "Tırtıl Pokémon", "frosmothCategory": "Buz Güvesi Pokémon", "stonjournerCategory": "Büyük Kaya Pokémon", "eiscueCategory": "Penguen Pokémon", "indeedeeCategory": "Duygu Pokémon", "morpekoCategory": "İki <PERSON>lı <PERSON>", "cufantCategory": "Bakır Derili Pokémon", "copperajahCategory": "Bakır Derili Pokémon", "dracozoltCategory": "Fosil Pokémon", "arctozoltCategory": "Fosil Pokémon", "dracovishCategory": "Fosil Pokémon", "arctovishCategory": "Fosil Pokémon", "duraludonCategory": "Alaşım Pokémon", "dreepyCategory": "Oyalanan Pokémon", "drakloakCategory": "Bakıcı Pokémon", "dragapultCategory": "<PERSON><PERSON><PERSON>", "zacianCategory": "Savaşçı Pokémon", "zamazentaCategory": "Savaşçı Pokémon", "eternatusCategory": "Kocaman Pokémon", "kubfuCategory": "Wushu Pokémon", "urshifuCategory": "Wushu Pokémon", "zarudeCategory": "Muzip <PERSON>mun Pokémon", "regielekiCategory": "Elektron Pokémon", "regidragoCategory": "Ejderha <PERSON>", "glastrierCategory": "Vah<PERSON>i At Pokémon", "spectrierCategory": "Hızlı At Pokémon", "calyrexCategory": "Kral Pokémon", "wyrdeerCategory": "<PERSON><PERSON>", "kleavorCategory": "Balta Pokémon", "ursalunaCategory": "Turba Pokémon", "basculegionCategory": "Büyük Balık Pokémon", "sneaslerCategory": "Dağcı Pokémon", "overqwilCategory": "İğne Yığını Pokémon", "enamorusCategory": "Aşk Nefret <PERSON>", "sprigatitoCategory": "Bit<PERSON>di <PERSON>", "floragatoCategory": "Bit<PERSON>di <PERSON>", "meowscaradaCategory": "Sihirbaz Pokémon", "fuecocoCategory": "Ateş Timsahı Pokémon", "crocalorCategory": "Ateş Timsahı Pokémon", "skeledirgeCategory": "Şarkıcı Pokémon", "quaxlyCategory": "Ördek Yavrusu Pokémon", "quaxwellCategory": "Çalışkan Pokémon", "quaquavalCategory": "Dansçı Pokémon", "lechonkCategory": "Büyük Domuz Pokémon", "oinkologneCategory": "Büyük Domuz Pokémon", "tarountulaCategory": "<PERSON><PERSON><PERSON>", "spidopsCategory": "Tuzakçı Pokémon", "nymbleCategory": "Çekirge Pokémon", "lokixCategory": "Çekirge Pokémon", "pawmiCategory": "Fare Pokémon", "pawmoCategory": "Fare Pokémon", "pawmotCategory": "Koca Avuç Pokémon", "tandemausCategory": "Çift Pokémon", "mausholdCategory": "Aile Pokémon", "fidoughCategory": "Köpek Yavrusu Pokémon", "dachsbunCategory": "Köpek Pokémon", "smolivCategory": "Zeytin Pokémon", "dollivCategory": "Zeytin Pokémon", "arbolivaCategory": "Zeytin Pokémon", "squawkabillyCategory": "Papağan Pokémon", "nacliCategory": "<PERSON><PERSON>", "naclstackCategory": "<PERSON><PERSON>", "garganaclCategory": "<PERSON><PERSON>", "charcadetCategory": "Ateş Çocuğu Pokémon", "armarougeCategory": "Ateş Savaşçısı Pokémon", "ceruledgeCategory": "Ateş Bıçağı Pokémon", "tadbulbCategory": "Elektrikli İribaş Pokémon", "belliboltCategory": "Elektrikli Kurbağa Pokémon", "wattrelCategory": "<PERSON><PERSON><PERSON><PERSON>na <PERSON>", "kilowattrelCategory": "Fregat Kuşu Pokémon", "maschiffCategory": "Yaramaz Pokémon", "mabosstiffCategory": "Patron Pokémon", "shroodleCategory": "Zehirli Fare Pokémon", "grafaiaiCategory": "<PERSON><PERSON><PERSON><PERSON>", "bramblinCategory": "Yuvarlanan Çalı <PERSON>", "brambleghastCategory": "Yuvarlanan Çalı <PERSON>", "toedscoolCategory": "<PERSON><PERSON><PERSON>ç <PERSON>", "toedscruelCategory": "<PERSON><PERSON><PERSON>ç <PERSON>", "klawfCategory": "Pusu Pokémon", "capsakidCategory": "Acı Biber Pokémon", "scovillainCategory": "Acı Biber Pokémon", "rellorCategory": "Yuvarlayan Pokémon", "rabscaCategory": "Yuvarlayan Pokémon", "flittleCategory": "Fırfır Pokémon", "espathraCategory": "Devekuşu Pokémon", "tinkatinkCategory": "Metal Ustası Pokémon", "tinkatuffCategory": "Çekiç Pokémon", "tinkatonCategory": "Çekiç Pokémon", "wiglettCategory": "Bahçe Yılan Balığı Pokémon", "wugtrioCategory": "Bahçe Yılan Balığı Pokémon", "bombirdierCategory": "<PERSON><PERSON><PERSON>", "finizenCategory": "<PERSON>us <PERSON>", "palafinCategory": "<PERSON>us <PERSON>", "varoomCategory": "<PERSON><PERSON>", "revavroomCategory": "Çoklu Silindir Pokémon", "cyclizarCategory": "Binek Pokémon", "orthwormCategory": "Toprak Solucanı Pokémon", "glimmetCategory": "Cevher Pokémon", "glimmoraCategory": "Cevher Pokémon", "greavardCategory": "<PERSON><PERSON>", "houndstoneCategory": "<PERSON><PERSON>", "flamigoCategory": "Senkronize Pokémon", "cetoddleCategory": "Kara Balinası Pokémon", "cetitanCategory": "Kara Balinası Pokémon", "veluzaCategory": "<PERSON><PERSON><PERSON>", "dondozoCategory": "Büyük Kedi Balığı Pokémon", "tatsugiriCategory": "Benzeşen Pokémon", "annihilapeCategory": "Öfke Maymunu <PERSON>", "clodsireCategory": "Dikenli Balık Pokémon", "farigirafCategory": "Uzun <PERSON>lu Pokémon", "dudunsparceCategory": "Kara Yılanı Pokémon", "kingambitCategory": "Büyük Bıçaklı Pokémon", "greatTuskCategory": "Paradoks Pokémon", "screamTailCategory": "Paradoks Pokémon", "bruteBonnetCategory": "Paradoks Pokémon", "flutterManeCategory": "Paradoks Pokémon", "slitherWingCategory": "Paradoks Pokémon", "sandyShocksCategory": "Paradoks Pokémon", "ironTreadsCategory": "Paradoks Pokémon", "ironBundleCategory": "Paradoks Pokémon", "ironHandsCategory": "Paradoks Pokémon", "ironJugulisCategory": "Paradoks Pokémon", "ironMothCategory": "Paradoks Pokémon", "ironThornsCategory": "Paradoks Pokémon", "frigibaxCategory": "Buzdan Yüzgeç Pokémon", "arctibaxCategory": "Buzdan Yüzgeç Pokémon", "baxcaliburCategory": "Buz Ejderhası Pokémon", "gholdengoCategory": "Hazine Pokémon", "woChienCategory": "Yıkıp Döken Pokémon", "chienPaoCategory": "Yıkıp Döken Pokémon", "tingLuCategory": "Yıkıp Döken Pokémon", "chiYuCategory": "Yıkıp Döken Pokémon", "roaringMoonCategory": "Paradoks Pokémon", "ironValiantCategory": "Paradoks Pokémon", "koraidonCategory": "Paradoks Pokémon", "miraidonCategory": "Paradoks Pokémon", "walkingWakeCategory": "Paradoks Pokémon", "ironLeavesCategory": "Paradoks Pokémon", "archaludonCategory": "Alaşım Pokémon", "gougingFireCategory": "Paradoks Pokémon", "ragingBoltCategory": "Paradoks Pokémon", "ironBoulderCategory": "Paradoks Pokémon", "ironCrownCategory": "Paradoks Pokémon", "bloodmoonUrsalunaCategory": "Turba Pokémon", "alolaRattataCategory": "Fare Pokémon", "alolaRaticateCategory": "Fare Pokémon", "alolaRaichuCategory": "Fare Pokémon", "alolaSandshrewCategory": "Fare Pokémon", "alolaSandslashCategory": "Fare Pokémon", "alolaVulpixCategory": "Tilki Pokémon", "alolaNinetalesCategory": "Tilki Pokémon", "alolaDiglettCategory": "Köstebek Pokémon", "alolaDugtrioCategory": "Köstebek Pokémon", "alolaMeowthCategory": "Tırmalayıcı Kedi Pokémon", "alolaPersianCategory": "<PERSON><PERSON>", "alolaGeodudeCategory": "Kaya Pokémon", "alolaGravelerCategory": "Kaya Pokémon", "alolaGolemCategory": "Megaton Pokémon", "alolaGrimerCategory": "Balçık Pokémon", "alolaMukCategory": "Balçık Pokémon", "alolaExeggutorCategory": "Hindistan Cevizi Pokémon", "alolaMarowakCategory": "Kemik Toplayıcısı Pokémon", "galarMeowthCategory": "Tırmalayıcı Kedi Pokémon", "galarPonytaCategory": "Eşsiz <PERSON> Pokémon", "galarRapidashCategory": "Eşsiz <PERSON> Pokémon", "galarSlowpokeCategory": "Uyuşuk Pokémon", "galarSlowbroCategory": "Pavurya Pokémon", "galarFarfetchdCategory": "<PERSON>ban Ö<PERSON>ği Pokémon", "galarWeezingCategory": "Zehirli Gaz Pokémon", "galarMrMimeCategory": "<PERSON><PERSON>", "galarArticunoCategory": "Acımasız Pokémon", "galarZapdosCategory": "Güçlü Bacaklı Pokémon", "galarMoltresCategory": "Kötücül Pokémon", "galarSlowkingCategory": "Asil <PERSON>", "galarCorsolaCategory": "Mercan Pokémon", "galarZigzagoonCategory": "Minik Rakun Pokémon", "galarLinooneCategory": "Aceleci Pokémon", "galarDarumakaCategory": "Zen Tılsımlı Pokémon", "galarDarmanitanCategory": "Zen Tılsımlı Pokémon", "galarYamaskCategory": "<PERSON><PERSON>", "galarStunfiskCategory": "Tuzakçı Pokémon", "hisuiGrowlitheCategory": "İzci Pokémon", "hisuiArcanineCategory": "Efsanevi Pokémon", "hisuiVoltorbCategory": "<PERSON><PERSON>re <PERSON>", "hisuiElectrodeCategory": "<PERSON><PERSON>re <PERSON>", "hisuiTyphlosionCategory": "<PERSON><PERSON>", "hisuiQwilfishCategory": "Balon Pokémon", "hisuiSneaselCategory": "<PERSON><PERSON>", "hisuiSamurottCategory": "Heybetli Pokémon", "hisuiLilligantCategory": "Fırıldak Pokémon", "hisuiZoruaCategory": "Kindar Tilki Pokémon", "hisuiZoroarkCategory": "<PERSON><PERSON> Tilki <PERSON>", "hisuiBraviaryCategory": "Avcı Pokémon", "hisuiSliggooCategory": "Salyangoz Pokémon", "hisuiGoodraCategory": "Sığınaklı Pokémon", "hisuiAvaluggCategory": "Buz Dağı Pokémon", "hisuiDecidueyeCategory": "<PERSON>", "paldeaTaurosCategory": "Yaban Öküzü Pokémon", "paldeaWooperCategory": "Zehirli Balık Pokémon"}