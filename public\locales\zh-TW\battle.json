{"bossAppeared": "{{boss<PERSON><PERSON>}} 出現了。", "trainerAppeared": "{{<PERSON><PERSON><PERSON>}}\n想要和你對戰！", "trainerAppearedDouble": "{{<PERSON><PERSON><PERSON>}}\n想要和你對戰！", "trainerSendOut": "{{trainerName}}派出了\n{{pokemonName}}！", "singleWildAppeared": "一只野生的{{pokemonName}}出現了！", "multiWildAppeared": "野生的{{pokemonName1}}\n和{{pokemonName2}}出現了！", "playerComeBack": "回來吧，{{pokemonName}}！", "trainerComeBack": "{{trainerName}}收回了{{pokemonName}}！", "playerGo": "去吧！{{pokemonName}}！", "trainerGo": "{{trainerName}}派出了\n{{pokemonName}}！", "pokemonDraggedOut": "{{pokemon<PERSON>ame}}\n被拖出來戰鬥了！", "switchQuestion": "要更換\n{{pokemonName}}嗎？", "trainerDefeated": "你擊敗了\n{{trainer<PERSON>ame}}！", "moneyWon": "你贏得了\n₽{{moneyAmount}}！", "moneyPickedUp": "撿到了₽{{moneyAmount}}！", "pokemonCaught": "{{pokemon<PERSON>ame}}被抓住了！", "pokemonObtained": "已接收{{pokemonName}}！", "pokemonBrokeFree": "不行！寶可夢從\n球裡掙脫出來了！", "pokemonFled": "野生的{{pokemonName}}\n逃走了！", "addedAsAStarter": "增加了{{pokemonName}}作爲\n一個新的基礎寶可夢！", "partyFull": "你的隊伍已滿員。是否放生其他寶可夢\n爲{{pokemonName}}騰出空間？", "pokemon": "寶可夢", "sendOutPokemon": "上吧！\n{{pokemonName}}！", "hitResultCriticalHit": "擊中了要害！", "hitResultSuperEffective": "效果拔群！", "hitResultNotVeryEffective": "收效甚微…", "hitResultNoEffect": "對{{pokemon<PERSON>ame}}沒有效果！", "hitResultImmune": "對于{{pokemonName}}，\n完全沒有效果！", "hitResultOneHitKO": "一擊必殺！", "attackFailed": "但是失敗了！", "attackMissed": "沒有命中{{pokemonNameWithAffix}}！", "attackHitsCount": "擊中{{count}}次！", "rewardGain": "你獲得了\n{{modifierName}}！", "rewardGainCount": "你獲得了\n{{count}}個{{modifierName}}!", "expGain": "{{pokemonName}}獲得了{{exp}} 點經驗值！", "levelUp": "{{pokemonName}}的等級\n提升到{{level}}了！", "learnMove": "{{pokemonName}}學會了{{moveName}}！", "learnMovePrompt": "{{pokemonName}}想要學習{{moveName}}。", "learnMoveLimitReached": "但是，{{pokemonName}}已經學會了\n四個技能", "learnMoveReplaceQuestion": "要忘記一個技能並學習{{moveName}}嗎？", "learnMoveStopTeaching": "不再嘗試學習{{moveName}}？", "learnMoveNotLearned": "{{pokemonName}}沒有學會{{moveName}}。", "learnMoveForgetQuestion": "要忘記哪個技能？", "learnMoveForgetSuccess": "{{pokemonName}}忘記了\n如何使用{{moveName}}。", "countdownPoof": "@d{32}1, @d{15}2 @d{15}… @d{15}… @d{15}@s{pb_bounce_1}空！", "learnMoveAnd": "然後……", "levelCapUp": "等級上限提升到{{levelCap}}！", "moveNotImplemented": "{{moveName}}尚未實裝，無法選擇。", "moveNoPP": "這個技能的PP用完了", "moveDisabled": "{{moveName}}被禁用！", "moveDisabledTorment": "{{pokemonNameWithAffix}}遭到了無理取鬧，\n因此無法繼續使出相同的招式！", "moveDisabledTaunt": "{{pokemonNameWithAffix}}受到了挑釁，\n無法使出{{moveName}}！", "moveDisabledImprison": "{{pokemonNameWithAffix}}因封印\n而無法使出{{moveName}}！", "moveDisabledConsecutive": "無法連續２次使出{{moveName}}！", "moveDisabledBelch": "{{pokemonNameWithAffix}}\n因沒有吃樹果而無法使出招式！", "moveDisabledNoBerry": "沒有攜帶樹果，無法使出招式！", "moveDisabledGravity": "{{pokemonNameWithAffix}}因重力太強\n而無法使出{{moveName}}！", "canOnlyUseMove": "{{pokemonName}}\n只能使出{{moveName}！", "disableInterruptedMove": "{{pokemonNameWithAffix}}的{{moveName}}\n被無效化了！", "throatChopInterruptedMove": "{{pokemonName}}因地獄突刺的效果而無法使出招式！", "noPokeballForce": "一股無形的力量阻止了你使用精靈球。", "noPokeballTrainer": "你不能捕捉其他訓練家的寶可夢！", "noPokeballMulti": "只能在剩下一只寶可夢時才能扔出精靈球！", "noPokeballStrong": "目標寶可夢太強了，無法捕捉！\n你需要先削弱它！", "noEscapeForce": "一股無形的力量阻止你逃跑。", "noEscapeTrainer": "你不能從與訓練家的戰鬥中逃跑！", "noEscapePokemon": "{{pokemonName}}的{{moveName}}\n阻止了你{{escapeVerb}}！", "runAwaySuccess": "成功逃走了！", "runAwayCannotEscape": "無法逃走！", "escapeVerbSwitch": "切換", "escapeVerbFlee": "逃跑", "notDisabled": "{{moveName}}不再被禁用！", "turnEndHpRestore": "{{pokemon<PERSON>ame}}的體力恢複了。", "hpIsFull": "{{pokemon<PERSON>ame}}的體力已滿！", "skipItemQuestion": "你確定要跳過拾取道具嗎？", "itemStackFull": "{{fullItemName}}持有數達到上限，\n你獲得了{{itemName}}作爲替代。", "eggHatching": "咦？", "eggSkipPrompt": "{{eggsToHatch}}個蛋已孵化。\n是否快進到孵化總結？", "ivScannerUseQuestion": "對{{pokemon<PERSON>ame}}使用個體值掃描儀？", "wildPokemonWithAffix": "野生的{{pokemonName}}", "foePokemonWithAffix": "對手的{{pokemonName}}", "useMove": "{{pokemonNameWithAffix}}使用了\n{{moveName}}！", "magicCoatActivated": "{{pokemonNameWithAffix}}\n將{{moveName}}反射了回去！", "drainMessage": "{{pokemon<PERSON>ame}}\n吸取了體力！", "regainHealth": "{{pokemon<PERSON>ame}}\n回複了體力！", "stealEatBerry": "{{pokemonName}}奪取並吃掉了\n{{targetName}}的{{berryName}}！", "ppHealBerry": "{{pokemonNameWithAffix}}用{{berryName}}\n回複了{{moveName}}的PP！", "hpHealBerry": "{{pokemonNameWithAffix}}用{{berryName}}\n回複了體力！", "fainted": "{{pokemonNameWithAffix}}\n倒下了！", "statsAnd": "和", "stats": "能力", "statRose_one": "{{pokemonNameWithAffix}}的\n{{stats}}提高了！", "statRose_other": "{{pokemonNameWithAffix}}的\n{{stats}}提高了！", "statSharplyRose_one": "{{pokemonNameWithAffix}}的\n{{stats}}大幅提高了！", "statSharplyRose_other": "{{pokemonNameWithAffix}}的\n{{stats}}大幅提高了！", "statRoseDrastically_one": "{{pokemonNameWithAffix}}的\n{{stats}}極大幅提高了！", "statRoseDrastically_other": "{{pokemonNameWithAffix}}的\n{{stats}}極大幅提高了！", "statWontGoAnyHigher_one": "{{pokemonNameWithAffix}}的\n{{stats}}已經無法再提高了！", "statWontGoAnyHigher_other": "{{pokemonNameWithAffix}}的\n{{stats}}已經無法再提高了！", "statFell_one": "{{pokemonNameWithAffix}}的\n{{stats}}降低了！", "statFell_other": "{{pokemonNameWithAffix}}的\n{{stats}}降低了！", "statHarshlyFell_one": "{{pokemonNameWithAffix}}的\n{{stats}}大幅降低了！", "statHarshlyFell_other": "{{pokemonNameWithAffix}}的\n{{stats}}大幅降低了！", "statSeverelyFell_one": "{{pokemonNameWithAffix}}的\n{{stats}}極大幅降低了！", "statSeverelyFell_other": "{{pokemonNameWithAffix}}的\n{{stats}}極大幅降低了！", "statWontGoAnyLower_one": "{{pokemonNameWithAffix}}的\n{{stats}}已經無法再降低了！", "statWontGoAnyLower_other": "{{pokemonNameWithAffix}}的\n{{stats}}已經無法再降低了！", "transformedIntoType": "{{pokemonName}}變成了\n{{type}}屬性！", "retryBattle": "你要從對戰開始時重試麽？", "unlockedSomething": "{{unlockedThing}}\n已解鎖。", "congratulations": "恭喜！", "beatModeFirstTime": "{{speciesName}}首次擊敗了{{gameMode}}！\n你獲得了{{newModifier}}！", "ppReduced": "降低了{{targetName}}的\n{{moveName}}的PP{{reduction}}點！", "mysteryEncounterAppeared": "這是什麼？"}