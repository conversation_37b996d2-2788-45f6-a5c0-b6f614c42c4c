{"title": "适用挑战条件", "illegalEvolution": "{{pokemon}}变成了\n不符合此挑战条件的宝可梦！", "noneSelected": "未选择", "freshStart": {"name": "初次尝试", "desc": {"0": "模拟使用没有任何额外解锁的初始宝可梦的挑战。", "1": "你只能使用原始状态的御三家，就像是你开了新号。", "2": "你可以使用任何没有额外解锁的初始宝可梦，就像你第一次抓到它们那样。"}, "value": {"0": "关闭", "1": "全部重置", "2": "所有初始宝可梦"}}, "hardcore": {"name": "硬核", "desc": "你不再能够复活濒死的宝可梦。", "value": {"0": "关闭", "1": "开启"}}, "limitedCatch": {"name": "捕获受限", "desc": "你只能捕获每次进入新地区后\n遇到的第一只宝可梦进队。", "value": {"0": "关闭", "1": "开启"}}, "limitedSupport": {"name": "辅助受限", "desc": {"0": "移除部分或全部免费治疗你队伍的选项。", "1": "完成每个区域时，不再治疗你的队伍。\n你仍会获得常规的波次完成奖励。", "2": "完成波次后不再出现商店。", "3": "完成波次后不再出现商店。\n完成每个区域时，不再治疗你的队伍。\n你仍会获得常规的波次完成奖励。"}, "value": {"0": "关闭", "1": "无回复", "2": "无商店", "3": "全部"}}, "singleGeneration": {"name": "单一世代", "desc": "你只能使用第{{gen}}\n世代的宝可梦", "descDefault": "你只能使用所选\n世代的宝可梦", "gen": {"1": "一", "2": "二", "3": "三", "4": "四", "5": "五", "6": "六", "7": "七", "8": "八", "9": "九"}}, "singleType": {"name": "单属性", "desc": "你只能使用{{type}}\n属性的宝可梦", "descDefault": "你只能使用所选\n属性的宝可梦"}, "inverseBattle": {"name": "逆转之战", "shortName": "逆转之战", "desc": "属性相克关系被反转，且没有任何属性对其他属性免疫。\n禁用其他挑战的成就。", "value": {"0": "关闭", "1": "开启"}}, "flipStat": {"name": "能力反转", "shortName": "反转", "desc": "能力按以下规则反转：\n- HP与速度\n- 攻击与特防\n- 防御与特攻\n禁用其他挑战与成就（反转对战除外）", "value": {"0": "关闭", "1": "开启"}}, "metronome": {"name": "挥指", "desc": "您只能摇手指！看看能靠运气\n走多远。这是一种纯乐子模式，\n不会授予成就。禁用其他成就。", "value": {"0": "关闭", "1": "开启"}}}