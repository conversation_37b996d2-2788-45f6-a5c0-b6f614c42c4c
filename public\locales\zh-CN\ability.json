{"stench": {"name": "恶臭", "description": "通过释放臭臭的气味，在攻击的时\n候，有时会使对手畏缩。"}, "drizzle": {"name": "降雨", "description": "出场时，会将天气变为下雨。"}, "speedBoost": {"name": "加速", "description": "每一回合速度会变快。"}, "battleArmor": {"name": "战斗盔甲", "description": "被坚硬的甲壳守护着，不会被对手\n的攻击击中要害。"}, "sturdy": {"name": "结实", "description": "在ＨＰ全满时，即使受到招式攻击\n，也不会被一击打倒。一击必杀的\n招式也没有效果。"}, "damp": {"name": "湿气", "description": "通过把周围都弄湿，使谁都无法使\n用自爆等爆炸类的招式。"}, "limber": {"name": "柔软", "description": "因为身体柔软，不会变为麻痹状态\n。"}, "sandVeil": {"name": "沙隐", "description": "在沙暴的时候，闪避率会提高。"}, "static": {"name": "静电", "description": "身上带有静电，有时会让接触到的\n对手麻痹。"}, "voltAbsorb": {"name": "蓄电", "description": "受到电属性的招式攻击时，不会受\n到伤害，而是会回复。"}, "waterAbsorb": {"name": "储水", "description": "受到水属性的招式攻击时，不会受\n到伤害，而是会回复。"}, "oblivious": {"name": "迟钝", "description": "因为感觉迟钝，不会变为着迷和被\n挑衅状态。对威吓也毫不动摇。"}, "cloudNine": {"name": "无关天气", "description": "任何天气的影响都会消失。"}, "compoundEyes": {"name": "复眼", "description": "因为拥有复眼，招式的命中率会提\n高。"}, "insomnia": {"name": "不眠", "description": "因为有着睡不着的体质，所以不会\n陷入睡眠状态。"}, "colorChange": {"name": "变色", "description": "自己的属性会变为从对手处所受招\n式的属性。"}, "immunity": {"name": "免疫", "description": "因为体内拥有免疫能力，不会变为\n中毒状态。"}, "flashFire": {"name": "引火", "description": "受到火属性的招式攻击时，吸收火\n焰，自己使出的火属性招式会变强\n。"}, "shieldDust": {"name": "鳞粉", "description": "被鳞粉守护着，不会受到招式的追\n加效果影响。"}, "ownTempo": {"name": "我行我素", "description": "因为我行我素，不会变为混乱状态\n。对威吓也毫不动摇。"}, "suctionCups": {"name": "吸盘", "description": "用吸盘牢牢贴在地面上，让替换宝\n可梦的招式和道具无效。"}, "intimidate": {"name": "威吓", "description": "出场时威吓对手，让其退缩，降低\n对手的攻击。"}, "shadowTag": {"name": "踩影", "description": "踩住对手的影子使其无法逃走或替\n换。"}, "roughSkin": {"name": "粗糙皮肤", "description": "受到攻击时，用粗糙的皮肤弄伤接\n触到自己的对手。"}, "wonderGuard": {"name": "神奇守护", "description": "不可思议的力量，只有效果绝佳的\n招式才能击中。"}, "levitate": {"name": "飘浮", "description": "从地面浮起，从而不会受到地面属\n性招式的攻击。"}, "effectSpore": {"name": "孢子", "description": "受到攻击时，有时会把接触到自己\n的对手变为中毒、麻痹或睡眠状态\n。"}, "synchronize": {"name": "同步", "description": "将自己的特殊状态传染给对手。\n当该特性的宝可梦在首位时，\n出现的野生宝可梦的性格与\n它相同。"}, "clearBody": {"name": "恒净之躯", "description": "不会因为对手的招式或特性而被降\n低能力。"}, "naturalCure": {"name": "自然回复", "description": "回到同行队伍后，异常状态就会被\n治愈。"}, "lightningRod": {"name": "避雷针", "description": "将电属性的招式吸引到自己身上，\n不会受到伤害，而是会提高特攻。"}, "sereneGrace": {"name": "天恩", "description": "托天恩的福，招式的追加效果容易\n出现。不会影响道具效果。"}, "swiftSwim": {"name": "悠游自如", "description": "下雨天气时，速度会提高。"}, "chlorophyll": {"name": "叶绿素", "description": "晴朗天气时，速度会提高。"}, "illuminate": {"name": "发光", "description": "通过让周围变亮来保持命中率不会\n被降低，同时提高遭遇双打的概率。"}, "trace": {"name": "复制", "description": "出场时，复制对手的特性，变为与\n之相同的特性。"}, "hugePower": {"name": "大力士", "description": "物理攻击的威力会变为２倍。"}, "poisonPoint": {"name": "毒刺", "description": "有时会让接触到自己的对手变为中\n毒状态。"}, "innerFocus": {"name": "精神力", "description": "拥有经过锻炼的精神，而不会因对\n手的攻击而畏缩。对威吓也毫不动\n摇。"}, "magmaArmor": {"name": "熔岩铠甲", "description": "将炽热的熔岩覆盖在身上，不会变\n为冰冻状态。"}, "waterVeil": {"name": "水幕", "description": "将水幕裹在身上，不会变为灼伤状\n态。"}, "magnetPull": {"name": "磁力", "description": "用磁力吸住钢属性的宝可梦，使其\n无法逃走。"}, "soundproof": {"name": "隔音", "description": "通过屏蔽声音，不受到声音招式的\n影响。"}, "rainDish": {"name": "雨盘", "description": "下雨天气时，会缓缓回复ＨＰ。"}, "sandStream": {"name": "扬沙", "description": "出场时，会把天气变为沙暴。"}, "pressure": {"name": "压迫感", "description": "给予对手压迫感，大量减少其使用\n招式的ＰＰ。"}, "thickFat": {"name": "厚脂肪", "description": "因为被厚厚的脂肪保护着，会让火\n属性和冰属性的招式伤害减半。"}, "earlyBird": {"name": "早起", "description": "即使变为睡眠状态，也能以２倍的\n速度提早醒来。"}, "flameBody": {"name": "火焰之躯", "description": "有时会让接触到自己的对手变为灼\n伤状态。"}, "runAway": {"name": "逃跑", "description": "一定能从野生宝可梦那儿逃走。"}, "keenEye": {"name": "锐利目光", "description": "多亏了锐利的目光，命中率不会被\n降低。"}, "hyperCutter": {"name": "怪力钳", "description": "因为拥有以力量自豪的钳子，不会\n被对手降低攻击。"}, "pickup": {"name": "捡拾", "description": "有时会捡来对手用过的道具，冒险\n过程中也会捡到。"}, "truant": {"name": "懒惰", "description": "如果使出招式，下一回合就会休息\n。"}, "hustle": {"name": "活力", "description": "自己的攻击变高，但命中率会降低\n。"}, "cuteCharm": {"name": "迷人之躯", "description": "有时会让接触到自己的对手着迷。"}, "plus": {"name": "正电", "description": "出场的伙伴之间如果有正电或负电\n特性的宝可梦，自己的特攻会提高\n。"}, "minus": {"name": "负电", "description": "出场的伙伴之间如果有正电或负电\n特性的宝可梦，自己的特攻会提高\n。"}, "forecast": {"name": "阴晴不定", "description": "受天气的影响，会变为水属性、火\n属性或冰属性中的某一个。"}, "stickyHold": {"name": "黏着", "description": "因为道具是粘在黏性身体上的，所\n以不会被对手夺走。"}, "shedSkin": {"name": "蜕皮", "description": "通过蜕去身上的皮，有时会治愈异\n常状态。"}, "guts": {"name": "毅力", "description": "如果变为异常状态，会拿出毅力，\n攻击会提高。"}, "marvelScale": {"name": "神奇鳞片", "description": "如果变为异常状态，神奇鳞片会发\n生反应，防御会提高。"}, "liquidOoze": {"name": "污泥浆", "description": "吸收了污泥浆的对手会因强烈的恶\n臭而受到伤害，减少ＨＰ。"}, "overgrow": {"name": "茂盛", "description": "ＨＰ减少的时候，草属性的招式威\n力会提高。"}, "blaze": {"name": "猛火", "description": "ＨＰ减少的时候，火属性的招式威\n力会提高。"}, "torrent": {"name": "激流", "description": "ＨＰ减少的时候，水属性的招式威\n力会提高。"}, "swarm": {"name": "虫之预感", "description": "ＨＰ减少的时候，虫属性的招式威\n力会提高。"}, "rockHead": {"name": "坚硬脑袋", "description": "即使使出会受反作用力伤害的招式\n，ＨＰ也不会减少。"}, "drought": {"name": "日照", "description": "出场时，会将天气变为晴朗。"}, "arenaTrap": {"name": "沙穴", "description": "在战斗中让对手无法逃走。\n同时提高双打概率。"}, "vitalSpirit": {"name": "干劲", "description": "通过激发出干劲，不会变为睡眠状\n态。"}, "whiteSmoke": {"name": "白色烟雾", "description": "被白色烟雾保护着，不会被对手降\n低能力。"}, "purePower": {"name": "瑜伽之力", "description": "因瑜伽的力量，物理攻击的威力会\n变为２倍。"}, "shellArmor": {"name": "硬壳盔甲", "description": "被坚硬的壳保护着，对手的攻击不\n会击中要害。"}, "airLock": {"name": "气闸", "description": "所有天气的影响都会消失。"}, "tangledFeet": {"name": "蹒跚", "description": "在混乱状态时，闪避率会提高。"}, "motorDrive": {"name": "电气引擎", "description": "受到电属性的招式攻击时，不会受\n到伤害，而是速度会提高。"}, "rivalry": {"name": "斗争心", "description": "面对性别相同的对手，会燃起斗争\n心，变得更强。而面对性别不同的\n，则会变弱。"}, "steadfast": {"name": "不屈之心", "description": "每次畏缩时，不屈之心就会燃起，\n速度也会提高。"}, "snowCloak": {"name": "雪隐", "description": "下雪天气时，闪避率会提高。"}, "gluttony": {"name": "贪吃鬼", "description": "原本ＨＰ变得很少时才会吃树果，\n在ＨＰ还有一半时就会把它吃掉。"}, "angerPoint": {"name": "愤怒穴位", "description": "要害被击中时，会大发雷霆，攻击\n力变为最大。"}, "unburden": {"name": "轻装", "description": "失去所持有的道具时，速度会提高\n。"}, "heatproof": {"name": "耐热", "description": "耐热的体质会让火属性的招式伤害\n减半。"}, "simple": {"name": "单纯", "description": "能力变化会变为平时的２倍。"}, "drySkin": {"name": "干燥皮肤", "description": "下雨天气时和受到水属性的招式时\n，ＨＰ会回复。晴朗天气时和受到\n火属性的招式时，ＨＰ会减少。"}, "download": {"name": "下载", "description": "比较对手的防御和特防，根据较低\n的那项能力相应地提高自己的攻击\n或特攻。"}, "ironFist": {"name": "铁拳", "description": "使用拳类招式的威力会提高。"}, "poisonHeal": {"name": "毒疗", "description": "变为中毒状态时，ＨＰ不会减少，\n反而会增加起来。"}, "adaptability": {"name": "适应力", "description": "与自身同属性的招式威力会提高。"}, "skillLink": {"name": "连续攻击", "description": "如果使用连续招式，总是能使出最\n高次数。"}, "hydration": {"name": "湿润之躯", "description": "下雨天气时，异常状态会治愈。"}, "solarPower": {"name": "太阳之力", "description": "晴朗天气时，特攻会提高，而每回\n合ＨＰ会减少。"}, "quickFeet": {"name": "飞毛腿", "description": "变为异常状态时，速度会提高。"}, "normalize": {"name": "一般皮肤", "description": "无论是什么属性的招式，全部会变\n为一般属性。威力会少量提高。"}, "sniper": {"name": "狙击手", "description": "击中要害时，威力会变得更强。"}, "magicGuard": {"name": "魔法防守", "description": "不会受到攻击以外的伤害。"}, "noGuard": {"name": "无防守", "description": "由于无防守战术，双方使出的招式\n都必定会击中。同时提高双打概率。"}, "stall": {"name": "慢出", "description": "使出招式的顺序必定会变为最后。"}, "technician": {"name": "技术高手", "description": "攻击时可以将低威力招式的威力提\n高。"}, "leafGuard": {"name": "叶子防守", "description": "晴朗天气时，不会变为异常状态。"}, "klutz": {"name": "笨拙", "description": "无法使用持有的道具。"}, "moldBreaker": {"name": "破格", "description": "可以不受对手特性的干扰，向对手\n使出招式。"}, "superLuck": {"name": "超幸运", "description": "因为拥有超幸运，攻击容易击中对\n手的要害。"}, "aftermath": {"name": "引爆", "description": "变为濒死时，会对接触到自己的对\n手造成伤害。"}, "anticipation": {"name": "危险预知", "description": "可以察觉到对手拥有的危险招式。"}, "forewarn": {"name": "预知梦", "description": "出场时，只读取１个对手拥有的招\n式。"}, "unaware": {"name": "纯朴", "description": "可以无视对手能力的变化，进行攻\n击。"}, "tintedLens": {"name": "有色眼镜", "description": "可以将效果不好的招式以通常的威\n力使出。"}, "filter": {"name": "过滤", "description": "受到效果绝佳的攻击时，可以减弱\n其威力。"}, "slowStart": {"name": "慢启动", "description": "在５回合内，攻击和速度减半。"}, "scrappy": {"name": "胆量", "description": "一般属性和格斗属性的招式可以击\n中幽灵属性的宝可梦。对威吓也毫\n不动摇。"}, "stormDrain": {"name": "引水", "description": "将水属性的招式引到自己身上，不\n会受到伤害，而是会提高特攻。"}, "iceBody": {"name": "冰冻之躯", "description": "下雪天气时，会缓缓回复ＨＰ。"}, "solidRock": {"name": "坚硬岩石", "description": "受到效果绝佳的攻击时，可以减弱\n其威力。"}, "snowWarning": {"name": "降雪", "description": "出场时，会将天气变为下雪。"}, "honeyGather": {"name": "采蜜", "description": "战斗结束时，有时候会捡来甜甜蜜。\n甜甜蜜会转换成金钱。"}, "frisk": {"name": "察觉", "description": "出场时，可以察觉对手的特性。"}, "reckless": {"name": "舍身", "description": "自己会因反作用力受伤的招式，其\n威力会提高。"}, "multitype": {"name": "多属性", "description": "自己的属性会根据持有的石板而改\n变。"}, "flowerGift": {"name": "花之礼", "description": "晴朗天气时，自己与同伴的攻击和\n特防能力会提高。"}, "badDreams": {"name": "梦魇", "description": "给予睡眠状态的对手伤害。"}, "pickpocket": {"name": "顺手牵羊", "description": "盗取接触到自己的对手的道具。"}, "sheerForce": {"name": "强行", "description": "招式的追加效果消失，但因此能以\n更高的威力使出招式。"}, "contrary": {"name": "唱反调", "description": "能力的变化发生逆转，原本提高时\n会降低，而原本降低时会提高。"}, "unnerve": {"name": "紧张感", "description": "让对手紧张，使其无法食用树果。"}, "defiant": {"name": "不服输", "description": "被对手降低能力时，攻击会大幅提\n高。"}, "defeatist": {"name": "软弱", "description": "ＨＰ减半时，会变得软弱，攻击和\n特攻会减半。"}, "cursedBody": {"name": "诅咒之躯", "description": "受到攻击时，有时会把对手的招式\n变为定身法状态。"}, "healer": {"name": "治愈之心", "description": "有时会治愈异常状态的同伴。"}, "friendGuard": {"name": "友情防守", "description": "可以减少我方的伤害。"}, "weakArmor": {"name": "碎裂铠甲", "description": "受到物理招式的伤害时，防御会降\n低，速度会大幅提高。"}, "heavyMetal": {"name": "重金属", "description": "自身的重量会变为２倍。"}, "lightMetal": {"name": "轻金属", "description": "自身的重量会减半。"}, "multiscale": {"name": "多重鳞片", "description": "ＨＰ全满时，受到的伤害会变少。"}, "toxicBoost": {"name": "中毒激升", "description": "变为中毒状态时，物理招式的威力\n会提高。"}, "flareBoost": {"name": "受热激升", "description": "变为灼伤状态时，特殊招式的威力\n会提高。"}, "harvest": {"name": "收获", "description": "可以多次制作出已被使用掉的树果\n。"}, "telepathy": {"name": "心灵感应", "description": "读取我方的攻击，并闪避其招式伤\n害。"}, "moody": {"name": "心情不定", "description": "每一回合，能力中的某项会大幅提\n高，而某项会降低。"}, "overcoat": {"name": "防尘", "description": "不会受到沙暴的伤害。也不会受到\n粉末类和孢子类招式的影响。"}, "poisonTouch": {"name": "毒手", "description": "只通过接触就有可能让对手变为中\n毒状态。"}, "regenerator": {"name": "再生力", "description": "退回同行队伍后，ＨＰ会少量回复\n。"}, "bigPecks": {"name": "健壮胸肌", "description": "不会受到防御降低的效果。"}, "sandRush": {"name": "拨沙", "description": "沙暴天气时，速度会提高。"}, "wonderSkin": {"name": "奇迹皮肤", "description": "成为不易受到变化招式攻击的身体\n。"}, "analytic": {"name": "分析", "description": "如果在最后使出招式，招式的威力\n会提高。"}, "illusion": {"name": "幻觉", "description": "假扮成同行队伍中的最后一只宝可\n梦出场，迷惑对手。"}, "imposter": {"name": "变身者", "description": "变身为当前面对的宝可梦。"}, "infiltrator": {"name": "穿透", "description": "可以穿透对手的壁障或替身进行攻\n击。"}, "mummy": {"name": "木乃伊", "description": "被对手接触到后，会将对手变为木\n乃伊。"}, "moxie": {"name": "自信过度", "description": "如果打倒对手，就会充满自信，攻\n击会提高。"}, "justified": {"name": "正义之心", "description": "受到恶属性的招式攻击时，因为正\n义感，攻击会提高。"}, "rattled": {"name": "胆怯", "description": "受到恶属性、幽灵属性和虫属性的\n攻击或威吓时，会因胆怯而速度提\n高。"}, "magicBounce": {"name": "魔法镜", "description": "可以不受到由对手使出的变化招式\n影响，并将其反弹。"}, "sapSipper": {"name": "食草", "description": "受到草属性的招式攻击时，不会受\n到伤害，而是攻击会提高。"}, "prankster": {"name": "恶作剧之心", "description": "可以率先使出变化招式。"}, "sandForce": {"name": "沙之力", "description": "沙暴天气时，岩石属性、地面属性\n和钢属性的招式威力会提高。"}, "ironBarbs": {"name": "铁刺", "description": "用铁刺给予接触到自己的对手伤害\n。"}, "zenMode": {"name": "达摩模式", "description": "ＨＰ变为一半以下时，样子会改变\n。"}, "victoryStar": {"name": "胜利之星", "description": "自己和同伴的命中率会提高。"}, "turboblaze": {"name": "涡轮火焰", "description": "可以不受对手特性的干扰，向对手\n使出招式。"}, "teravolt": {"name": "兆级电压", "description": "可以不受对手特性的干扰，向对手\n使出招式。"}, "aromaVeil": {"name": "芳香幕", "description": "可以防住向自己和同伴发出的心灵\n攻击。"}, "flowerVeil": {"name": "花幕", "description": "我方的草属性宝可梦能力不会降低\n，也不会变为异常状态。"}, "cheekPouch": {"name": "颊囊", "description": "无论是哪种树果，食用后，ＨＰ都\n会回复。"}, "protean": {"name": "变幻自如", "description": "变为与自己使出的招式相同的属性\n。每次出场战斗仅生效一次。"}, "furCoat": {"name": "毛皮大衣", "description": "对手给予的物理招式的伤害会减半\n。"}, "magician": {"name": "魔术师", "description": "夺走被自己的招式击中的对手的道\n具。"}, "bulletproof": {"name": "防弹", "description": "可以防住对手的球和弹类招式。"}, "competitive": {"name": "好胜", "description": "如果被对手降低能力，特攻会大幅\n提高。"}, "strongJaw": {"name": "强壮之颚", "description": "因为颚部强壮，啃咬类招式的威力\n会提高。"}, "refrigerate": {"name": "冰冻皮肤", "description": "一般属性的招式会变为冰属性。威\n力会少量提高。"}, "sweetVeil": {"name": "甜幕", "description": "自己和同伴的宝可梦不会变为睡眠\n状态。"}, "stanceChange": {"name": "战斗切换", "description": "如果使出攻击招式，会变为刀剑形\n态，如果使出招式“王者盾牌”，\n会变为盾牌形态。"}, "galeWings": {"name": "疾风之翼", "description": "ＨＰ全满时，飞行属性的招式可以\n率先使出。"}, "megaLauncher": {"name": "超级发射器", "description": "波动和波导类招式的威力会提高。"}, "grassPelt": {"name": "草之毛皮", "description": "在青草场地时，防御会提高。"}, "symbiosis": {"name": "共生", "description": "同伴使用道具时，会把自己持有的\n道具传递给同伴。"}, "toughClaws": {"name": "硬爪", "description": "接触到对手的招式威力会提高。"}, "pixilate": {"name": "妖精皮肤", "description": "一般属性的招式会变为妖精属性。\n威力会少量提高。"}, "gooey": {"name": "黏滑", "description": "对于用攻击接触到自己的对手，会\n降低其速度。"}, "aerilate": {"name": "飞行皮肤", "description": "一般属性的招式会变为飞行属性。\n威力会少量提高。"}, "parentalBond": {"name": "亲子爱", "description": "亲子俩可以合计攻击２次。"}, "darkAura": {"name": "暗黑气场", "description": "全体的恶属性招式变强。"}, "fairyAura": {"name": "妖精气场", "description": "全体的妖精属性招式变强。"}, "auraBreak": {"name": "气场破坏", "description": "让气场的效果发生逆转，降低威力\n。"}, "primordialSea": {"name": "始源之海", "description": "变为不会受到火属性攻击的天气。"}, "desolateLand": {"name": "终结之地", "description": "变为不会受到水属性攻击的天气。"}, "deltaStream": {"name": "德尔塔气流", "description": "变为令飞行属性的弱点消失的天气\n。"}, "stamina": {"name": "持久力", "description": "受到攻击时，防御会提高。"}, "wimpOut": {"name": "跃跃欲逃", "description": "ＨＰ变为一半时，会慌慌张张逃走\n，退回同行队伍中。"}, "emergencyExit": {"name": "危险回避", "description": "ＨＰ变为一半时，为了回避危险，\n会退回到同行队伍中。"}, "waterCompaction": {"name": "遇水凝固", "description": "受到水属性的招式攻击时，防御会\n大幅提高。"}, "merciless": {"name": "不仁不义", "description": "攻击中毒状态的对手时，必定会击\n中要害。"}, "shieldsDown": {"name": "界限盾壳", "description": "ＨＰ变为一半时，壳会坏掉，变得\n有攻击性。"}, "stakeout": {"name": "蹲守", "description": "可以对替换出场的对手以２倍的伤\n害进行攻击。"}, "waterBubble": {"name": "水泡", "description": "降低自己受到的火属性招式的威力\n，不会灼伤。"}, "steelworker": {"name": "钢能力者", "description": "钢属性的招式威力会提高。"}, "berserk": {"name": "怒火冲天", "description": "因对手的攻击ＨＰ变为一半时，特\n攻会提高。"}, "slushRush": {"name": "拨雪", "description": "下雪天气时，速度会提高。"}, "longReach": {"name": "远隔", "description": "可以不接触对手就使出所有的招式\n。"}, "liquidVoice": {"name": "湿润之声", "description": "所有的声音招式都变为水属性。"}, "triage": {"name": "先行治疗", "description": "可以率先使出回复招式。"}, "galvanize": {"name": "电气皮肤", "description": "一般属性的招式会变为电属性。威\n力会少量提高。"}, "surgeSurfer": {"name": "冲浪之尾", "description": "电气场地时，速度会变为２倍。"}, "schooling": {"name": "鱼群", "description": "ＨＰ多的时候会聚起来变强。ＨＰ\n剩余量变少时，群体会分崩离析。"}, "disguise": {"name": "画皮", "description": "通过画皮覆盖住身体，可以防住１\n次攻击。"}, "battleBond": {"name": "牵绊变身", "description": "打倒对手时，与训练家的牵绊会增\n强，自己的攻击、特攻、速度会提\n高。"}, "powerConstruct": {"name": "群聚变形", "description": "ＨＰ变为一半时，细胞们会赶来支\n援，变为完全体形态。"}, "corrosion": {"name": "腐蚀", "description": "可以使钢属性和毒属性的宝可梦也\n陷入中毒状态。"}, "comatose": {"name": "绝对睡眠", "description": "总是半梦半醒的状态，绝对不会醒\n来。可以就这么睡着进行攻击。\n宝可梦也被视为处于特殊状态中。"}, "queenlyMajesty": {"name": "女王的威严", "description": "向对手施加威慑力，使其无法对我\n方使出先制招式。"}, "innardsOut": {"name": "飞出的内在物", "description": "被对手打倒的时候，会给予对手相\n当于ＨＰ剩余量的伤害。"}, "dancer": {"name": "舞者", "description": "有谁使出跳舞招式时，自己也能就\n这么接着使出跳舞招式。"}, "battery": {"name": "蓄电池", "description": "会提高我方的特殊招式的威力。"}, "fluffy": {"name": "毛茸茸", "description": "会将对手所给予的接触类招式的伤\n害减半，但火属性招式的伤害会变\n为２倍。"}, "dazzling": {"name": "鲜艳之躯", "description": "让对手吓一跳，使其无法对我方使\n出先制招式。"}, "soulHeart": {"name": "魂心", "description": "宝可梦每次变为濒死状态时，特攻\n会提高。"}, "tanglingHair": {"name": "卷发", "description": "对于用攻击接触到自己的对手，会\n降低其速度。"}, "receiver": {"name": "接球手", "description": "继承被打倒的同伴的特性，变为相\n同的特性。"}, "powerOfAlchemy": {"name": "化学之力", "description": "继承被打倒的同伴的特性，变为相\n同的特性。"}, "beastBoost": {"name": "异兽提升", "description": "打倒对手的时候，自己最高的那项\n能力会提高。"}, "rksSystem": {"name": "ＡＲ系统", "description": "根据持有的存储碟，自己的属性会\n改变。"}, "electricSurge": {"name": "电气制造者", "description": "出场时，会布下电气场地。"}, "psychicSurge": {"name": "精神制造者", "description": "出场时，会布下精神场地。"}, "mistySurge": {"name": "薄雾制造者", "description": "出场时，会布下薄雾场地。"}, "grassySurge": {"name": "青草制造者", "description": "出场时，会布下青草场地。"}, "fullMetalBody": {"name": "金属防护", "description": "不会因为对手的招式或特性而被降\n低能力。"}, "shadowShield": {"name": "幻影防守", "description": "ＨＰ全满时，受到的伤害会变少。"}, "prismArmor": {"name": "棱镜装甲", "description": "受到效果绝佳的攻击时，可以减弱\n其威力。"}, "neuroforce": {"name": "脑核之力", "description": "效果绝佳的攻击，威力会变得更强\n。"}, "intrepidSword": {"name": "不挠之剑", "description": "首次出场时，攻击会提高。"}, "dauntlessShield": {"name": "不屈之盾", "description": "首次出场时，防御会提高。"}, "libero": {"name": "自由者", "description": "变为与自己使出的招式相同的属性\n。每次出场战斗仅生效一次。"}, "ballFetch": {"name": "捡球", "description": "没有携带道具时，会拾取第１个投\n出后捕捉失败的精灵球。"}, "cottonDown": {"name": "棉絮", "description": "受到攻击后撒下棉絮，降低除自己\n以外的所有宝可梦的速度。"}, "propellerTail": {"name": "螺旋尾鳍", "description": "能无视具有吸引对手招式效果的特\n性或招式的影响。"}, "mirrorArmor": {"name": "镜甲", "description": "只反弹自己受到的能力降低效果。"}, "gulpMissile": {"name": "一口导弹", "description": "冲浪或潜水时会叼来猎物。受到伤\n害时，会吐出猎物进行攻击。"}, "stalwart": {"name": "坚毅", "description": "能无视具有吸引对手招式效果的特\n性或招式的影响。"}, "steamEngine": {"name": "蒸汽机", "description": "受到水属性或火属性的招式攻击时\n，速度会巨幅提高。"}, "punkRock": {"name": "庞克摇滚", "description": "声音招式的威力会提高。受到的声\n音招式伤害会减半。"}, "sandSpit": {"name": "吐沙", "description": "受到攻击时，会刮起沙暴。"}, "iceScales": {"name": "冰鳞粉", "description": "由于有冰鳞粉的守护，受到的特殊\n攻击伤害会减半。"}, "ripen": {"name": "熟成", "description": "使树果成熟，效果变为２倍。"}, "iceFace": {"name": "结冻头", "description": "头部的冰会代替自己承受物理攻击\n，但是样子会改变。下雪时，冰会\n恢复原状。"}, "powerSpot": {"name": "能量点", "description": "只要处在相邻位置，招式的威力就\n会提高。"}, "mimicry": {"name": "拟态", "description": "宝可梦的属性会根据场地的状态而\n变化。"}, "screenCleaner": {"name": "除障", "description": "出场时，敌方和我方的光墙、反射\n壁和极光幕的效果会消失。"}, "steelySpirit": {"name": "钢之意志", "description": "我方的钢属性攻击威力会提高。"}, "perishBody": {"name": "灭亡之躯", "description": "受到接触类招式攻击时，双方都会\n在３回合后变为濒死状态。替换后\n效果消失。"}, "wanderingSpirit": {"name": "游魂", "description": "与使用接触类招式攻击自己的宝可\n梦互换特性。"}, "gorillaTactics": {"name": "一猩一意", "description": "虽然攻击会提高，但是只能使出一\n开始所选的招式。"}, "neutralizingGas": {"name": "化学变化气体", "description": "特性为化学变化气体的宝可梦在场\n时，场上所有宝可梦的特性效果都\n会消失或者无法生效。"}, "pastelVeil": {"name": "粉彩护幕", "description": "自己和同伴都不会陷入中毒的异常\n状态。"}, "hungerSwitch": {"name": "饱了又饿", "description": "每回合结束时会在满腹花纹与空腹\n花纹之间交替改变样子。"}, "quickDraw": {"name": "速击", "description": "有时能比对手先一步行动。"}, "unseenFist": {"name": "无形拳", "description": "如果使出的是接触到对手的招式，\n就可以无视守护效果进行攻击。"}, "curiousMedicine": {"name": "怪药", "description": "出场时会从贝壳撒药，将我方的能\n力变化复原。"}, "transistor": {"name": "电晶体", "description": "电属性的招式威力会提高。"}, "dragonsMaw": {"name": "龙颚", "description": "龙属性的招式威力会提高。"}, "chillingNeigh": {"name": "苍白嘶鸣", "description": "打倒对手时会用冰冷的声音嘶鸣并\n提高攻击。"}, "grimNeigh": {"name": "漆黑嘶鸣", "description": "打倒对手时会用恐怖的声音嘶鸣并\n提高特攻。"}, "asOneGlastrier": {"name": "人马一体", "description": "兼备蕾冠王的紧张感和雪暴马的苍\n白嘶鸣这两种特性。"}, "asOneSpectrier": {"name": "人马一体", "description": "兼备蕾冠王的紧张感和灵幽马的漆\n黑嘶鸣这两种特性。"}, "lingeringAroma": {"name": "甩不掉的气味", "description": "被对手接触到后，甩不掉的气味会\n沾上对手。"}, "seedSower": {"name": "掉出种子", "description": "受到攻击时，会将脚下变成青草场\n地。"}, "thermalExchange": {"name": "热交换", "description": "受到火属性的招式攻击时，攻击会\n提高，且不会陷入灼伤状态。"}, "angerShell": {"name": "愤怒甲壳", "description": "因被对手攻击而ＨＰ变为一半时，\n会因愤怒降低防御和特防。但攻击\n、特攻、速度会提高。"}, "purifyingSalt": {"name": "洁净之盐", "description": "因洁净的盐而不会陷入异常状态。\n会让幽灵属性的招式伤害减半。"}, "wellBakedBody": {"name": "焦香之躯", "description": "受到火属性的招式攻击时，不会受\n到伤害，而是会大幅提高防御。"}, "windRider": {"name": "乘风", "description": "吹起了顺风或受到风的招式攻击时\n，不会受到伤害，而是会提高攻击\n。"}, "guardDog": {"name": "看门犬", "description": "受到威吓时，攻击会提高。让替换\n宝可梦的招式和道具无效。"}, "rockyPayload": {"name": "搬岩", "description": "岩石属性的招式威力会提高。"}, "windPower": {"name": "风力发电", "description": "受到风的招式攻击时，会变为充电\n状态。"}, "zeroToHero": {"name": "全能变身", "description": "回到同行队伍后，会变为全能形态\n。"}, "commander": {"name": "发号施令", "description": "出场时，若我方当中有吃吼霸，就\n会进入其口中，并从其口中发出指\n令。还会提高双打战斗的概率。"}, "electromorphosis": {"name": "电力转换", "description": "受到伤害时，会变为充电状态。"}, "protosynthesis": {"name": "古代活性", "description": "携带着驱劲能量或天气为晴朗时，\n数值最高的能力会提高。"}, "quarkDrive": {"name": "夸克充能", "description": "携带着驱劲能量或在电气场地上时\n，数值最高的能力会提高。"}, "goodAsGold": {"name": "黄金之躯", "description": "不会氧化的坚固黄金身躯不会受到\n对手的变化招式的影响。"}, "vesselOfRuin": {"name": "灾祸之鼎", "description": "以能呼唤灾厄的鼎的力量降低除自\n己以外的宝可梦的特攻。"}, "swordOfRuin": {"name": "灾祸之剑", "description": "以能呼唤灾厄的剑的力量降低除自\n己以外的宝可梦的防御。"}, "tabletsOfRuin": {"name": "灾祸之简", "description": "以能呼唤灾厄的简的力量降低除自\n己以外的宝可梦的攻击。"}, "beadsOfRuin": {"name": "灾祸之玉", "description": "以能呼唤灾厄的勾玉的力量降低除\n自己以外的宝可梦的特防。"}, "orichalcumPulse": {"name": "绯红脉动", "description": "出场时，会将天气变为晴朗。日照\n强烈时，会通过古代的脉动升高攻\n击。"}, "hadronEngine": {"name": "强子引擎", "description": "出场时，会布下电气场地。处于电\n气场地时，会通过未来的机关升高\n特攻。"}, "opportunist": {"name": "跟风", "description": "对手的能力提高时，自己也会趁机\n同样地提高能力。"}, "cudChew": {"name": "反刍", "description": "吃了树果后，会在下一回合结束时\n从胃反刍出来再吃１次。"}, "sharpness": {"name": "锋锐", "description": "提高切割对手的招式的威力。"}, "supremeOverlord": {"name": "大将", "description": "出场时，攻击和特攻会按照目前被\n打倒的同伴数量逐渐提升，被打倒\n越多，提升越多。"}, "costar": {"name": "同台共演", "description": "出场时，复制同伴的能力变化。"}, "toxicDebris": {"name": "毒满地", "description": "受到物理招式的伤害时，会在对手\n脚下散布毒菱。"}, "armorTail": {"name": "尾甲", "description": "包裹头部的神秘尾巴使对手无法对\n我方使出先制招式。"}, "earthEater": {"name": "食土", "description": "受到地面属性的招式攻击时，不会\n受到伤害，而是会得到回复。"}, "myceliumMight": {"name": "菌丝之力", "description": "使出变化招式时，虽然行动必定会\n变慢，但能不受对手的特性妨碍。"}, "mindsEye": {"name": "心眼", "description": "一般属性和格斗属性的招式可以击\n中幽灵属性的宝可梦。无视对手的\n闪避率的变化，且命中率不会被降\n低。"}, "supersweetSyrup": {"name": "甘露之蜜", "description": "首次出场时，会散发出甜腻的蜜\n的香味来降低对手的闪避率。"}, "hospitality": {"name": "款待", "description": "出场时款待同伴，回复其少量ＨＰ\n。"}, "toxicChain": {"name": "毒锁链", "description": "凭借含有毒素的锁链的力量，有时\n能让被招式击中的对手陷入剧毒状\n态。"}, "embodyAspectTeal": {"name": "面影辉映", "description": "将回忆映于心中，让碧草面具发出\n光辉，提高自己的速度。"}, "embodyAspectWellspring": {"name": "面影辉映", "description": "将回忆映于心中，让水井面具发出\n光辉，提高自己的特防。"}, "embodyAspectHearthflame": {"name": "面影辉映", "description": "将回忆映于心中，让火灶面具发出\n光辉，提高自己的攻击。"}, "embodyAspectCornerstone": {"name": "面影辉映", "description": "将回忆映于心中，让础石面具发出\n光辉，提高自己的防御。"}, "teraShift": {"name": "太晶变形", "description": "出场时，会吸收周围的能量，变为\n太晶形态。"}, "teraShell": {"name": "太晶甲壳", "description": "甲壳蕴藏着全部属性的力量，会将\n自己ＨＰ全满时受到的伤害全都变\n为效果不好。"}, "teraformZero": {"name": "归零化境", "description": "太乐巴戈斯变为星晶形态时，蕴藏\n在它身上的力量会将天气和场地的\n影响全部归零。"}, "poisonPuppeteer": {"name": "毒傀儡", "description": "因此宝可梦的招式而陷入中毒状态的对手\n同时也会陷入混乱状态。"}}