{"atLevel": "在等级{{lv}}", "levelUp": "升级", "using": "使用{{item}} ({{tier}})", "great": "超级球", "ultra": "高级球", "connector": "，", "gender": "(仅{{gender}})\n", "timeOfDay": {"day": "在白天或黎明", "dusk": "在黄昏", "night": "在夜晚或黄昏", "dawn": "在黎明"}, "move": "学会{{move}}招式", "moveType": "学会{{type}} 属性的招式", "friendship": "高亲密度", "shedinja": "队伍中有空位", "partyType": "队伍中有{{type}}属性宝可梦", "caught": "已捕获{{species}}", "weather": "在特定天气下", "treasure": "在收集足够宝藏后", "moveUseCount": "使用 {{move}} 招式{{count}} 次后", "nature": "有指定性格", "biome": "在特定环境", "heldItem": {"deepSeaTooth": "携带深海之牙", "deepSeaScale": "携带深海之鳞"}, "forms": {"ability": "由于其特性", "item": "激活了{{item}}", "deactivateItem": "禁用了{{item}}", "timeOfDay": "在一天中的特定时间", "enter": "进入对战场地后", "leave": "离开对战场地后", "statusEffect": "在被特定状态影响下", "moveLearned": "学会{{move}}后", "moveForgotten": "忘记{{move}}后", "move": "使用{{move}}后", "tera": "因为太晶化", "teraLapse": "当太晶化结束时", "weather": "当天气正确时", "weatherRevert": "当天气变化时", "preMove": "使用特定招式前", "postMove": "使用特定招式后"}}