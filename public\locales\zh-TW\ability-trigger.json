{"blockRecoilDamage": "{{pokemonName}} 的 {{abilityName}}\n抵消了反作用力！", "badDreams": "{{pokemon<PERSON>ame}} 被折磨着！", "costar": "{{pokemonName}} 複製了 {{allyName}} 的\n能力變化！", "iceFaceAvoidedDamage": "{{pokemonNameWithAffix}} 因爲 {{abilityName}}\n避免了傷害！", "perishBody": "因爲{{pokemonName}}的{{abilityName}}\n雙方將在3回合後滅亡！", "poisonHeal": "{{pokemonName}}因{{abilityName}}\n回複了少許HP！", "trace": "{{pokemonName}}複制了{{targetName}}的\n{{abilityName}}！", "windPowerCharged": "受{{moveName}}的影響，{{pokemonName}}提升了能力！", "quickDraw": "因爲速擊效果發動，\n{{pokemonName}}比平常出招更快了！", "illusionBreak": "{{pokemon<PERSON>ame}}造成的\n幻覺解除了！", "disguiseAvoidedDamage": "{{pokemonNameWithAffix}}的畫皮脫落了！", "blockItemTheft": "{{pokemonNameWithAffix}}的{{abilityName}}\n阻止了對方奪取道具！", "typeImmunityHeal": "{{pokemonNameWithAffix}}因{{abilityName}}\n回複了少許HP！", "nonSuperEffectiveImmunity": "{{pokemonNameWithAffix}}因{{abilityName}}\n避免了傷害！", "fullHpResistType": "{{pokemonNameWithAffix}}讓甲殼綻放光輝，扭曲了屬性相剋關係！", "moveImmunity": "對{{pokemonNameWithAffix}}沒有效果！", "reverseDrain": "{{pokemonNameWithAffix}}\n吸到了汙泥漿！", "postDefendTypeChange": "{{pokemonNameWithAffix}}因{{abilityName}}\n變成了{{typeName}}屬性！", "postDefendContactDamage": "{{pokemonNameWithAffix}}的{{abilityName}}\n使對方受到了傷害！", "postDefendAbilitySwap": "{{pokemonNameWithAffix}}\n互換了各自的特性！", "postDefendAbilityGive": "因爲{{pokemonNameWithAffix}}\n對方的特性變成了{{abilityName}}！", "postDefendMoveDisable": "封住了{{pokemonNameWithAffix}}的\n{{moveName}}！", "pokemonTypeChange": "{{pokemonNameWithAffix}}\n變成了{{moveType}}屬性！", "pokemonTypeChangeRevert": "{{pokemonNameWithAffix}} 變回原來的屬性了！", "postAttackStealHeldItem": "{{pokemonNameWithAffix}}從{{defenderName}}那裏\n奪取了{{stolenItemType}}！", "postDefendStealHeldItem": "{{pokemonNameWithAffix}}從{{attackerName}}那裏\n奪取了{{stolenItemType}}！", "copyFaintedAllyAbility": "繼承了{{pokemonNameWithAffix}}的\n{{abilityName}}！", "intimidateImmunity": "{{pokemonNameWithAffix}}因{{abilityName}}沒有受到威嚇！", "postSummonAllyHeal": "{{pokemonNameWithAffix}}喝光了\n{{pokemonName}}泡的茶！", "postSummonClearAllyStats": "{{pokemonNameWithAffix}}的\n能力變化消失了！", "postSummonTransform": "{{pokemonNameWithAffix}}\n變身成了{{targetName}}！", "protectStat": "因{{pokemonNameWithAffix}}的{{abilityName}}\n{{statName}}不會降低！", "statusEffectImmunityWithName": "{{pokemonNameWithAffix}}因{{abilityName}}\n{{statusEffectName}}沒有效果！", "statusEffectImmunity": "{{pokemonNameWithAffix}}因{{abilityName}}\n異常狀態沒有效果！", "battlerTagImmunity": "{{pokemonNameWithAffix}}因{{abilityName}}\n而不會{{battlerTagName}}！", "typeImmunityPowerBoost": "{{pokemonNameWithAffix}}的{{typeName}}火焰威力提高了！", "forewarn": "{{pokemonNameWithAffix}}讀取了\n{{moveName}}！", "frisk": "{{pokemonNameWithAffix}}察覺到了\n{{opponentName}}的{{opponentAbilityName}}！", "postWeatherLapseHeal": "{{pokemonNameWithAffix}}因{{abilityName}}\n回複了少許HP！", "postWeatherLapseDamage": "{{pokemonNameWithAffix}}\n因{{abilityName}}而受到了傷害！", "postTurnLootCreateEatenBerry": "{{pokemonNameWithAffix}}\n收獲了{{berryName}}！", "postTurnHeal": "{{pokemonNameWithAffix}}因{{abilityName}}\n回複了少許HP！", "fetchBall": "{{pokemonNameWithAffix}}\n撿回了{{pokeballName}}！", "healFromBerryUse": "{{pokemonNameWithAffix}}因{{abilityName}}\n回複了HP！", "arenaTrap": "因{{pokemonNameWithAffix}}的{{abilityName}}\n而無法進行替換！", "postBattleLoot": "{{pokemonNameWithAffix}}撿到了\n{{itemName}}！", "postFaintContactDamage": "{{pokemonNameWithAffix}}的{{abilityName}}\n使對方受到了傷害！", "postFaintHpDamage": "{{pokemonNameWithAffix}}的{{abilityName}}\n使對方受到了傷害！", "postSummonPressure": "從{{pokemonNameWithAffix}}的身上\n感到了一種壓迫感！", "weatherEffectDisappeared": "天氣的影響消失了！", "postSummonMoldBreaker": "{{pokemonNameWithAffix}}\n打破了常規！", "postSummonAnticipation": "{{pokemonNameWithAffix}}\n發抖了！", "postSummonTurboblaze": "{{pokemonNameWithAffix}}\n正在釋放熾焰氣場！", "postSummonTeravolt": "{{pokemonNameWithAffix}}\n正在釋放濺射氣場！", "postSummonDarkAura": "{{pokemonNameWithAffix}}\n正在釋放暗黑氣場！", "postSummonFairyAura": "{{pokemonNameWithAffix}}\n正在釋放妖精氣場！", "postSummonAuraBreak": "{{pokemonNameWithAffix}}壓制了所有氣場！", "postSummonNeutralizingGas": "周圍充滿了\n{{pokemonNameWithAffix}}的化學變化氣體！", "postSummonAsOneGlastrier": "{{pokemonNameWithAffix}}\n同時擁有了兩種特性！", "postSummonAsOneSpectrier": "{{pokemonNameWithAffix}}\n同時擁有了兩種特性！", "postSummonVesselOfRuin": "{{pokemonNameWithAffix}}的災禍之鼎\n令周圍的寶可夢的{{statName}}減弱了！", "postSummonSwordOfRuin": "{{pokemonNameWithAffix}}的災禍之劍\n令周圍的寶可夢的{{statName}}減弱了！", "postSummonTabletsOfRuin": "{{pokemonNameWithAffix}}的災禍之簡\n令周圍的寶可夢的{{statName}}減弱了！", "postSummonBeadsOfRuin": "{{pokemonNameWithAffix}}的災禍之玉\n令周圍的寶可夢的{{statName}}減弱了！", "preventBerryUse": "{{pokemonNameWithAffix}}因太緊張\n而無法食用樹果！"}