{"menuBaseStats": "基础能力值", "menuAbilities": "特性", "menuLevelMoves": "可学会的招式", "menuEggMoves": "蛋招式", "menuTmMoves": "能使用的招式学习器", "menuBiomes": "环境", "menuNatures": "性格", "menuToggleIvs": "显示个体", "menuEvolutions": "进化", "confirmExit": "确定要退出吗？", "showNature": "显示已解锁的性格", "showBaseStats": "显示基础能力值", "showAbilities": "显示已解锁的特性和被动", "showLevelMoves": "显示升级可以学会的招式", "onlyEvolutionMove": "此招式在进化后立刻学习", "onlyRecallMove": "此招式可以用回忆蘑菇想起", "onStarterSelectMove": "此招式可以在游戏开始时选择", "byLevelUpMove": "此招式在升级时学习", "showEggMoves": "显示已解锁的蛋招式", "showTmMoves": "显示可以通过招式学习机学习的招式", "showBiomes": "显示可以获得的地点", "showNatures": "显示已解锁的性格", "showEvolutions": "显示其进化形态与特殊对战形态", "noEvolutions": "此宝可梦无进化形态或特殊对战形态", "noBiomes": "此宝可梦不会在野外出现", "noEggMoves": "此宝可梦无可用蛋招式", "noTmMoves": "此宝可梦无可学会的招式学习机", "baseTotal": "全部", "common": "常见：", "rare": "稀有：", "hidden": "隐藏：", "prevolutions": "退化：", "evolutions": "进化：", "forms": "形态：", "preBiomes": "作为进化型：", "great": "超级球", "ultra": "高级球", "rogue": "肉鸽球", "scanChooseOption": "请选择", "scanSelect": "选择", "scanCancel": "取消", "scanLabelName": "输入名称", "scanLabelMove": "输入招式", "scanLabelAbility": "输入特性", "scanLabelPassive": "输入被动", "goFilters": ": 转到筛选", "toggleDecorations": "：启用装饰", "showForms": "：显示形态", "pokemonNumber": "编号", "cycleShiny": ": 闪光", "cycleForm": ": 形态", "cycleGender": ": 性别", "cycleVariant": ": 变种", "candyUpgrade": "：购买糖果升级", "showBackSprite": "：显示背面", "showFrontSprite": "：显示正面", "gen1": "I", "gen2": "II", "gen3": "III", "gen4": "IV", "gen5": "V", "gen6": "VI", "gen7": "VII", "gen8": "VIII", "gen9": "IX", "growthRate": "成长速度：", "ability": "特性:", "passive": "被动：", "nature": "性格:", "eggMoves": "蛋招式", "addToParty": "加入队伍", "removeFromParty": "移出队伍", "toggleIVs": "显示个体值", "useCandies": "使用糖果", "unlockPassive": "解锁被动", "reduceCost": "降低花费", "sameSpeciesEgg": "兑换一颗蛋", "locked": "未解锁", "disabled": "禁用", "uncaught": "未捕获"}