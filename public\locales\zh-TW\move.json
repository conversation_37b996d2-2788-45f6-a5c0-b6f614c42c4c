{"pound": {"name": "拍擊", "effect": "使用長長的尾巴或手等拍打\n對手進行攻擊"}, "karateChop": {"name": "空手劈", "effect": "用鋒利的手刀劈向對手進行\n攻擊。容易擊中要害"}, "doubleSlap": {"name": "連環巴掌", "effect": "用連環巴掌拍打對手進行攻\n擊。連續攻擊２～５次"}, "cometPunch": {"name": "連續拳", "effect": "用拳頭怒濤般的毆打對手進\n行攻擊。連續攻擊２～５次"}, "megaPunch": {"name": "百萬噸重拳", "effect": "用充滿力量的拳頭攻擊對手"}, "payDay": {"name": "聚寶功", "effect": "向對手的身體投擲小金幣進\n行攻擊。戰鬥後可以拿到錢"}, "firePunch": {"name": "火焰拳", "effect": "用充滿火焰的拳頭攻擊對手。\n有時會讓對手陷入灼傷狀\n態"}, "icePunch": {"name": "冰凍拳", "effect": "用充滿寒氣的拳頭攻擊對手。\n有時會讓對手陷入冰凍狀\n態"}, "thunderPunch": {"name": "雷電拳", "effect": "用充滿電流的拳頭攻擊對手。\n有時會讓對手陷入麻痹狀\n態"}, "scratch": {"name": "抓", "effect": "用堅硬且無比鋒利的爪子抓\n對手進行攻擊"}, "viseGrip": {"name": "夾住", "effect": "將對手從兩側夾住，給予傷\n害"}, "guillotine": {"name": "斷頭鉗", "effect": "用大鉗子或剪刀等夾斷對手\n進行攻擊。只要命中就會一\n擊瀕死"}, "razorWind": {"name": "旋風刀", "effect": "製造風之刃，於第２回合攻\n擊對手。容易擊中要害"}, "swordsDance": {"name": "劍舞", "effect": "激烈地跳起戰舞提高氣勢。\n大幅提高自己的攻擊"}, "cut": {"name": "居合斬", "effect": "用鐮刀或爪子等切斬對手進\n行攻擊"}, "gust": {"name": "起風", "effect": "用翅膀將颳起的狂風襲向對\n手進行攻擊"}, "wingAttack": {"name": "翅膀攻擊", "effect": "大大地展開美麗的翅膀，將\n其撞向對手進行攻擊"}, "whirlwind": {"name": "吹飛", "effect": "吹飛對手，強制拉後備寶可\n夢上場。如果對手爲野生寶\n可夢，戰鬥將直接結束"}, "fly": {"name": "飛翔", "effect": "第１回合飛上天空，第２回\n合攻擊對手"}, "bind": {"name": "綁緊", "effect": "使用長長的身體或藤蔓等，\n在４～５回合內綁緊對手進\n行攻擊"}, "slam": {"name": "摔打", "effect": "使用長長的尾巴或藤蔓等摔\n打對手進行攻擊"}, "vineWhip": {"name": "藤鞭", "effect": "用如同鞭子般彎曲而細長的\n藤蔓摔打對手進行攻擊"}, "stomp": {"name": "踩踏", "effect": "用大腳踩踏對手進行攻擊。\n有時會使對手畏縮"}, "doubleKick": {"name": "二連踢", "effect": "用２隻腳踢飛對手進行攻擊。\n連續２次給予傷害"}, "megaKick": {"name": "百萬噸重踢", "effect": "使出力大無窮的重踢踢飛對\n手進行攻擊"}, "jumpKick": {"name": "飛踢", "effect": "使出高高的騰空踢攻擊對手。\n如果踢偏則自己會受到傷\n害"}, "rollingKick": {"name": "迴旋踢", "effect": "一邊使身體快速旋轉，一邊\n踢飛對手進行攻擊。有時會\n使對手畏縮"}, "sandAttack": {"name": "潑沙", "effect": "向對手臉上潑沙子，從而降\n低命中率"}, "headbutt": {"name": "頭錘", "effect": "將頭伸出，筆直地撲向對手\n進行攻擊。有時會使對手畏\n縮"}, "hornAttack": {"name": "角撞", "effect": "用尖銳的角攻擊對手"}, "furyAttack": {"name": "亂擊", "effect": "用角或喙刺向對手進行攻擊。\n連續攻擊２～５次"}, "hornDrill": {"name": "角鑽", "effect": "用旋轉的角刺入對手進行攻\n擊。只要命中就會一擊昏厥"}, "tackle": {"name": "撞擊", "effect": "用整個身體撞向對手進行攻\n擊"}, "bodySlam": {"name": "泰山壓頂", "effect": "用整個身體壓住對手進行攻\n擊。有時會讓對手陷入麻痹\n狀態"}, "wrap": {"name": "緊束", "effect": "使用長長的身體或藤蔓等，\n在４～５回合內緊束對手進\n行攻擊"}, "takeDown": {"name": "猛撞", "effect": "以驚人的氣勢撞向對手進行\n攻擊。自己也會受到少許傷\n害"}, "thrash": {"name": "大鬧一番", "effect": "在２～３回合內，亂打一氣\n地攻擊對手。大鬧一番後自\n己會陷入混亂"}, "doubleEdge": {"name": "捨身衝撞", "effect": "拼命地猛撞向對手進行攻擊。\n自己也會受到不小的傷害"}, "tailWhip": {"name": "搖尾巴", "effect": "可愛地左右搖晃尾巴，誘使\n對手疏忽大意。會降低對手\n的防禦"}, "poisonSting": {"name": "毒針", "effect": "將有毒的針刺入對手進行攻\n擊。有時會讓對手陷入中毒\n狀態"}, "twineedle": {"name": "雙針", "effect": "將２根針刺入對手，連續２\n次給予傷害。有時會讓對手\n陷入中毒狀態"}, "pinMissile": {"name": "飛彈針", "effect": "向對手發射銳針進行攻擊。\n連續攻擊２～５次"}, "leer": {"name": "瞪眼", "effect": "用犀利的眼神使其害怕，從\n而降低對手的防禦"}, "bite": {"name": "咬住", "effect": "用尖銳的牙咬住對手進行攻\n擊。有時會使對手畏縮"}, "growl": {"name": "叫聲", "effect": "讓對手聽可愛的叫聲，引開\n注意力使其疏忽，從而降低\n對手的攻擊"}, "roar": {"name": "吼叫", "effect": "放走對手，強制拉後備寶可\n夢上場。如果對手爲野生寶\n可夢，戰鬥將直接結束"}, "sing": {"name": "唱歌", "effect": "讓對手聽舒適、美妙的歌聲，\n從而陷入睡眠狀態"}, "supersonic": {"name": "超音波", "effect": "從身體發出特殊的音波，從\n而使對手混亂"}, "sonicBoom": {"name": "音爆", "effect": "將衝擊波撞向對手進行攻擊。\n必定會給予20的傷害"}, "disable": {"name": "定身法", "effect": "阻礙對手行動，之前使出的\n招式將在４回合內無法使用"}, "acid": {"name": "溶解液", "effect": "將強酸潑向對手進行攻擊。\n有時會降低對手的特防"}, "ember": {"name": "火花", "effect": "向對手發射小型火焰進行攻\n擊。有時會讓對手陷入灼傷\n狀態"}, "flamethrower": {"name": "噴射火焰", "effect": "向對手發射烈焰進行攻擊。\n有時會讓對手陷入灼傷狀態"}, "mist": {"name": "白霧", "effect": "用白霧覆蓋身體。在５回合\n內不會讓對手降低自己的能\n力"}, "waterGun": {"name": "水槍", "effect": "向對手猛烈地噴射水流進行\n攻擊"}, "hydroPump": {"name": "水炮", "effect": "向對手猛烈地噴射大量水流\n進行攻擊"}, "surf": {"name": "衝浪", "effect": "利用大浪攻擊自己周圍所有\n的寶可夢"}, "iceBeam": {"name": "冰凍光束", "effect": "向對手發射冰凍光束進行攻\n擊。有時會讓對手陷入冰凍\n狀態"}, "blizzard": {"name": "暴風雪", "effect": "將猛烈的暴風雪刮向對手進\n行攻擊。有時會讓對手陷入\n冰凍狀態"}, "psybeam": {"name": "幻象光線", "effect": "向對手發射神奇的光線進行\n攻擊。有時會使對手混亂"}, "bubbleBeam": {"name": "泡沫光線", "effect": "向對手猛烈地噴射泡沫進行\n攻擊。有時會降低對手的速\n度"}, "auroraBeam": {"name": "極光束", "effect": "向對手發射虹色光束進行攻\n擊。有時會降低對手的攻擊"}, "hyperBeam": {"name": "破壞光線", "effect": "向對手發射強烈的光線進行\n攻擊。下一回合自己將無法\n動彈"}, "peck": {"name": "啄", "effect": "用尖銳的喙或角刺向對手進\n行攻擊"}, "drillPeck": {"name": "啄鑽", "effect": "一邊旋轉，一邊將尖喙刺入\n對手進行攻擊"}, "submission": {"name": "地獄翻滾", "effect": "將對手連同自己一起摔向地\n面進行攻擊。自己也會受到\n少許傷害"}, "lowKick": {"name": "踢倒", "effect": "用力踢對手的腳，使其摔倒\n進行攻擊。對手越重，威力\n越大"}, "counter": {"name": "雙倍奉還", "effect": "從對手那裏受到物理攻擊的\n傷害將以２倍返還給同一個\n對手"}, "seismicToss": {"name": "地球上投", "effect": "利用引力將對手甩飛出去。\n給予對手和自己等級相同的\n傷害"}, "strength": {"name": "怪力", "effect": "使出渾身力氣毆打對手進行\n攻擊"}, "absorb": {"name": "吸取", "effect": "吸取對手的養分進行攻擊。\n可以回覆給予對手傷害的一\n半ＨＰ"}, "megaDrain": {"name": "超級吸取", "effect": "吸取對手的養分進行攻擊。\n可以回覆給予對手傷害的一\n半ＨＰ"}, "leechSeed": {"name": "寄生種子", "effect": "植入寄生種子後，將在每回\n合一點一點吸取對手的ＨＰ，\n從而用來回復自己的ＨＰ"}, "growth": {"name": "生長", "effect": "讓身體一下子長大，從而提\n高攻擊和特攻"}, "razorLeaf": {"name": "飛葉快刀", "effect": "飛出葉片，切斬對手進行攻\n擊。容易擊中要害"}, "solarBeam": {"name": "日光束", "effect": "第１回合收集滿滿的日光，\n第２回合發射光束進行攻擊"}, "poisonPowder": {"name": "毒粉", "effect": "撒出毒粉，從而讓對手陷入\n中毒狀態"}, "stunSpore": {"name": "麻痹粉", "effect": "撒出麻痹粉，從而讓對手陷\n入麻痹狀態"}, "sleepPowder": {"name": "催眠粉", "effect": "撒出催眠粉，從而讓對手陷\n入睡眠狀態"}, "petalDance": {"name": "花瓣舞", "effect": "在２～３回合內，散落花瓣\n攻擊對手。之後自己會陷入\n混亂"}, "stringShot": {"name": "吐絲", "effect": "用口中吐出的絲纏繞對手，\n從而大幅降低對手的速度"}, "dragonRage": {"name": "龍之怒", "effect": "將憤怒的衝擊波撞向對手進\n行攻擊。必定會給予40的\n傷害"}, "fireSpin": {"name": "火焰旋渦", "effect": "將對手困在激烈的火焰旋渦\n中，在４～５回合內進行攻\n擊"}, "thunderShock": {"name": "電擊", "effect": "發出電流刺激對手進行攻擊。\n有時會讓對手陷入麻痹狀\n態"}, "thunderbolt": {"name": "十萬伏特", "effect": "向對手發出強力電擊進行攻\n擊。有時會讓對手陷入麻痹\n狀態"}, "thunderWave": {"name": "電磁波", "effect": "向對手發出微弱的電擊，從\n而讓對手陷入麻痹狀態"}, "thunder": {"name": "打雷", "effect": "向對手劈下暴雷進行攻擊。\n有時會讓對手陷入麻痹狀態"}, "rockThrow": {"name": "落石", "effect": "拿起小岩石，投擲對手進行\n攻擊"}, "earthquake": {"name": "地震", "effect": "利用地震的衝擊，攻擊自己\n周圍所有的寶可夢"}, "fissure": {"name": "地裂", "effect": "讓對手掉落於地裂的裂縫中\n進行攻擊。只要命中就會一\n擊瀕死"}, "dig": {"name": "挖洞", "effect": "第１回合鑽入地底，第２回\n合攻擊對手"}, "toxic": {"name": "劇毒", "effect": "讓對手陷入劇毒狀態。隨着\n回合的推進，中毒傷害會增\n加"}, "confusion": {"name": "念力", "effect": "向對手發送微弱的念力進行\n攻擊。有時會使對手混亂"}, "psychic": {"name": "精神強念", "effect": "向對手發送強大的念力進行\n攻擊。有時會降低對手的特\n防"}, "hypnosis": {"name": "催眠術", "effect": "施以誘導睡意的暗示，讓對\n手陷入睡眠狀態"}, "meditate": {"name": "瑜伽姿勢", "effect": "喚醒身體深處沉睡的力量，\n從而提高自己的攻擊"}, "agility": {"name": "高速移動", "effect": "讓身體放鬆變得輕盈，以便\n高速移動。大幅提高自己的\n速度"}, "quickAttack": {"name": "電光一閃", "effect": "以迅雷不及掩耳之勢撲向對\n手。必定能夠先制攻擊"}, "rage": {"name": "憤怒", "effect": "如果在使出招式後受到攻擊\n的話，會因憤怒的力量而提\n高攻擊"}, "teleport": {"name": "瞬間移動", "effect": "當有後備寶可夢時使用，就\n可以進行替換。野生的寶可\n夢使用則會逃走"}, "nightShade": {"name": "黑夜魔影", "effect": "顯示恐怖幻影，只給予對手\n和自己等級相同的傷害"}, "mimic": {"name": "模仿", "effect": "可以將對手最後使用的招式，\n在戰鬥內變成自己的招式"}, "screech": {"name": "刺耳聲", "effect": "發出不由自主想要捂起耳朵\n的刺耳聲，從而大幅降低對\n手的防禦"}, "doubleTeam": {"name": "影子分身", "effect": "通過快速移動來製造分身，\n擾亂對手，從而提高閃避率"}, "recover": {"name": "自我再生", "effect": "讓細胞再生，從而回復自己\n最大ＨＰ的一半"}, "harden": {"name": "變硬", "effect": "全身使勁，讓身體變硬，從\n而提高自己的防禦"}, "minimize": {"name": "變小", "effect": "蜷縮身體顯得很小，從而大\n幅提高自己的閃避率"}, "smokescreen": {"name": "煙幕", "effect": "向對手噴出煙或墨汁等，從\n而降低對手的命中率"}, "confuseRay": {"name": "奇異之光", "effect": "顯示奇怪的光，擾亂對手。\n使對手混亂"}, "withdraw": {"name": "縮入殼中", "effect": "縮入殼裏保護身體，從而提\n高自己的防禦"}, "defenseCurl": {"name": "變圓", "effect": "將身體蜷曲變圓，從而提高\n自己的防禦"}, "barrier": {"name": "屏障", "effect": "製造堅固的壁障，從而大幅\n提高自己的防禦"}, "lightScreen": {"name": "光牆", "effect": "利用神奇的牆壁，在５回合\n內減弱從對手那裏受到的特\n殊攻擊的傷害"}, "haze": {"name": "黑霧", "effect": "升起黑霧，將正在場上戰鬥\n的全體寶可夢的能力變回原\n點"}, "reflect": {"name": "反射壁", "effect": "利用神奇的牆壁，在５回合\n內減弱從對手那裏受到的物\n理攻擊的傷害"}, "focusEnergy": {"name": "聚氣", "effect": "深深地吸口氣，集中精神。\n自己的攻擊會變得容易擊中\n要害"}, "bide": {"name": "忍耐", "effect": "在２回合內忍受攻擊，受到\n的傷害會２倍返還給對手"}, "metronome": {"name": "揮指", "effect": "揮動手指刺激自己的大腦，\n從許多的招式中隨機使出１\n個"}, "mirrorMove": {"name": "鸚鵡學舌", "effect": "模仿對手使用的招式，自己\n也使用相同招式"}, "selfDestruct": {"name": "自爆", "effect": "引發爆炸，攻擊自己周圍所\n有的寶可夢。使用後陷入瀕\n死"}, "eggBomb": {"name": "炸蛋", "effect": "向對手用力投擲大大的蛋進\n行攻擊"}, "lick": {"name": "舌舔", "effect": "用長長的舌頭，舔遍對手進\n行攻擊。有時會讓對手陷入\n麻痹狀態"}, "smog": {"name": "濁霧", "effect": "將骯髒的濃霧吹向對手進行\n攻擊。有時會讓對手陷入中\n毒狀態"}, "sludge": {"name": "污泥攻擊", "effect": "用污泥投擲對手進行攻擊。\n有時會讓對手陷入中毒狀態"}, "boneClub": {"name": "骨棒", "effect": "用手中的骨頭毆打對手進行\n攻擊。有時會使對手畏縮"}, "fireBlast": {"name": "大字爆炎", "effect": "用大字形狀的火焰燒盡對手。\n有時會讓對手陷入灼傷狀\n態"}, "waterfall": {"name": "攀瀑", "effect": "以驚人的氣勢撲向對手。有\n時會使對手畏縮"}, "clamp": {"name": "貝殼夾擊", "effect": "用非常堅固且厚實的貝殼，\n在４～５回合內夾住對手進\n行攻擊"}, "swift": {"name": "高速星星", "effect": "發射星形的光攻擊對手。攻\n擊必定會命中"}, "skullBash": {"name": "火箭頭錘", "effect": "第１回合把頭縮進去，從而\n提高防禦。第２回合攻擊對\n手"}, "spikeCannon": {"name": "尖刺加農炮", "effect": "向對手發射銳針進行攻擊。\n連續攻擊２～５次"}, "constrict": {"name": "纏繞", "effect": "用觸手或青藤等纏繞進行攻\n擊。有時會降低對手的速度"}, "amnesia": {"name": "瞬間失憶", "effect": "將頭腦清空，瞬間忘記某事，\n從而大幅提高自己的特防"}, "kinesis": {"name": "折彎湯匙", "effect": "折彎湯匙引開注意，從而降\n低對手的命中率"}, "softBoiled": {"name": "生蛋", "effect": "回覆自己最大ＨＰ的一半"}, "highJumpKick": {"name": "飛膝踢", "effect": "跳起後用膝蓋撞對手進行攻\n擊。如果撞偏則自己會受到\n傷害"}, "glare": {"name": "大蛇瞪眼", "effect": "用腹部的花紋使對手害怕，\n從而讓其陷入麻痹狀態"}, "dreamEater": {"name": "食夢", "effect": "喫掉正在睡覺的對手的夢進\n行攻擊。回覆對手所受到傷\n害的一半ＨＰ"}, "poisonGas": {"name": "毒瓦斯", "effect": "將毒瓦斯吹到對手的臉上，\n從而讓對手陷入中毒狀態"}, "barrage": {"name": "投球", "effect": "向對手投擲圓形物體進行攻\n擊。連續攻擊２～５次"}, "leechLife": {"name": "吸血", "effect": "吸取血液攻擊對手。可以回\n復給予對手傷害的一半ＨＰ"}, "lovelyKiss": {"name": "惡魔之吻", "effect": "用恐怖的臉強吻對手。讓對\n手陷入睡眠狀態"}, "skyAttack": {"name": "神鳥猛擊", "effect": "第２回合攻擊對手。偶爾使\n對手畏縮。也容易擊中要害"}, "transform": {"name": "變身", "effect": "變身成對手寶可夢的樣子，\n能夠使用和對手完全相同的\n招式"}, "bubble": {"name": "泡沫", "effect": "向對手用力吹起無數泡泡進\n行攻擊。有時會降低對手的\n速度"}, "dizzyPunch": {"name": "迷昏拳", "effect": "有節奏地出拳攻擊對手。有\n時會使對手混亂"}, "spore": {"name": "蘑菇孢子", "effect": "沙沙沙地撒滿具有催眠效果\n的孢子，從而讓對手陷入睡\n眠狀態"}, "flash": {"name": "閃光", "effect": "使出光芒，從而降低對手的\n命中率。也可在陰暗的洞窟\n裏照亮四周"}, "psywave": {"name": "精神波", "effect": "向對手發射神奇的念波進行\n攻擊。每次使用，傷害都會\n改變"}, "splash": {"name": "躍起", "effect": "也不攻擊只是一蹦一蹦地跳，\n什麼都不會發生…"}, "acidArmor": {"name": "溶化", "effect": "通過細胞的變化進行液化，\n從而大幅提高自己的防禦"}, "crabhammer": {"name": "蟹鉗錘", "effect": "用大鉗子敲打對手進行攻擊。\n容易擊中要害"}, "explosion": {"name": "大爆炸", "effect": "引發大爆炸，攻擊自己周圍\n所有的寶可夢。使用後自己\n會陷入瀕死"}, "furySwipes": {"name": "亂抓", "effect": "用爪子或鐮刀等抓對手進行\n攻擊。連續攻擊２～５次"}, "bonemerang": {"name": "骨頭回力鏢", "effect": "用手中的骨頭投擲對手，來\n回連續２次給予傷害"}, "rest": {"name": "睡覺", "effect": "連續睡上２回合。回覆自己\n的全部ＨＰ以及治癒所有異\n常狀態"}, "rockSlide": {"name": "岩崩", "effect": "將大岩石猛烈地撞向對手進\n行攻擊。有時會使對手畏縮"}, "hyperFang": {"name": "必殺門牙", "effect": "用鋒利的門牙牢牢地咬住對\n手進行攻擊。有時會使對手\n畏縮"}, "sharpen": {"name": "稜角化", "effect": "增加身體的角，變得棱棱角\n角，從而提高自己的攻擊"}, "conversion": {"name": "紋理", "effect": "將自己的屬性轉換成和已學\n會的招式中第一個招式相同\n的屬性"}, "triAttack": {"name": "三重攻擊", "effect": "用３種光線進行攻擊。有時\n會讓對手陷入麻痹、灼傷或\n冰凍的狀態"}, "superFang": {"name": "憤怒門牙", "effect": "用鋒利的門牙猛烈地咬住對\n手進行攻擊。對手的ＨＰ減\n半"}, "slash": {"name": "劈開", "effect": "用爪子或鐮刀等劈開對手進\n行攻擊。容易擊中要害"}, "substitute": {"name": "替身", "effect": "削減少許自己的ＨＰ，製造\n分身。分身將成爲自己的替\n身"}, "struggle": {"name": "掙扎", "effect": "當自己的ＰＰ耗盡時，努力\n掙扎攻擊對手。自己也會受\n到少許傷害"}, "sketch": {"name": "寫生", "effect": "將對手使用的招式變成自己\n的招式。使用１次後寫生消\n失"}, "tripleKick": {"name": "三連踢", "effect": "連續３次踢對手進行攻擊。\n每踢中一次，威力就會提高"}, "thief": {"name": "小偷"}, "spiderWeb": {"name": "蛛網", "effect": "將黏糊糊的細絲一層一層纏\n住對手，使其不能從戰鬥中\n逃走"}, "mindReader": {"name": "心之眼", "effect": "用心感受對手的行動，下次\n攻擊必定會擊中對手"}, "nightmare": {"name": "惡夢", "effect": "讓在睡眠狀態下的對手做惡\n夢，每回合會緩緩減少HP"}, "flameWheel": {"name": "火焰輪", "effect": "讓火焰覆蓋全身，猛撞向對\n手進行攻擊。有時會讓對手\n陷入灼傷狀態"}, "snore": {"name": "打鼾", "effect": "在自己睡覺時，發出噪音進\n行攻擊。有時會使對手畏縮"}, "curse": {"name": "詛咒"}, "flail": {"name": "抓狂", "effect": "抓狂般亂打進行攻擊。自己\n的ＨＰ越少，招式的威力越\n大"}, "conversion2": {"name": "紋理２", "effect": "爲了可以抵抗對手最後使用\n的招式，從而使自己的屬性\n發生變化"}, "aeroblast": {"name": "氣旋攻擊", "effect": "發射空氣旋渦進行攻擊。容\n易擊中要害"}, "cottonSpore": {"name": "棉孢子", "effect": "將棉花般柔軟的孢子緊貼對\n手，從而大幅降低對手的速\n度"}, "reversal": {"name": "起死回生", "effect": "竭盡全力進行攻擊。自己的\nＨＰ越少，招式的威力越大"}, "spite": {"name": "怨恨", "effect": "對對手最後使用的招式懷有\n怨恨，減少４ＰＰ該招式"}, "powderSnow": {"name": "細雪", "effect": "將冰冷的細雪吹向對手進行\n攻擊。有時會讓對手陷入冰\n凍狀態"}, "protect": {"name": "守住", "effect": "完全抵擋對手的攻擊。連續\n使出則容易失敗"}, "machPunch": {"name": "音速拳", "effect": "以迅雷不及掩耳之勢出拳。\n必定能夠先制攻擊"}, "scaryFace": {"name": "鬼面", "effect": "用恐怖的表情瞪着對手，使\n其害怕，從而大幅降低對手\n的速度"}, "feintAttack": {"name": "出奇一擊", "effect": "悄悄地靠近對手，趁其不備\n進行毆打。攻擊必定會命中"}, "sweetKiss": {"name": "天使之吻", "effect": "像天使般可愛地親吻對手，\n從而使對手混亂"}, "bellyDrum": {"name": "腹鼓", "effect": "將自己的ＨＰ減少到最大\nＨＰ的一半，從而最大限度提\n高自己的攻擊"}, "sludgeBomb": {"name": "污泥炸彈", "effect": "用污泥投擲對手進行攻擊。\n有時會讓對手陷入中毒狀態"}, "mudSlap": {"name": "擲泥", "effect": "向對手的臉等投擲泥塊進行\n攻擊。會降低對手的命中率"}, "octazooka": {"name": "章魚桶炮", "effect": "向對手的臉等噴出墨汁進行\n攻擊。有時會降低對手的命\n中率"}, "spikes": {"name": "撒菱", "effect": "在對手的腳下扔撒菱。對替\n換出場的對手的寶可夢給予\n傷害"}, "zapCannon": {"name": "電磁炮", "effect": "發射大炮一樣的電流進行攻\n擊。讓對手陷入麻痹狀態"}, "foresight": {"name": "識破", "effect": "使出後對幽靈屬性寶可夢沒\n有效果的招式以及閃避率高\n的對手，變得能夠打中"}, "destinyBond": {"name": "同命", "effect": "使出招式後，當受到對手攻\n擊陷入瀕死時，對手也會一\n同瀕死。連續使出則會失敗"}, "perishSong": {"name": "滅亡之歌", "effect": "傾聽歌聲的寶可夢經過３回\n合陷入瀕死。替換後效果消\n失"}, "icyWind": {"name": "冰凍之風", "effect": "將結冰的冷氣吹向對手進行\n攻擊。會降低對手的速度"}, "detect": {"name": "看穿", "effect": "完全抵擋對手的攻擊。連續\n使出則容易失敗"}, "boneRush": {"name": "骨棒亂打", "effect": "用堅硬的骨頭毆打對手進行\n攻擊。連續攻擊２～５次"}, "lockOn": {"name": "鎖定", "effect": "緊緊瞄準對手，下次攻擊必\n定會打中"}, "outrage": {"name": "逆鱗", "effect": "在２～３回合內，亂打一氣\n地進行攻擊。大鬧一番後自\n己會陷入混亂"}, "sandstorm": {"name": "沙暴", "effect": "在５回合內揚起沙暴，除巖\n石、地面和鋼屬性以外的寶\n可夢，都會受到傷害。岩石\n屬性的特防還會提高"}, "gigaDrain": {"name": "終極吸取", "effect": "吸取對手的養分進行攻擊。\n可以回覆給予對手傷害的一\n半ＨＰ"}, "endure": {"name": "挺住", "effect": "即使受到攻擊，也至少會留\n下１ＨＰ。連續使出則容易\n失敗"}, "charm": {"name": "撒嬌", "effect": "可愛地凝視，誘使對手疏忽\n大意，從而大幅降低對手的\n攻擊"}, "rollout": {"name": "滾動", "effect": "在５回合內連續滾動攻擊對\n手。招式每次擊中，威力就\n會提高"}, "falseSwipe": {"name": "點到爲止", "effect": "對手的ＨＰ至少會留下１\nＨＰ，如此般手下留情地攻擊"}, "swagger": {"name": "虛張聲勢", "effect": "激怒對手，使其混亂。因爲\n憤怒，對手的攻擊會大幅提\n高"}, "milkDrink": {"name": "喝牛奶", "effect": "回覆自己最大ＨＰ的一半"}, "spark": {"name": "電光", "effect": "讓電流覆蓋全身，猛撞向對\n手進行攻擊。有時會讓對手\n陷入麻痹狀態"}, "furyCutter": {"name": "連斬", "effect": "用鐮刀或爪子等切斬對手進\n行攻擊。連續擊中，威力就\n會提高"}, "steelWing": {"name": "鋼翼", "effect": "用堅硬的翅膀敲打對手進行\n攻擊。有時會提高自己的防\n御"}, "meanLook": {"name": "黑色目光", "effect": "用好似要勾人心魂的黑色目\n光一動不動地凝視對手，使\n其不能從戰鬥中逃走"}, "attract": {"name": "迷人", "effect": "♂誘惑♀或♀誘惑♂，讓對\n手着迷。對手將很難使出招\n式"}, "sleepTalk": {"name": "夢話", "effect": "從自己已學會的招式中任意\n使出１個。只能在自己睡覺\n時使用"}, "healBell": {"name": "治癒鈴聲", "effect": "讓同伴聽舒適的鈴音，從而\n治癒我方全員的異常狀態"}, "return": {"name": "報恩", "effect": "爲了訓練家而全力攻擊對手。\n親密度越高，威力越大"}, "present": {"name": "禮物", "effect": "遞給對手設有圈套的盒子進\n行攻擊。也有可能回覆對手\nＨＰ"}, "frustration": {"name": "遷怒", "effect": "爲了發泄不滿而全力攻擊對\n手。親密度越低，威力越大"}, "safeguard": {"name": "神祕守護", "effect": "在５回合內被神奇的力量守\n護，從而不會陷入異常狀態"}, "painSplit": {"name": "分擔痛楚", "effect": "將自己的ＨＰ和對手的ＨＰ\n相加，然後自己和對手友好\n地平分"}, "sacredFire": {"name": "神聖之火", "effect": "用神祕的火焰燒盡對手進行\n攻擊。有時會讓對手陷入灼\n傷狀態"}, "magnitude": {"name": "震級", "effect": "晃動地面，攻擊自己周圍所\n有的寶可夢。招式的威力會\n有各種變化"}, "dynamicPunch": {"name": "爆裂拳", "effect": "使出渾身力氣出拳進行攻擊。\n必定會使對手混亂"}, "megahorn": {"name": "超級角擊", "effect": "用堅硬且華麗的角狠狠地刺\n入對手進行攻擊"}, "dragonBreath": {"name": "龍息", "effect": "將強烈的氣息吹向對手進行\n攻擊。有時會讓對手陷入麻\n痹狀態"}, "batonPass": {"name": "接棒", "effect": "和後備寶可夢進行替換。換\n上的寶可夢能直接繼承其能\n力的變化"}, "encore": {"name": "再來一次", "effect": "讓對手接受再來一次，連續\n３次使出最後使用的招式"}, "pursuit": {"name": "追打", "effect": "當對手替換寶可夢上場時使\n出此招式的話，能夠以２倍\n的威力進行攻擊"}, "rapidSpin": {"name": "高速旋轉", "effect": "通過旋轉來攻擊對手。可以\n擺脫綁緊、緊束、寄生種子\n等招式。還能提高自己的速\n度"}, "sweetScent": {"name": "甜甜香氣", "effect": "用香氣大幅降低對手的閃避\n率"}, "ironTail": {"name": "鐵尾", "effect": "使用堅硬的尾巴摔打對手進\n行攻擊。有時會降低對手的\n防禦"}, "metalClaw": {"name": "金屬爪", "effect": "用鋼鐵之爪劈開對手進行攻\n擊。有時會提高自己的攻擊"}, "vitalThrow": {"name": "借力摔", "effect": "會在對手之後進行攻擊。但\n是自己的攻擊必定會命中"}, "morningSun": {"name": "晨光", "effect": "回覆自己的ＨＰ。根據天氣\n的不同，回覆量也會有所變\n化"}, "synthesis": {"name": "光合作用", "effect": "回覆自己的ＨＰ。根據天氣\n的不同，回覆量也會有所變\n化"}, "moonlight": {"name": "月光", "effect": "回覆自己的ＨＰ。根據天氣\n的不同，回覆量也會有所變\n化"}, "hiddenPower": {"name": "覺醒力量", "effect": "招式的屬性會隨着使用此招\n式的寶可夢而改變"}, "crossChop": {"name": "十字劈", "effect": "用兩手呈十字劈打對手進行\n攻擊。容易擊中要害"}, "twister": {"name": "龍捲風", "effect": "興起龍捲風，將對手卷入進\n行攻擊。有時會使對手畏縮"}, "rainDance": {"name": "求雨", "effect": "在５回合內一直降雨，從而\n提高水屬性的招式威力。火\n屬性的招式威力則降低"}, "sunnyDay": {"name": "大晴天", "effect": "在５回合內讓日照變得強烈，\n從而提高火屬性的招式威\n力。水屬性的招式威力則降\n低"}, "crunch": {"name": "咬碎", "effect": "用利牙咬碎對手進行攻擊。\n有時會降低對手的防禦"}, "mirrorCoat": {"name": "鏡面反射", "effect": "從對手那裏受到特殊攻擊的\n傷害將以２倍返還給同一個\n對手"}, "psychUp": {"name": "自我暗示", "effect": "向自己施以自我暗示，將能\n力變化的狀態變得和對手一\n樣"}, "extremeSpeed": {"name": "神速", "effect": "以迅雷不及掩耳之勢猛撞向\n對手進行攻擊。必定能夠先\n制攻擊"}, "ancientPower": {"name": "原始之力", "effect": "用原始之力進行攻擊。有時\n會提高自己所有的能力"}, "shadowBall": {"name": "暗影球", "effect": "投擲一團黑影進行攻擊。有\n時會降低對手的特防"}, "futureSight": {"name": "預知未來", "effect": "在使用招式２回合後，向對\n手發送一團念力進行攻擊"}, "rockSmash": {"name": "碎岩", "effect": "用拳頭進行攻擊。有時會降\n低對手的防禦"}, "whirlpool": {"name": "潮旋", "effect": "將對手困在激烈的水流旋渦\n中，在４～５回合內進行攻\n擊"}, "beatUp": {"name": "圍攻", "effect": "我方全員進行攻擊。同行的\n寶可夢越多，招式的攻擊次\n數越多"}, "fakeOut": {"name": "擊掌奇襲", "effect": "進行先制攻擊，使對手畏縮。\n要在出場後立刻使出才能\n成功"}, "uproar": {"name": "吵鬧", "effect": "在３回合內大吵大鬧攻擊對\n手。在此期間誰都不能入眠"}, "stockpile": {"name": "蓄力", "effect": "積蓄力量，提高自己的防禦\n和特防。最多積蓄３次"}, "spitUp": {"name": "噴出", "effect": "將積蓄的力量撞向對手進行\n攻擊。積蓄得越多，威力越\n大"}, "swallow": {"name": "吞下", "effect": "將積蓄的力量吞下，從而回\n復自己的ＨＰ。積蓄得越多，\n回覆越大"}, "heatWave": {"name": "熱風", "effect": "將炎熱的氣息吹向對手進行\n攻擊。有時會讓對手陷入灼\n傷狀態"}, "hail": {"name": "冰雹", "effect": "在５回合內一直降冰雹，除\n冰屬性的寶可夢以外，給予\n全體寶可夢傷害"}, "torment": {"name": "無理取鬧", "effect": "向對手無理取鬧，令其不能\n連續２次使出相同招式"}, "flatter": {"name": "吹捧", "effect": "吹捧對手，使其混亂。同時\n還會提高對手的特攻"}, "willOWisp": {"name": "鬼火", "effect": "放出怪異的火焰，從而讓對\n手陷入灼傷狀態"}, "memento": {"name": "臨別禮物", "effect": "雖然會使自己陷入昏厥，但\n是能夠大幅降低對手的攻擊\n和特攻"}, "facade": {"name": "硬撐", "effect": "當自己處於中毒、麻痹、灼\n傷狀態時，向對手使出此招\n式的話，威力會變成２倍"}, "focusPunch": {"name": "真氣拳", "effect": "集中精神出拳。在招式使出\n前若受到攻擊則會失敗"}, "smellingSalts": {"name": "清醒", "effect": "對於麻痹狀態下的對手，威\n力會變成２倍。但相反對手\n的麻痹也會被治癒"}, "followMe": {"name": "看我嘛", "effect": "引起對手的注意，將對手的\n攻擊全部轉移到自己身上"}, "naturePower": {"name": "自然之力", "effect": "用自然之力進行攻擊。根據\n所使用場所的不同，使出的\n招式也會有所變化"}, "charge": {"name": "充電", "effect": "變爲充電狀態，提高下次使\n出的電屬性的招式威力。自\n己的特防也會提高"}, "taunt": {"name": "挑釁", "effect": "使對手憤怒。在３回合內讓\n對手只能使出給予傷害的招\n式"}, "helpingHand": {"name": "幫助", "effect": "幫助夥伴。被幫助的寶可夢，\n其招式威力變得比平時大"}, "trick": {"name": "戲法", "effect": "抓住對手的空隙，交換自己\n和對手的持有物"}, "rolePlay": {"name": "扮演", "effect": "扮演對手，讓自己的特性變\n得和對手相同"}, "wish": {"name": "祈願", "effect": "在下一回合回覆自己或是替\n換出場的寶可夢最大ＨＰ的\n一半"}, "assist": {"name": "藉助", "effect": "向同伴緊急求助，從我方寶\n可夢已學會的招式中隨機使\n用１個"}, "ingrain": {"name": "扎根", "effect": "在大地上扎根，每回合回覆\n自己的ＨＰ。因爲扎根了，\n所以不能替換寶可夢"}, "superpower": {"name": "蠻力", "effect": "發揮驚人的力量攻擊對手。\n自己的攻擊和防禦會降低"}, "magicCoat": {"name": "魔法反射", "effect": "當對手使出會變成異常狀態\n的招式或寄生種子等時，會\n將對手的招式反射回去"}, "recycle": {"name": "回收利用", "effect": "使戰鬥中已經消耗掉的自己\n的持有物再生，並可以再次\n使用"}, "revenge": {"name": "報復", "effect": "如果受到對手的招式攻擊，\n就能給予對手２倍的傷害"}, "brickBreak": {"name": "劈瓦", "effect": "將手刀猛烈地揮下攻擊對手。\n還可以破壞光牆和反射壁\n等"}, "yawn": {"name": "哈欠", "effect": "打個大哈欠引起睡意。在下\n一回合讓對手陷入睡眠狀態"}, "knockOff": {"name": "拍落", "effect": "拍落對手的持有物，直到戰\n鬥結束都不能使用。對手攜\n帶道具時會增加傷害"}, "endeavor": {"name": "蠻幹", "effect": "給予傷害，使對手的ＨＰ變\n得和自己的ＨＰ一樣"}, "eruption": {"name": "噴火", "effect": "爆發怒火攻擊對手。自己的\nＨＰ越少，招式的威力越小"}, "skillSwap": {"name": "特性互換", "effect": "利用超能力互換自己和對手\n的特性"}, "imprison": {"name": "封印", "effect": "如果對手有和自己相同的招\n式，那麼只有對手無法使用\n該招式"}, "refresh": {"name": "煥然一新", "effect": "讓身體休息，治癒自己身上\n所中的毒、麻痹、灼傷的異\n常狀態"}, "grudge": {"name": "怨念", "effect": "因對手的招式而陷入昏厥時\n給對手施加怨念，讓該招式\n的ＰＰ變成０"}, "snatch": {"name": "搶奪", "effect": "將對手打算使用的回覆招式\n或能力變化招式奪爲己用"}, "secretPower": {"name": "祕密之力", "effect": "根據使用場所不同，該招式\n的追加效果也會有所變化"}, "dive": {"name": "潛水", "effect": "第１回合潛入水中，第２回\n合浮上來進行攻擊"}, "armThrust": {"name": "猛推", "effect": "用張開着的雙手猛推對手進\n行攻擊。連續攻擊２～５次"}, "camouflage": {"name": "保護色", "effect": "根據所在場所不同，如水邊\n、草叢和洞窟等，可以改變\n自己的屬性"}, "tailGlow": {"name": "螢火", "effect": "凝視閃爍的光芒，集中自己\n的精神，從而巨幅提高特攻"}, "lusterPurge": {"name": "潔淨光芒", "effect": "釋放耀眼的光芒進行攻擊。\n有時會降低對手的特防"}, "mistBall": {"name": "薄霧球", "effect": "用圍繞着霧狀羽毛的球進行\n攻擊。有時會降低對手的特\n攻"}, "featherDance": {"name": "羽毛舞", "effect": "撒出羽毛，籠罩在對手的周\n圍。大幅降低對手的攻擊"}, "teeterDance": {"name": "搖晃舞", "effect": "搖搖晃晃地跳起舞蹈，讓自\n己周圍的寶可夢陷入混亂狀\n態"}, "blazeKick": {"name": "火焰踢", "effect": "攻擊對手後，有時會使其陷\n入灼傷狀態。也容易擊中要\n害"}, "mudSport": {"name": "玩泥巴", "effect": "一旦使用此招式，周圍就會\n弄得到處是泥。在５回合內\n減弱電屬性的招式"}, "iceBall": {"name": "冰球", "effect": "在５回合內攻擊對手。招式\n每次擊中，威力就會提高"}, "needleArm": {"name": "尖刺臂", "effect": "用帶刺的手臂猛烈地揮舞進\n行攻擊。有時會使對手畏縮"}, "slackOff": {"name": "偷懶", "effect": "偷懶休息。回覆自己最大\nＨＰ的一半"}, "hyperVoice": {"name": "巨聲", "effect": "給予對手又吵又響的巨大震\n動進行攻擊"}, "poisonFang": {"name": "劇毒牙", "effect": "用有毒的牙齒咬住對手進行\n攻擊。有時會使對手中劇毒"}, "crushClaw": {"name": "撕裂爪", "effect": "用堅硬的銳爪劈開對手進行\n攻擊。有時會降低對手的防\n御"}, "blastBurn": {"name": "爆炸烈焰", "effect": "用爆炸的火焰燒盡對手進行\n攻擊。下一回合自己將無法\n動彈"}, "hydroCannon": {"name": "加農水炮", "effect": "向對手噴射水炮進行攻擊。\n下一回合自己將無法動彈"}, "meteorMash": {"name": "彗星拳", "effect": "使出彗星般的拳頭攻擊對手。\n有時會提高自己的攻擊"}, "astonish": {"name": "驚嚇", "effect": "用尖叫聲等突然驚嚇對手進\n行攻擊。有時會使對手畏縮"}, "weatherBall": {"name": "氣象球", "effect": "根據使用時的天氣，招式屬\n性和威力會改變"}, "aromatherapy": {"name": "芳香治療", "effect": "讓同伴聞沁人心脾的香氣，\n從而治癒我方全員的異常狀\n態"}, "fakeTears": {"name": "假哭", "effect": "裝哭流淚。使對手不知所措，\n從而大幅降低對手的特防"}, "airCutter": {"name": "空氣利刃", "effect": "用銳利的風切斬對手進行攻\n擊。容易擊中要害"}, "overheat": {"name": "過熱", "effect": "使出全部力量攻擊對手。使\n用之後會因爲反作用力，自\n己的特攻大幅降低"}, "odorSleuth": {"name": "氣味偵測", "effect": "使出後對幽靈屬性寶可夢沒\n有效果的招式以及閃避率高\n的對手，變得能夠打中"}, "rockTomb": {"name": "岩石封鎖", "effect": "投擲岩石進行攻擊。封住對\n手的行動，從而降低速度"}, "silverWind": {"name": "銀色旋風", "effect": "在風中摻入鱗粉攻擊對手。\n有時會提高自己的全部能力"}, "metalSound": {"name": "金屬音", "effect": "讓對手聽摩擦金屬般討厭的\n聲音。大幅降低對手的特防"}, "grassWhistle": {"name": "草笛", "effect": "讓對手聽舒適的笛聲，從而\n陷入睡眠狀態"}, "tickle": {"name": "搔癢", "effect": "給對手搔癢，使其發笑，從\n而降低對手的攻擊和防禦"}, "cosmicPower": {"name": "宇宙力量", "effect": "汲取宇宙中神祕的力量，從\n而提高自己的防禦和特防"}, "waterSpout": {"name": "噴水", "effect": "掀起潮水進行攻擊。自己的\nＨＰ越少，招式的威力越小"}, "signalBeam": {"name": "信號光束", "effect": "發射神奇的光線進行攻擊。\n有時會使對手混亂"}, "shadowPunch": {"name": "暗影拳", "effect": "使出混影之拳。攻擊必定會\n命中"}, "extrasensory": {"name": "神通力", "effect": "發出看不見的神奇力量進行\n攻擊。有時會使對手畏縮"}, "skyUppercut": {"name": "衝天拳", "effect": "用衝向天空般高高的上勾拳\n頂起對手進行攻擊"}, "sandTomb": {"name": "流沙地獄", "effect": "將對手困在鋪天蓋地的沙暴\n中，在４～５回合內進行攻\n擊"}, "sheerCold": {"name": "絕對零度", "effect": "給對手一擊瀕死。如果是冰\n屬性以外的寶可夢使用，就\n會難以打中"}, "muddyWater": {"name": "濁流", "effect": "向對手噴射渾濁的水進行攻\n擊。有時會降低對手的命中\n率"}, "bulletSeed": {"name": "種子機關槍", "effect": "向對手猛烈地發射種子進行\n攻擊。連續攻擊２～５次"}, "aerialAce": {"name": "燕返", "effect": "以敏捷的動作戲弄對手後進\n行切斬。攻擊必定會命中"}, "icicleSpear": {"name": "冰錐", "effect": "向對手發射鋒利的冰柱進行\n攻擊。連續攻擊２～５次"}, "ironDefense": {"name": "鐵壁", "effect": "將皮膚變得堅硬如鐵，從而\n大幅提高自己的防禦"}, "block": {"name": "擋路", "effect": "張開雙手進行阻擋，封住對\n手的退路，使其不能逃走"}, "howl": {"name": "長嚎", "effect": "大聲吼叫提高氣勢，從而提\n高自己和同伴的攻擊"}, "dragonClaw": {"name": "龍爪", "effect": "用尖銳的巨爪劈開對手進行\n攻擊"}, "frenzyPlant": {"name": "瘋狂植物", "effect": "用大樹摔打對手進行攻擊。\n下一回合自己將無法動彈"}, "bulkUp": {"name": "健美", "effect": "使出全身力氣繃緊肌肉，從\n而提高自己的攻擊和防禦"}, "bounce": {"name": "彈跳", "effect": "彈跳到高高的空中，第２回\n合攻擊對手。有時會讓對手\n陷入麻痹狀態"}, "mudShot": {"name": "泥巴射擊", "effect": "向對手投擲泥塊進行攻擊。\n同時降低對手的速度"}, "poisonTail": {"name": "毒尾", "effect": "用尾巴拍打。有時會讓對手\n陷入中毒狀態，也容易擊中\n要害"}, "covet": {"name": "渴望"}, "voltTackle": {"name": "伏特攻擊", "effect": "讓電流覆蓋全身猛撞向對手。\n自己也會受到不小的傷害。\n有時會讓對手陷入麻痹狀\n態"}, "magicalLeaf": {"name": "魔法葉", "effect": "散落可以追蹤對手的神奇葉\n片。攻擊必定會命中"}, "waterSport": {"name": "玩水", "effect": "用水溼透周圍。在５回合內\n減弱火屬性的招式"}, "calmMind": {"name": "冥想", "effect": "靜心凝神，從而提高自己的\n特攻和特防"}, "leafBlade": {"name": "葉刃", "effect": "像用劍一般操縱葉片切斬對\n手進行攻擊。容易擊中要害"}, "dragonDance": {"name": "龍之舞", "effect": "激烈地跳起神祕且強有力的\n舞蹈。從而提高自己的攻擊\n和速度"}, "rockBlast": {"name": "岩石爆擊", "effect": "向對手發射堅硬的岩石進行\n攻擊。連續攻擊２～５次"}, "shockWave": {"name": "電擊波", "effect": "向對手快速發出電擊。攻擊\n必定會命中"}, "waterPulse": {"name": "水之波動", "effect": "用水的震動攻擊對手。有時\n會使對手混亂"}, "doomDesire": {"name": "破滅之願", "effect": "使用招式２回合後，會用無\n數道光束攻擊對手"}, "psychoBoost": {"name": "精神突進", "effect": "使出全部力量攻擊對手。使\n用之後會因爲反作用力，自\n己的特攻大幅降低"}, "roost": {"name": "羽棲", "effect": "降到地面，使身體休息。回\n復自己最大ＨＰ的一半"}, "gravity": {"name": "重力", "effect": "在５回合內，飄浮特性和飛\n行屬性的寶可夢會被地面屬\n性的招式擊中。飛向空中的\n招式也將無法使用"}, "miracleEye": {"name": "奇蹟之眼", "effect": "使出後對惡屬性寶可夢沒有\n效果的招式以及閃避率高的\n對手，變得能夠打中"}, "wakeUpSlap": {"name": "喚醒巴掌", "effect": "給予睡眠狀態下的對手較大\n的傷害。但相反對手會從睡\n眠中醒過來"}, "hammerArm": {"name": "臂錘", "effect": "揮舞強力而沉重的拳頭，給\n予對手傷害。自己的速度會\n降低"}, "gyroBall": {"name": "陀螺球", "effect": "讓身體高速旋轉並撞擊對手。\n速度比對手越慢，威力越\n大"}, "healingWish": {"name": "治癒之願", "effect": "雖然自己陷入瀕死，但可以\n治癒後備上場的寶可夢的異\n常狀態以及回覆ＨＰ"}, "brine": {"name": "鹽水", "effect": "當對手的ＨＰ負傷到一半左\n右時，招式威力會變成２倍"}, "naturalGift": {"name": "自然之恩", "effect": "從樹果上獲得力量進行攻擊。\n根據攜帶的樹果，招式屬\n性和威力會改變"}, "feint": {"name": "佯攻", "effect": "能夠攻擊正在使用守住或看\n穿等招式的對手。解除其守\n護效果"}, "pluck": {"name": "啄食", "effect": "用喙進行攻擊。當對手攜帶\n樹果時，可以食用並獲得其\n效果"}, "tailwind": {"name": "順風", "effect": "颳起猛烈的旋風，在４回合\n內提高我方全員的速度"}, "acupressure": {"name": "點穴", "effect": "通過點穴讓身體舒筋活絡。\n大幅提高某１項能力"}, "metalBurst": {"name": "金屬爆炸", "effect": "使出招式前，將最後受到的\n招式的傷害大力返還給對手"}, "uTurn": {"name": "急速折返", "effect": "在攻擊之後急速返回，和後\n備寶可夢進行替換"}, "closeCombat": {"name": "近身戰", "effect": "放棄守護，向對手的懷裏突\n擊。自己的防禦和特防會降\n低"}, "payback": {"name": "以牙還牙", "effect": "蓄力攻擊。如果能在對手之\n後攻擊，招式的威力會變成\n２倍"}, "assurance": {"name": "惡意追擊", "effect": "如果此回合內對手已經受到\n傷害的話，招式威力會變成\n２倍"}, "embargo": {"name": "查封", "effect": "讓對手在５回合內不能使用\n寶可夢攜帶的道具。訓練家\n也不能給那隻寶可夢使用道\n具"}, "fling": {"name": "投擲", "effect": "快速投擲攜帶的道具進行攻\n擊。根據道具不同，威力和\n效果會改變"}, "psychoShift": {"name": "精神轉移", "effect": "利用超能力施以暗示，從而\n將自己受到的異常狀態轉移\n給對手"}, "trumpCard": {"name": "王牌", "effect": "王牌招式的剩餘PP越少，\n招式的威力越大"}, "healBlock": {"name": "回覆封鎖", "effect": "在５回合內無法通過招式、\n特性或攜帶的道具來回復H\nP"}, "wringOut": {"name": "絞緊", "effect": "用力勒緊對手進行攻擊。對\n手的HP越多，威力越大"}, "powerTrick": {"name": "力量戲法", "effect": "利用超能力交換自己的攻擊\n和防禦的力量"}, "gastroAcid": {"name": "胃液", "effect": "將胃液吐向對手的身體。沾\n上的胃液會消除對手的特性\n效果"}, "luckyChant": {"name": "幸運咒語", "effect": "向天許願，從而在５回合內\n不會被對手的攻擊打中要害"}, "meFirst": {"name": "搶先一步", "effect": "提高威力，搶先使出對手想\n要使出的招式。如果不先使\n出則會失敗"}, "copycat": {"name": "仿效", "effect": "模仿對手剛纔使出的招式，\n並使出相同招式。如果對手\n還沒出招則會失敗"}, "powerSwap": {"name": "力量互換", "effect": "利用超能力互換自己和對手\n的攻擊以及特攻的能力變化"}, "guardSwap": {"name": "防守互換", "effect": "利用超能力互換自己和對手\n的防禦以及特防的能力變化"}, "punishment": {"name": "懲罰", "effect": "根據能力變化，對手提高的\n力量越大，招式的威力越大"}, "lastResort": {"name": "珍藏", "effect": "當戰鬥中已學會的招式全部\n使用過後，才能開始使出珍\n藏的招式"}, "worrySeed": {"name": "煩惱種子", "effect": "種植心神不寧的種子。使對\n手不能入眠，並將特性變成\n不眠"}, "suckerPunch": {"name": "突襲", "effect": "可以比對手先攻擊。對手使\n出的招式如果不是攻擊招式\n則會失敗"}, "toxicSpikes": {"name": "毒菱", "effect": "在對手的腳下撒毒菱。使對\n手替換出場的寶可夢中毒"}, "heartSwap": {"name": "心靈互換", "effect": "利用超能力互換自己和對手\n之間的能力變化"}, "aquaRing": {"name": "水流環", "effect": "在自己身體的周圍覆蓋用水\n製造的幕。每回合回覆ＨＰ"}, "magnetRise": {"name": "電磁飄浮", "effect": "利用電氣產生的磁力浮在空\n中。在５回合內可以飄浮"}, "flareBlitz": {"name": "閃焰衝鋒", "effect": "讓火焰覆蓋全身猛撞向對手。\n自己也會受到不小的傷害。\n有時會讓對手陷入灼傷狀\n態"}, "forcePalm": {"name": "發勁", "effect": "向對手的身體發出衝擊波進\n行攻擊。有時會讓對手陷入\n麻痹狀態"}, "auraSphere": {"name": "波導彈", "effect": "從體內產生出波導之力，然\n後向對手發出。攻擊必定會\n命中"}, "rockPolish": {"name": "岩石打磨", "effect": "打磨自己的身體，減少空氣\n阻力。可以大幅提高自己的\n速度"}, "poisonJab": {"name": "毒擊", "effect": "用帶毒的觸手或手臂刺入對\n手。有時會讓對手陷入中毒\n狀態"}, "darkPulse": {"name": "惡之波動", "effect": "從體內發出充滿惡意的恐怖\n氣場。有時會使對手畏縮"}, "nightSlash": {"name": "暗襲要害", "effect": "抓住瞬間的空隙切斬對手。\n容易擊中要害"}, "aquaTail": {"name": "水流尾", "effect": "如驚濤駭浪般揮動大尾巴攻\n擊對手"}, "seedBomb": {"name": "種子炸彈", "effect": "將外殼堅硬的大種子，從上\n方砸下攻擊對手"}, "airSlash": {"name": "空氣斬", "effect": "用連天空也能劈開的空氣之\n刃進行攻擊。有時會使對手\n畏縮"}, "xScissor": {"name": "十字剪", "effect": "將鐮刀或爪子像剪刀般地交\n叉，順勢劈開對手"}, "bugBuzz": {"name": "蟲鳴", "effect": "利用振動發出音波進行攻擊。\n有時會降低對手的特防"}, "dragonPulse": {"name": "龍之波動", "effect": "從大大的口中掀起衝擊波攻\n擊對手"}, "dragonRush": {"name": "龍之俯衝", "effect": "釋放出駭人的殺氣，一邊威\n懾一邊撞擊對手。有時會使\n對手畏縮"}, "powerGem": {"name": "力量寶石", "effect": "發射如寶石般閃耀的光芒攻\n擊對手"}, "drainPunch": {"name": "吸取拳", "effect": "用拳頭吸取對手的力量。可\n以回覆給予對手傷害的一半\nＨＰ"}, "vacuumWave": {"name": "真空波", "effect": "揮動拳頭，掀起真空波。必\n定能夠先制攻擊"}, "focusBlast": {"name": "真氣彈", "effect": "提高氣勢，釋放出全部力量。\n有時會降低對手的特防"}, "energyBall": {"name": "能量球", "effect": "發射從自然收集的生命力量。\n有時會降低對手的特防"}, "braveBird": {"name": "勇鳥猛攻", "effect": "收攏翅膀，通過低空飛行突\n擊對手。自己也會受到不小\n的傷害"}, "earthPower": {"name": "大地之力", "effect": "向對手腳下釋放出大地之力。\n有時會降低對手的特防"}, "switcheroo": {"name": "掉包", "effect": "用一閃而過的速度交換自己\n和對手的持有物"}, "gigaImpact": {"name": "終極衝擊", "effect": "使出自己渾身力量突擊對手。\n下一回合自己將無法動彈"}, "nastyPlot": {"name": "詭計", "effect": "謀劃詭計，激活頭腦。大幅\n提高自己的特攻"}, "bulletPunch": {"name": "子彈拳", "effect": "向對手使出如子彈般快速而\n堅硬的拳頭。必定能夠先制\n攻擊"}, "avalanche": {"name": "雪崩", "effect": "如果受到對手的招式攻擊，\n就能給予該對手２倍威力的\n攻擊"}, "iceShard": {"name": "冰礫", "effect": "瞬間製作冰塊，快速地扔向\n對手。必定能夠先制攻擊"}, "shadowClaw": {"name": "暗影爪", "effect": "以影子做成的銳爪，劈開對\n手。容易擊中要害"}, "thunderFang": {"name": "雷電牙", "effect": "用蓄滿電流的牙齒咬住對手。\n有時會使對手畏縮或陷入\n麻痹狀態"}, "iceFang": {"name": "冰凍牙", "effect": "用藏有冷氣的牙齒咬住對手。\n有時會使對手畏縮或陷入\n冰凍狀態"}, "fireFang": {"name": "火焰牙", "effect": "用覆蓋着火焰的牙齒咬住對\n手。有時會使對手畏縮或陷\n入灼傷狀態"}, "shadowSneak": {"name": "影子偷襲", "effect": "伸長影子，從對手的背後進\n行攻擊。必定能夠先制攻擊"}, "mudBomb": {"name": "泥巴炸彈", "effect": "向對手發射堅硬的泥彈進行\n攻擊。有時會降低對手的命\n中率"}, "psychoCut": {"name": "精神利刃", "effect": "用實體化的心之利刃劈開對\n手。容易擊中要害"}, "zenHeadbutt": {"name": "意念頭錘", "effect": "將思念的力量集中在前額進\n行攻擊。有時會使對手畏縮"}, "mirrorShot": {"name": "鏡光射擊", "effect": "拋光自己的身體，向對手釋\n放出閃光之力。有時會降低\n對手的命中率"}, "flashCannon": {"name": "加農光炮", "effect": "將身體的光芒聚集在一點釋\n放出去。有時會降低對手的\n特防"}, "rockClimb": {"name": "攀岩", "effect": "發動猛撞攻擊，有時會使對\n手混亂。是寶可表的祕傳招\n式之一"}, "defog": {"name": "清除濃霧", "effect": "用強風吹開對手的反射壁或\n光牆等。也會降低對手的閃\n避率"}, "trickRoom": {"name": "戲法空間", "effect": "製造出離奇的空間。在５回\n合內速度慢的寶可夢可以先\n行動"}, "dracoMeteor": {"name": "流星群", "effect": "從天空中向對手落下隕石。\n使用之後因爲反作用力，自\n己的特攻會大幅降低"}, "discharge": {"name": "放電", "effect": "用耀眼的電擊攻擊自己周圍\n所有的寶可夢。有時會陷入\n麻痹狀態"}, "lavaPlume": {"name": "噴煙", "effect": "用熊熊烈火攻擊自己周圍所\n有的寶可夢。有時會陷入灼\n傷狀態"}, "leafStorm": {"name": "飛葉風暴", "effect": "用尖尖的葉片向對手卷起風\n暴。使用之後因爲反作用力\n自己的特攻會大幅降低"}, "powerWhip": {"name": "強力鞭打", "effect": "激烈地揮舞青藤或觸手摔打\n對手進行攻擊"}, "rockWrecker": {"name": "岩石炮", "effect": "向對手發射巨大的岩石進行\n攻擊。下一回合自己將無法\n動彈"}, "crossPoison": {"name": "十字毒刃", "effect": "用毒刃劈開對手。有時會讓\n對手陷入中毒狀態，也容易\n擊中要害"}, "gunkShot": {"name": "垃圾射擊", "effect": "用骯髒的垃圾撞向對手進行\n攻擊。有時會讓對手陷入中\n毒狀態"}, "ironHead": {"name": "鐵頭", "effect": "用鋼鐵般堅硬的頭部進行攻\n擊。有時會使對手畏縮"}, "magnetBomb": {"name": "磁鐵炸彈", "effect": "發射吸住對手的鋼鐵炸彈。\n攻擊必定會命中"}, "stoneEdge": {"name": "尖石攻擊", "effect": "用尖尖的岩石刺入對手進行\n攻擊。容易擊中要害"}, "captivate": {"name": "誘惑", "effect": "♂誘惑♀或♀誘惑♂，從而\n大幅降低對手的特攻"}, "stealthRock": {"name": "隱形岩", "effect": "將無數岩石懸浮在對手的周\n圍，從而對替換出場的對手\n的寶可夢給予傷害"}, "grassKnot": {"name": "打草結", "effect": "用草纏住並絆倒對手。對手\n越重，威力越大"}, "chatter": {"name": "喋喋不休", "effect": "用非常煩人的，喋喋不休的\n音波攻擊對手。使對手混亂"}, "judgment": {"name": "制裁光礫", "effect": "向對手放出無數的光彈。屬\n性會根據自己攜帶的石板不\n同而改變"}, "bugBite": {"name": "蟲咬", "effect": "咬住進行攻擊。當對手攜帶\n樹果時，可以食用並獲得其\n效果"}, "chargeBeam": {"name": "充電光束", "effect": "向對手發射電擊光束。由於\n蓄滿電流，有時會提高自己\n的特攻"}, "woodHammer": {"name": "木槌", "effect": "用堅硬的軀體撞擊對手進行\n攻擊。自己也會受到不小的\n傷害"}, "aquaJet": {"name": "水流噴射", "effect": "以迅雷不及掩耳之勢撲向對\n手。必定能夠先制攻擊"}, "attackOrder": {"name": "攻擊指令", "effect": "召喚手下，讓其朝對手發起\n攻擊。容易擊中要害"}, "defendOrder": {"name": "防禦指令", "effect": "召喚手下，讓其附在自己的\n身體上。可以提高自己的防\n御和特防"}, "healOrder": {"name": "回覆指令", "effect": "召喚手下療傷。回覆自己最\n大HP的一半"}, "headSmash": {"name": "雙刃頭錘", "effect": "拼命使出渾身力氣，向對手\n進行頭錘攻擊。自己也會受\n到非常大的傷害"}, "doubleHit": {"name": "二連擊", "effect": "使用尾巴等拍打對手進行攻\n擊。連續２次給予傷害"}, "roarOfTime": {"name": "時光咆哮", "effect": "釋放出扭曲時間般的強大力\n量攻擊對手。下一回合自己\n將無法動彈"}, "spacialRend": {"name": "亞空裂斬", "effect": "將對手連同周圍的空間一起\n撕裂並給予傷害。容易擊中\n要害"}, "lunarDance": {"name": "新月舞", "effect": "雖然自己陷入昏厥，但可以\n治癒後備上場的寶可夢的全\n部狀態"}, "crushGrip": {"name": "捏碎", "effect": "用駭人的力量捏碎對手。對\n手剩餘的ＨＰ越多，威力越\n大"}, "magmaStorm": {"name": "熔岩風暴", "effect": "將對手困在熊熊燃燒的火焰\n中，在４～５回合內進行攻\n擊"}, "darkVoid": {"name": "暗黑洞", "effect": "將對手強制拖入黑暗的世界，\n從而讓對手陷入睡眠狀態"}, "seedFlare": {"name": "種子閃光", "effect": "從身體裏產生衝擊波。有時\n會大幅降低對手的特防"}, "ominousWind": {"name": "奇異之風", "effect": "突然颳起毛骨悚然的暴風攻\n擊對手。有時會提高自己的\n全部能力"}, "shadowForce": {"name": "暗影潛襲", "effect": "第１回合消失蹤影，第２回\n合攻擊對手。即使對手正受\n保護，也能擊中"}, "honeClaws": {"name": "磨爪", "effect": "將爪子磨得更加鋒利。提高\n自己的攻擊和命中率"}, "wideGuard": {"name": "廣域防守", "effect": "在１回合內防住擊打我方全\n員的攻擊"}, "guardSplit": {"name": "防守平分", "effect": "利用超能力將自己和對手的\n防禦和特防相加，再進行平\n分"}, "powerSplit": {"name": "力量平分", "effect": "利用超能力將自己和對手的\n攻擊和特攻相加，再進行平\n分"}, "wonderRoom": {"name": "奇妙空間", "effect": "製造出離奇的空間。在５回\n合內互換所有寶可夢的防禦\n和特防"}, "psyshock": {"name": "精神衝擊", "effect": "將神奇的念波實體化攻擊對\n手。給予物理傷害"}, "venoshock": {"name": "毒液衝擊", "effect": "將特殊的毒液潑向對手。對\n處於中毒狀態的對手，威力\n會變成２倍"}, "autotomize": {"name": "身體輕量化", "effect": "削掉身體上沒用的部分。大\n幅提高自己的速度，同時體\n重也會變輕"}, "ragePowder": {"name": "憤怒粉", "effect": "將令人煩躁的粉末撒在自己\n身上，用以吸引對手的注意。\n使對手的攻擊全部指向自\n己"}, "telekinesis": {"name": "意念移物", "effect": "利用超能力使對手浮起來。\n在３回合內攻擊會變得容易\n打中對手"}, "magicRoom": {"name": "魔法空間", "effect": "製造出離奇的空間。在５回\n合內所有寶可夢攜帶道具的\n效果都會消失"}, "smackDown": {"name": "擊落", "effect": "扔石頭或炮彈，攻擊飛行的\n對手。對手會被擊落，掉到\n地面"}, "stormThrow": {"name": "山嵐摔", "effect": "向對手使出強烈的一擊。攻\n擊必定會擊中要害"}, "flameBurst": {"name": "烈焰濺射", "effect": "如果擊中，爆裂的火焰會攻\n擊到對手。爆裂出的火焰還\n會飛濺到旁邊的對手"}, "sludgeWave": {"name": "污泥波", "effect": "用污泥波攻擊自己周圍所有\n的寶可夢。有時會陷入中毒\n狀態"}, "quiverDance": {"name": "蝶舞", "effect": "輕巧地跳起神祕而又美麗的\n舞蹈。提高自己的特攻、特\n防和速度"}, "heavySlam": {"name": "重磅衝撞", "effect": "用沉重的身體撞向對手進行\n攻擊。自己比對手越重，威\n力越大"}, "synchronoise": {"name": "同步干擾", "effect": "用神奇電波對周圍所有和自\n己屬性相同的寶可夢給予傷\n害"}, "electroBall": {"name": "電球", "effect": "用電氣團撞向對手。自己比\n對手速度越快，威力越大"}, "soak": {"name": "浸水", "effect": "將大量的水潑向對手，從而\n使其變成水屬性"}, "flameCharge": {"name": "蓄能焰襲", "effect": "讓火焰覆蓋全身，攻擊對手。\n積蓄力量來提高自己的速\n度"}, "coil": {"name": "盤蜷", "effect": "盤蜷着集中精神。提高自己\n的攻擊、防禦和命中率"}, "lowSweep": {"name": "下盤踢", "effect": "以敏捷的動作瞄準對手的腳\n進行攻擊。會降低對手的速\n度"}, "acidSpray": {"name": "酸液炸彈", "effect": "噴出能溶化對手的液體進行\n攻擊。會大幅降低對手的特\n防"}, "foulPlay": {"name": "欺詐", "effect": "利用對手的力量進行攻擊。\n正和自己戰鬥的對手，其攻\n擊越高，傷害越大"}, "simpleBeam": {"name": "單純光束", "effect": "向對手發送謎之念波。接收\n到念波的對手，其特性會變\n爲單純"}, "entrainment": {"name": "找夥伴", "effect": "用神奇的節奏跳舞。使對手\n模仿自己的動作，從而將特\n性變成一樣"}, "afterYou": {"name": "您先請", "effect": "支援我方或對手的行動，使\n其緊接着此招式之後行動"}, "round": {"name": "輪唱", "effect": "用歌聲攻擊對手。大家一起\n輪唱便可以接連使出，威力\n也會提高"}, "echoedVoice": {"name": "迴聲", "effect": "用回聲攻擊對手。如果每回\n合都有寶可夢接着使用該招\n式，威力就會提高"}, "chipAway": {"name": "逐步擊破", "effect": "看準機會穩步攻擊。無視對\n手的能力變化，直接給予傷\n害"}, "clearSmog": {"name": "清除之煙", "effect": "向對手投擲特殊的泥塊進行\n攻擊。使其能力變回原點"}, "storedPower": {"name": "輔助力量", "effect": "用蓄積起來的力量攻擊對手。\n自己的能力提高得越多，\n威力就越大"}, "quickGuard": {"name": "快速防守", "effect": "守護自己和同伴，以防對手\n的先制攻擊"}, "allySwitch": {"name": "交換場地", "effect": "用神奇的力量瞬間移動，互\n換自己和同伴所在的位置。\n連續使出則容易失敗"}, "scald": {"name": "熱水", "effect": "向對手噴射煮得翻滾的開水\n進行攻擊。有時會讓對手陷\n入灼傷狀態"}, "shellSmash": {"name": "破殼", "effect": "打破外殼，降低自己的防禦\n和特防，但大幅提高攻擊、\n特攻和速度"}, "healPulse": {"name": "治癒波動", "effect": "放出治癒波動，從而回復對\n手最大ＨＰ的一半"}, "hex": {"name": "禍不單行", "effect": "接二連三地進行攻擊。對處\n於異常狀態的對手給予較大\n的傷害"}, "skyDrop": {"name": "自由落體", "effect": "第１回合將對手帶到空中，\n第２回合將其摔下進行攻擊。\n被帶到空中的對手不能動\n彈"}, "shiftGear": {"name": "換檔", "effect": "轉動齒輪，不僅提高自己的\n攻擊，還會大幅提高速度"}, "circleThrow": {"name": "巴投", "effect": "扔飛對手，強制拉後備寶可\n夢上場。如果對手爲野生寶\n可夢，戰鬥將直接結束"}, "incinerate": {"name": "燒盡", "effect": "用火焰攻擊對手。對手攜帶\n樹果等時，會燒掉，使其不\n能使用"}, "quash": {"name": "延後", "effect": "壓制對手，從而將其行動順\n序放到最後"}, "acrobatics": {"name": "雜技", "effect": "輕巧地攻擊對手。自己沒有\n攜帶道具時，會給予較大的\n傷害"}, "reflectType": {"name": "鏡面屬性", "effect": "反射對手的屬性，讓自己也\n變成一樣的屬性"}, "retaliate": {"name": "報仇", "effect": "爲倒下的同伴報仇。如果上\n一回合有同伴倒下，威力就\n會提高"}, "finalGambit": {"name": "搏命", "effect": "拼命攻擊對手。雖然自己陷\n入昏厥，但會給予對手和自\n己目前ＨＰ等量的傷害"}, "bestow": {"name": "傳遞禮物", "effect": "當對手未攜帶道具時，能夠\n將自己攜帶的道具交給對手"}, "inferno": {"name": "煉獄", "effect": "用猛烈的火焰包圍對手進行\n攻擊。讓對手陷入灼傷狀態"}, "waterPledge": {"name": "水之誓約", "effect": "用水柱進行攻擊。如果和火\n組合，威力就會提高，天空\n中會掛上彩虹"}, "firePledge": {"name": "火之誓約", "effect": "用火柱進行攻擊。如果和草\n組合，威力就會提高，周圍\n會變成火海"}, "grassPledge": {"name": "草之誓約", "effect": "用草柱進行攻擊。如果和水\n組合，威力就會提高，周圍\n會變成溼地"}, "voltSwitch": {"name": "伏特替換", "effect": "在攻擊之後急速返回，和後\n備寶可夢進行替換"}, "struggleBug": {"name": "蟲之抵抗", "effect": "抵抗並攻擊對手。會降低對\n手的特攻"}, "bulldoze": {"name": "重踏", "effect": "用力踩踏地面並攻擊自己周\n圍所有的寶可夢。會降低對\n方的速度"}, "frostBreath": {"name": "冰息", "effect": "將冰冷的氣息吹向對手進行\n攻擊。必定會擊中要害"}, "dragonTail": {"name": "龍尾", "effect": "彈飛對手，強制拉後備寶可\n夢上場。如果對手爲野生寶\n可夢，戰鬥將直接結束"}, "workUp": {"name": "自我激勵", "effect": "激勵自己，從而提高攻擊和\n特攻"}, "electroweb": {"name": "電網", "effect": "用電網捉住對手進行攻擊。\n會降低對手的速度"}, "wildCharge": {"name": "瘋狂伏特", "effect": "讓電流覆蓋全身，撞向對手\n進行攻擊。自己也會受到少\n許傷害"}, "drillRun": {"name": "直衝鑽", "effect": "像鋼鑽一樣，一邊旋轉身體\n一邊撞擊對手。容易擊中要\n害"}, "dualChop": {"name": "二連劈", "effect": "用身體堅硬的部分拍打對手\n進行攻擊。連續２次給予傷\n害"}, "heartStamp": {"name": "愛心印章", "effect": "以可愛的動作使對手疏忽，\n乘機給出強烈的一擊。有時\n會使對手畏縮"}, "hornLeech": {"name": "木角", "effect": "將角刺入，吸取對手的養分。\n可以回覆給予對手傷害的\n一半ＨＰ"}, "sacredSword": {"name": "聖劍", "effect": "用劍切斬對手進行攻擊。無\n視對手的能力變化，直接給\n予傷害"}, "razorShell": {"name": "貝殼刃", "effect": "用鋒利的貝殼切斬對手進行\n攻擊。有時會降低對手的防\n御"}, "heatCrash": {"name": "高溫重壓", "effect": "用燃燒的身體撞向對手進行\n攻擊。自己比對手越重，威\n力越大"}, "leafTornado": {"name": "青草攪拌器", "effect": "用鋒利的葉片包裹住對手進\n行攻擊。有時會降低對手的\n命中率"}, "steamroller": {"name": "瘋狂滾壓", "effect": "旋轉揉成團的身體壓扁對手。\n有時會使對手畏縮"}, "cottonGuard": {"name": "棉花防守", "effect": "用軟綿綿的絨毛包裹住自己\n的身體進行守護。巨幅提高\n自己的防禦"}, "nightDaze": {"name": "暗黑爆破", "effect": "放出黑暗的衝擊波攻擊對手。\n有時會降低對手的命中率"}, "psystrike": {"name": "精神擊破", "effect": "將神奇的念波實體化攻擊對\n手。給予物理傷害"}, "tailSlap": {"name": "掃尾拍打", "effect": "用堅硬的尾巴拍打對手進行\n攻擊。連續攻擊２～５次"}, "hurricane": {"name": "暴風", "effect": "用強烈的風席捲對手進行攻\n擊。有時會使對手混亂"}, "headCharge": {"name": "爆炸頭突擊", "effect": "用厲害的爆炸頭猛撞向對手\n進行攻擊。自己也會受到少\n許傷害"}, "gearGrind": {"name": "齒輪飛盤", "effect": "向對手投擲鋼鐵齒輪進行攻\n擊。連續２次給予傷害"}, "searingShot": {"name": "火焰彈", "effect": "用熊熊烈火攻擊自己周圍所\n有的寶可夢。有時會陷入灼\n傷狀態"}, "technoBlast": {"name": "高科技光炮", "effect": "向對手放出光彈。屬性會根\n據自己攜帶的卡帶不同而改\n變"}, "relicSong": {"name": "古老之歌", "effect": "讓對手聽古老之歌，打動對\n手的內心進行攻擊。有時會\n讓對手陷入睡眠狀態"}, "secretSword": {"name": "神祕之劍", "effect": "用長角切斬對手進行攻擊。\n角上擁有的神奇力量將給予\n物理傷害"}, "glaciate": {"name": "冰封世界", "effect": "將冰凍的冷氣吹向對手進行\n攻擊。會降低對手的速度"}, "boltStrike": {"name": "雷擊", "effect": "讓強大的電流覆蓋全身，猛\n撞向對手進行攻擊。有時會\n讓對手陷入麻痹狀態"}, "blueFlare": {"name": "青焰", "effect": "用美麗而激烈的青焰包裹住\n對手進行攻擊。有時會讓對\n手陷入灼傷狀態"}, "fieryDance": {"name": "火之舞", "effect": "讓火焰覆蓋全身，振翅攻擊\n對手。有時會提高自己的特\n攻"}, "freezeShock": {"name": "冰凍伏特", "effect": "用覆蓋着電流的冰塊，在第\n２回合撞向對手。有時會讓\n對手陷入麻痹狀態"}, "iceBurn": {"name": "極寒冷焰", "effect": "用能夠凍結一切的強烈冷氣，\n在第２回合包裹住對手。\n有時會讓對手陷入灼傷狀態"}, "snarl": {"name": "大聲咆哮", "effect": "沒完沒了地大聲斥責，從而\n降低對手的特攻"}, "icicleCrash": {"name": "冰柱墜擊", "effect": "用大冰柱激烈地撞向對手進\n行攻擊。有時會使對手畏縮"}, "vCreate": {"name": "Ｖ熱焰", "effect": "從前額產生灼熱的火焰，舍\n身撞擊對手。防禦、特防和\n速度會降低"}, "fusionFlare": {"name": "交錯火焰", "effect": "釋放出巨大的火焰。受到巨\n大的閃電影響時，招式威力\n會提高"}, "fusionBolt": {"name": "交錯閃電", "effect": "釋放出巨大的閃電。受到巨\n大的火焰影響時，招式威力\n會提高"}, "flyingPress": {"name": "飛身重壓", "effect": "從空中俯衝向對手。此招式\n同時帶有格鬥屬性和飛行屬\n性"}, "matBlock": {"name": "掀榻榻米", "effect": "將掀起來的榻榻米當作盾牌，\n防住自己和同伴免受招式\n傷害。變化招式無法防住"}, "belch": {"name": "打嗝", "effect": "朝着對手打嗝，並給予傷害。\n如果不喫樹果則無法使出"}, "rototiller": {"name": "耕地", "effect": "翻耕土地，使草木更容易成\n長。會提高草屬性寶可夢的\n攻擊和特攻"}, "stickyWeb": {"name": "黏黏網", "effect": "在對手周圍圍上黏黏的網，\n降低替換出場的對手的速度"}, "fellStinger": {"name": "致命針刺", "effect": "如果使用此招式打倒對手，\n攻擊會巨幅提高"}, "phantomForce": {"name": "潛靈奇襲", "effect": "第１回合消失在某處，第２\n回合攻擊對手。可以無視守\n護進行攻擊"}, "trickOrTreat": {"name": "萬聖夜", "effect": "邀請對手參加萬聖夜。使對\n手被追加幽靈屬性"}, "nobleRoar": {"name": "戰吼", "effect": "發出戰吼威嚇對手，從而降\n低對手的攻擊和特攻"}, "ionDeluge": {"name": "等離子浴", "effect": "將帶電粒子擴散開來，使一\n般屬性的招式變成電屬性"}, "parabolicCharge": {"name": "拋物面充電", "effect": "給周圍全體寶可夢造成傷害。\n可以回覆給予傷害的一半\nHP"}, "forestsCurse": {"name": "森林咒術", "effect": "向對手施加森林咒術。中了\n咒術的對手會被追加草屬性"}, "petalBlizzard": {"name": "落英繽紛", "effect": "猛烈地颳起飛雪般的落花，\n攻擊周圍所有的寶可夢，並\n給予傷害"}, "freezeDry": {"name": "冷凍乾燥", "effect": "急劇冷凍對手，有時會讓對\n手陷入冰凍狀態。對於水屬\n性寶可夢也是效果絕佳"}, "disarmingVoice": {"name": "魅惑之聲", "effect": "發出魅惑的叫聲，給予對手\n精神上的傷害。攻擊必定會\n命中"}, "partingShot": {"name": "拋下狠話", "effect": "拋下狠話威嚇對手，降低攻\n擊和特攻後，和後備寶可夢\n進行替換"}, "topsyTurvy": {"name": "顛倒", "effect": "顛倒對手身上的所有能力變\n化，變成和原來相反的狀態"}, "drainingKiss": {"name": "吸取之吻", "effect": "用一個吻吸取對手的ＨＰ。\n回覆給予對手傷害的一半以\n上的ＨＰ"}, "craftyShield": {"name": "戲法防守", "effect": "使用神奇的力量防住攻擊我\n方的變化招式。但無法防住\n傷害招式的攻擊"}, "flowerShield": {"name": "鮮花防守", "effect": "使用神奇的力量提高在場的\n所有草屬性寶可夢的防禦"}, "grassyTerrain": {"name": "青草場地", "effect": "在５回合內變成青草場地。\n地面上的寶可夢每回合都能\n回覆。草屬性的招式威力還\n會提高"}, "mistyTerrain": {"name": "薄霧場地", "effect": "在５回合內，地面上的寶可\n夢不會陷入異常狀態。龍屬\n性招式的傷害也會減半"}, "electrify": {"name": "輸電", "effect": "對手使出招式前，如果輸電，\n則該回合對手的招式變成\n電屬性"}, "playRough": {"name": "嬉鬧", "effect": "與對手嬉鬧並攻擊。有時會\n降低對手的攻擊"}, "fairyWind": {"name": "妖精之風", "effect": "颳起妖精之風，吹向對手進\n行攻擊"}, "moonblast": {"name": "月亮之力", "effect": "借用月亮的力量攻擊對手。\n有時會降低對手的特攻"}, "boomburst": {"name": "爆音波", "effect": "通過震耳欲聾的爆炸聲產生\n的破壞力，攻擊自己周圍所\n有的寶可夢"}, "fairyLock": {"name": "妖精之鎖", "effect": "通過封鎖，下一回合所有的\n寶可夢都無法逃走"}, "kingsShield": {"name": "王者盾牌", "effect": "防住對手攻擊的同時，自己\n變爲防禦姿態。能夠降低所\n接觸到的對手的攻擊"}, "playNice": {"name": "和睦相處", "effect": "和對手和睦相處，使其失去\n戰鬥的氣力，從而降低對手\n的攻擊"}, "confide": {"name": "密語", "effect": "和對手進行密語，使其失去\n集中力，從而降低對手的特\n攻"}, "diamondStorm": {"name": "鑽石風暴", "effect": "掀起鑽石風暴給予傷害。有\n時會大幅提高自己的防禦"}, "steamEruption": {"name": "蒸汽爆炸", "effect": "將滾燙的蒸汽噴向對手。有\n時會讓對手灼傷"}, "hyperspaceHole": {"name": "異次元洞", "effect": "通過異次元洞，突然出現在\n對手的側面進行攻擊。還可\n以無視守住和看穿等招式"}, "waterShuriken": {"name": "飛水手裏劍", "effect": "用粘液製成的手裏劍，連續\n攻擊２～５次。必定能夠先\n制攻擊"}, "mysticalFire": {"name": "魔法火焰", "effect": "從口中噴出特別灼熱的火焰\n進行攻擊。降低對手的特攻"}, "spikyShield": {"name": "尖刺防守", "effect": "防住對手攻擊的同時，削減\n接觸到自己的對手的體力"}, "aromaticMist": {"name": "芳香薄霧", "effect": "通過神奇的芳香，提高我方\n寶可夢的特防"}, "eerieImpulse": {"name": "怪異電波", "effect": "從身體放射出怪異電波，讓\n對手沐浴其中，從而大幅降\n低其特攻"}, "venomDrench": {"name": "毒液陷阱", "effect": "將特殊的毒液潑向對手。對\n處於中毒狀態的對手，其攻\n擊、特攻和速度都會降低"}, "powder": {"name": "粉塵", "effect": "如果被撒到粉塵的對手使用\n火招式，則會爆炸並給予傷\n害"}, "geomancy": {"name": "大地掌控", "effect": "第１回合吸收能量，第２回\n合大幅提高特攻、特防和速\n度"}, "magneticFlux": {"name": "磁場操控", "effect": "通過操控磁場，會提高特性\n爲正電和負電的寶可夢的防\n御和特防"}, "happyHour": {"name": "歡樂時光", "effect": "如果使用歡樂時光，戰鬥後\n得到的錢會翻倍"}, "electricTerrain": {"name": "電氣場地", "effect": "在５回合內變成電氣場地。\n地面上的寶可夢將無法入眠。\n電屬性的招式威力還會提\n高"}, "dazzlingGleam": {"name": "魔法閃耀", "effect": "向對手發射強光，並給予傷\n害"}, "celebrate": {"name": "慶祝", "effect": "寶可夢爲十分開心的你慶祝"}, "holdHands": {"name": "牽手", "effect": "我方寶可夢之間牽手。能帶\n來非常幸福的心情"}, "babyDollEyes": {"name": "圓瞳", "effect": "用圓瞳凝視對手，從而降低\n其攻擊。必定能夠先制攻擊"}, "nuzzle": {"name": "蹭蹭臉頰", "effect": "將帶電的臉頰蹭蹭對手進行\n攻擊。讓對手陷入麻痹狀態"}, "holdBack": {"name": "手下留情", "effect": "在攻擊的時候手下留情，從\n而使對手的ＨＰ至少會留下\n１ＨＰ"}, "infestation": {"name": "糾纏不休", "effect": "在４～５回合內死纏爛打地\n進行攻擊。在此期間對手將\n無法逃走"}, "powerUpPunch": {"name": "增強拳", "effect": "通過反覆擊打對手，使自己\n的拳頭慢慢變硬。打中對手\n攻擊就會提高"}, "oblivionWing": {"name": "歸天之翼", "effect": "從鎖定的對手身上吸取ＨＰ。\n回覆給予對手傷害的一半\n以上的ＨＰ"}, "thousandArrows": {"name": "千箭齊發", "effect": "可以擊中浮在空中的寶可夢。\n空中的對手被擊落後，會\n掉到地面"}, "thousandWaves": {"name": "千波激盪", "effect": "從地面掀起波浪進行攻擊。\n被掀入波浪中的對手，將無\n法從戰鬥中逃走"}, "landsWrath": {"name": "大地神力", "effect": "聚集大地的力量，將此力量\n集中攻擊對手，並給予傷害"}, "lightOfRuin": {"name": "破滅之光", "effect": "借用永恆之花的力量，發射\n出強力光線。自己也會受到\n非常大的傷害"}, "originPulse": {"name": "根源波動", "effect": "用無數青白色且閃耀的光線\n攻擊對手"}, "precipiceBlades": {"name": "斷崖之劍", "effect": "將大地的力量變化爲利刃攻\n擊對手"}, "dragonAscent": {"name": "畫龍點睛", "effect": "從天空中急速下降攻擊對手。\n自己的防禦和特防會降低"}, "hyperspaceFury": {"name": "異次元猛攻", "effect": "用許多手臂，無視對手的守\n住或看穿等招式進行連續攻\n擊，自己的防禦會降低"}, "breakneckBlitzPhysical": {"name": "一般Ｚ究極無敵大沖撞", "effect": "通過Ｚ力量氣勢猛烈地全力\n撞上對手。威力會根據原來\n的招式而改變"}, "breakneckBlitzSpecial": {"name": "一般Ｚ究極無敵大沖撞", "effect": "通過Ｚ力量氣勢猛烈地全力\n撞上對手。威力會根據原來\n的招式而改變"}, "allOutPummelingPhysical": {"name": "格鬥Ｚ全力無雙激烈拳", "effect": "通過Ｚ力量製造出能量彈，\n全力撞向對手。威力會根據\n原來的招式而改變"}, "allOutPummelingSpecial": {"name": "格鬥Ｚ全力無雙激烈拳", "effect": "通過Ｚ力量製造出能量彈，\n全力撞向對手。威力會根據\n原來的招式而改變"}, "supersonicSkystrikePhysical": {"name": "飛行Ｚ極速俯衝轟烈撞", "effect": "通過Ｚ力量猛烈地飛向天空，\n朝對手全力落下。威力會\n根據原來的招式而改變"}, "supersonicSkystrikeSpecial": {"name": "飛行Ｚ極速俯衝轟烈撞", "effect": "通過Ｚ力量猛烈地飛向天空，\n朝對手全力落下。威力會\n根據原來的招式而改變"}, "acidDownpourPhysical": {"name": "毒Ｚ強酸劇毒滅絕雨", "effect": "通過Ｚ力量使毒沼湧起，全\n力讓對手沉下去。威力會根\n據原來的招式而改變"}, "acidDownpourSpecial": {"name": "毒Ｚ強酸劇毒滅絕雨", "effect": "通過Ｚ力量使毒沼湧起，全\n力讓對手沉下去。威力會根\n據原來的招式而改變"}, "tectonicRagePhysical": {"name": "地面Ｚ地隆嘯天大終結", "effect": "通過Ｚ力量潛入地裏最深處，\n全力撞上對手。威力會根\n據原來的招式而改變"}, "tectonicRageSpecial": {"name": "地面Ｚ地隆嘯天大終結", "effect": "通過Ｚ力量潛入地裏最深處，\n全力撞上對手。威力會根\n據原來的招式而改變"}, "continentalCrushPhysical": {"name": "岩石Ｚ毀天滅地巨巖墜", "effect": "通過Ｚ力量召喚大大的巖山，\n全力撞向對手。威力會根\n據原來的招式而改變"}, "continentalCrushSpecial": {"name": "岩石Ｚ毀天滅地巨巖墜", "effect": "通過Ｚ力量召喚大大的巖山，\n全力撞向對手。威力會根\n據原來的招式而改變"}, "savageSpinOutPhysical": {"name": "蟲Ｚ絕對捕食迴旋斬", "effect": "通過Ｚ力量將吐出的絲線全\n力束縛對手。威力會根據原\n來的招式而改變"}, "savageSpinOutSpecial": {"name": "蟲Ｚ絕對捕食迴旋斬", "effect": "通過Ｚ力量將吐出的絲線全\n力束縛對手。威力會根據原\n來的招式而改變"}, "neverEndingNightmarePhysical": {"name": "幽靈Ｚ無盡暗夜之誘惑", "effect": "通過Ｚ力量召喚強烈的怨念，\n全力降臨到對手身上。威\n力會根據原來的招式而改變"}, "neverEndingNightmareSpecial": {"name": "幽靈Ｚ無盡暗夜之誘惑", "effect": "通過Ｚ力量召喚強烈的怨念，\n全力降臨到對手身上。威\n力會根據原來的招式而改變"}, "corkscrewCrashPhysical": {"name": "鋼Ｚ超絕螺旋連擊", "effect": "通過Ｚ力量進行高速旋轉，\n全力撞上對手。威力會根據\n原來的招式而改變"}, "corkscrewCrashSpecial": {"name": "鋼Ｚ超絕螺旋連擊", "effect": "通過Ｚ力量進行高速旋轉，\n全力撞上對手。威力會根據\n原來的招式而改變"}, "infernoOverdrivePhysical": {"name": "火Ｚ超強極限爆焰彈", "effect": "通過Ｚ力量噴出熊熊烈火，\n全力撞向對手。威力會根據\n原來的招式而改變"}, "infernoOverdriveSpecial": {"name": "火Ｚ超強極限爆焰彈", "effect": "通過Ｚ力量噴出熊熊烈火，\n全力撞向對手。威力會根據\n原來的招式而改變"}, "hydroVortexPhysical": {"name": "水Ｚ超級水流大漩渦", "effect": "通過Ｚ力量製造大大的潮旋，\n全力吞沒對手。威力會根\n據原來的招式而改變"}, "hydroVortexSpecial": {"name": "水Ｚ超級水流大漩渦", "effect": "通過Ｚ力量製造大大的潮旋，\n全力吞沒對手。威力會根\n據原來的招式而改變"}, "bloomDoomPhysical": {"name": "草Ｚ絢爛繽紛花怒放", "effect": "通過Ｚ力量藉助花草的能量，\n全力攻擊對手。威力會根\n據原來的招式而改變"}, "bloomDoomSpecial": {"name": "草Ｚ絢爛繽紛花怒放", "effect": "通過Ｚ力量藉助花草的能量，\n全力攻擊對手。威力會根\n據原來的招式而改變"}, "gigavoltHavocPhysical": {"name": "電Ｚ終極伏特狂雷閃", "effect": "通過Ｚ力量將蓄積的強大電\n流全力撞向對手。威力會根\n據原來的招式而改變"}, "gigavoltHavocSpecial": {"name": "電Ｚ終極伏特狂雷閃", "effect": "通過Ｚ力量將蓄積的強大電\n流全力撞向對手。威力會根\n據原來的招式而改變"}, "shatteredPsychePhysical": {"name": "超能力Ｚ至高精神破壞波", "effect": "通過Ｚ力量操縱對手，全力\n使其感受到痛苦。威力會根\n據原來的招式而改變"}, "shatteredPsycheSpecial": {"name": "超能力Ｚ至高精神破壞波", "effect": "通過Ｚ力量操縱對手，全力\n使其感受到痛苦。威力會根\n據原來的招式而改變"}, "subzeroSlammerPhysical": {"name": "冰Ｚ激狂大地萬里冰", "effect": "通過Ｚ力量急劇降低氣溫，\n全力冰凍對手。威力會根據\n原來的招式而改變"}, "subzeroSlammerSpecial": {"name": "冰Ｚ激狂大地萬里冰", "effect": "通過Ｚ力量急劇降低氣溫，\n全力冰凍對手。威力會根據\n原來的招式而改變"}, "devastatingDrakePhysical": {"name": "龍Ｚ究極巨龍震天地", "effect": "通過Ｚ力量將氣場實體化，\n向對手全力發動襲擊。威力\n會根據原來的招式而改變"}, "devastatingDrakeSpecial": {"name": "龍Ｚ究極巨龍震天地", "effect": "通過Ｚ力量將氣場實體化，\n向對手全力發動襲擊。威力\n會根據原來的招式而改變"}, "blackHoleEclipsePhysical": {"name": "惡Ｚ黑洞吞噬萬物滅", "effect": "通過Ｚ力量收集惡能量，全\n力將對手吸入。威力會根據\n原來的招式而改變"}, "blackHoleEclipseSpecial": {"name": "惡Ｚ黑洞吞噬萬物滅", "effect": "通過Ｚ力量收集惡能量，全\n力將對手吸入。威力會根據\n原來的招式而改變"}, "twinkleTacklePhysical": {"name": "妖精Ｚ可愛星星飛天撞", "effect": "通過Ｚ力量製造魅惑空間，\n全力捉弄對手。威力會根據\n原來的招式而改變"}, "twinkleTackleSpecial": {"name": "妖精Ｚ可愛星星飛天撞", "effect": "通過Ｚ力量製造魅惑空間，\n全力捉弄對手。威力會根據\n原來的招式而改變"}, "catastropika": {"name": "皮卡丘Ｚ皮卡皮卡必殺擊", "effect": "通過Ｚ力量，皮卡丘全身覆\n蓋最強電力，全力猛撲對手"}, "shoreUp": {"name": "集沙", "effect": "回覆自己最大ＨＰ的一半。\n在沙暴中回覆得更多"}, "firstImpression": {"name": "迎頭一擊", "effect": "威力很高的招式，但只有在\n出場戰鬥時，立刻使出才能\n成功"}, "banefulBunker": {"name": "碉堡", "effect": "防住對手攻擊的同時，讓接\n觸到自己的對手中毒"}, "spiritShackle": {"name": "縫影", "effect": "攻擊的同時，縫住對手的影\n子，使其無法逃走"}, "darkestLariat": {"name": "ＤＤ金勾臂", "effect": "旋轉雙臂打向對手。無視對\n手的能力變化，直接給予傷\n害"}, "sparklingAria": {"name": "泡影的詠歎調", "effect": "隨着唱歌會放出很多氣球。\n受到此招式攻擊時，灼傷會\n被治癒"}, "iceHammer": {"name": "冰錘", "effect": "揮舞強力而沉重的拳頭，給\n予對手傷害。自己的速度會\n降低"}, "floralHealing": {"name": "花療", "effect": "回覆對手最大ＨＰ的一半。\n在青草場地時，效果會提高"}, "highHorsepower": {"name": "十萬馬力", "effect": "使出全身力量，猛攻對手"}, "strengthSap": {"name": "吸取力量", "effect": "給自己回覆和對手攻擊力相\n同數值的ＨＰ，然後降低對\n手的攻擊"}, "solarBlade": {"name": "日光刃", "effect": "第１回合收集滿滿的日光，\n第２回合將此力量集中在劍\n上進行攻擊"}, "leafage": {"name": "樹葉", "effect": "將葉片打向對手，進行攻擊"}, "spotlight": {"name": "聚光燈", "effect": "給寶可夢打上聚光燈，該回\n合只能瞄準該寶可夢"}, "toxicThread": {"name": "毒絲", "effect": "將混有毒的絲吐向對手。使\n其中毒，從而降低對手的速\n度"}, "laserFocus": {"name": "磨礪", "effect": "集中精神，下次攻擊必定會\n擊中要害"}, "gearUp": {"name": "輔助齒輪", "effect": "啓動齒輪，提高特性爲正電\n和負電的寶可夢的攻擊和特\n攻"}, "throatChop": {"name": "深淵突刺", "effect": "受到此招式攻擊的對手，會\n因爲地獄般的痛苦，在２回\n合內，變得無法使出聲音類\n招式"}, "pollenPuff": {"name": "花粉團", "effect": "對敵人使用是會爆炸的糰子。\n對我方使用則是給予回覆\n的糰子"}, "anchorShot": {"name": "擲錨", "effect": "將錨纏住對手進行攻擊。使\n對手無法逃走"}, "psychicTerrain": {"name": "精神場地", "effect": "在５回合內，地面上的寶可\n夢不會受到先制招式的攻擊。\n超能力屬性的招式威力會\n提高"}, "lunge": {"name": "猛撲", "effect": "全力猛撲對手進行攻擊。從\n而降低對手的攻擊"}, "fireLash": {"name": "火焰鞭", "effect": "用燃燒的鞭子抽打對手。受\n到攻擊的對手防禦會降低"}, "powerTrip": {"name": "囂張", "effect": "耀武揚威地攻擊對手，自己\n的能力提高得越多，威力就\n越大"}, "burnUp": {"name": "燃盡", "effect": "將自己全身燃燒起火焰來，\n給予對手大大的傷害。自己\n的火屬性將會消失"}, "speedSwap": {"name": "速度互換", "effect": "將對手和自己的速度進行互\n換"}, "smartStrike": {"name": "修長之角", "effect": "用尖尖的角刺入對手進行攻\n擊。攻擊必定會命中"}, "purify": {"name": "淨化", "effect": "治癒對手的異常狀態。治癒\n後可以回覆自己的ＨＰ"}, "revelationDance": {"name": "覺醒之舞", "effect": "全力跳舞進行攻擊。此招式\n的屬性將變得和自己的屬性\n相同"}, "coreEnforcer": {"name": "核心懲罰者", "effect": "如果給予過傷害的對手已經\n結束行動，其特性就會被消\n除"}, "tropKick": {"name": "熱帶踢", "effect": "向對手使出來自南國的火熱\n腳踢。從而降低對手的攻擊"}, "instruct": {"name": "號令", "effect": "向對手下達指示，讓其再次\n使出剛纔的招式"}, "beakBlast": {"name": "鳥嘴加農炮", "effect": "先加熱鳥嘴後再進行攻擊。\n鳥嘴在加熱時對手觸碰的話，\n就會使其灼傷"}, "clangingScales": {"name": "鱗片噪音", "effect": "摩擦全身鱗片，發出響亮的\n聲音進行攻擊。攻擊後自己\n的防禦會降低"}, "dragonHammer": {"name": "龍錘", "effect": "將身體當作錘子，向對手發\n動襲擊，給予傷害"}, "brutalSwing": {"name": "狂舞揮打", "effect": "用自己的身體狂舞揮打，給\n予對手傷害"}, "auroraVeil": {"name": "極光幕", "effect": "在５回合內減弱物理和特殊\n的傷害。只有下雪時才能使\n出"}, "sinisterArrowRaid": {"name": "狙射樹梟Ｚ遮天蔽日暗影箭", "effect": "通過Ｚ力量製造出無數箭的\n狙射樹梟將全力射穿對手進\n行攻擊"}, "maliciousMoonsault": {"name": "熾焰咆哮虎Ｚ極惡飛躍粉碎擊", "effect": "通過Ｚ力量得到強壯肉體的\n熾焰咆哮虎將全力撞向對手\n進行攻擊"}, "oceanicOperetta": {"name": "西獅海壬Ｚ海神莊嚴交響樂", "effect": "通過Ｚ力量召喚大量水的西\n獅海壬將全力攻擊對手"}, "guardianOfAlola": {"name": "卡璞Ｚ巨人衛士・阿羅拉", "effect": "通過Ｚ力量得到阿羅拉之力\n的土地神寶可夢將全力進行\n攻擊。對手的剩餘HP會減\n少很多"}, "soulStealing7StarStrike": {"name": "瑪夏多Ｚ七星奪魂腿", "effect": "得到Ｚ力量的瑪夏多將全力\n使出拳頭和腳踢的連續招式\n叩打對手"}, "stokedSparksurfer": {"name": "阿羅雷Ｚ駕雷馭電戲衝浪", "effect": "得到Ｚ力量的阿羅拉地區的\n雷丘將全力進行攻擊。從而\n讓對手陷入麻痹狀態"}, "pulverizingPancake": {"name": "卡比獸Ｚ認真起來大爆擊", "effect": "通過Ｚ力量使得認真起來的\n卡比獸躍動巨大身軀，全力\n向對手發動襲擊"}, "extremeEvoboost": {"name": "伊布Ｚ九彩昇華齊聚頂", "effect": "得到Ｚ力量的伊布將藉助進\n化後夥伴們的力量，大幅提\n高能力"}, "genesisSupernova": {"name": "夢幻Ｚ起源超新星大爆炸", "effect": "得到Ｚ力量的夢幻將全力攻\n擊對手。腳下會變成精神場\n地"}, "shellTrap": {"name": "陷阱甲殼", "effect": "設下甲殼陷阱。如果對手使\n出物理招式，陷阱就會爆炸\n並給予對手傷害"}, "fleurCannon": {"name": "花朵加農炮", "effect": "放出強力光束後，自己的特\n攻會大幅降低"}, "psychicFangs": {"name": "精神之牙", "effect": "利用精神力量咬住對手進行\n攻擊。還可以破壞光牆和反\n射壁等"}, "stompingTantrum": {"name": "跺腳", "effect": "化悔恨爲力量進行攻擊。如\n果上一回合招式沒有打中，\n威力就會翻倍"}, "shadowBone": {"name": "暗影之骨", "effect": "用附有靈魂的骨頭毆打對手\n進行攻擊。有時會降低對手\n的防禦"}, "accelerock": {"name": "衝巖", "effect": "迅速撞向對手進行攻擊。必\n定能夠先制攻擊"}, "liquidation": {"name": "水流裂破", "effect": "用水之力量撞向對手進行攻\n擊。有時會降低對手的防禦"}, "prismaticLaser": {"name": "棱鏡鐳射", "effect": "用棱鏡的力量發射強烈光線。\n下一回合自己將無法動彈"}, "spectralThief": {"name": "暗影偷盜", "effect": "潛入對手的影子進行攻擊。\n會奪取對手的能力提升"}, "sunsteelStrike": {"name": "流星閃衝", "effect": "以流星般的氣勢猛撞對手。\n可以無視對手的特性進行攻\n擊"}, "moongeistBeam": {"name": "暗影之光", "effect": "放出奇怪的光線攻擊對手。\n可以無視對手的特性進行攻\n擊"}, "tearfulLook": {"name": "淚眼汪汪", "effect": "變得淚眼汪汪，讓對手喪失\n鬥志。從而降低對手的攻擊\n和特攻"}, "zingZap": {"name": "麻麻刺刺", "effect": "撞向對手，併發出強電，使\n其感到麻麻刺刺的。有時會\n使對手畏縮"}, "naturesMadness": {"name": "自然之怒", "effect": "向對手釋放自然之怒。對手\n的ＨＰ會減半"}, "multiAttack": {"name": "多屬性攻擊", "effect": "一邊覆蓋高能量，一邊撞向\n對手進行攻擊。根據存儲碟\n不同，屬性會改變"}, "tenMillionVoltThunderbolt": {"name": "智皮卡Ｚ千萬伏特", "effect": "戴着帽子的皮卡丘將通過Ｚ\n力量增強的電擊全力釋放給\n對手。容易擊中要害"}, "mindBlown": {"name": "驚爆大頭", "effect": "讓自己的頭爆炸，來攻擊周\n圍的一切。自己也會受到傷\n害"}, "plasmaFists": {"name": "等離子閃電拳", "effect": "用覆蓋着電流的拳頭進行攻\n擊。使一般屬性的招式變成\n電屬性"}, "photonGeyser": {"name": "光子噴湧", "effect": "用光柱來進行攻擊。比較自\n己的攻擊和特攻，用數值相\n對較高的一項給予對方傷害"}, "lightThatBurnsTheSky": {"name": "究極奈克洛Ｚ焚天滅世熾光爆", "effect": "奈克洛茲瑪會無視對手的特\n性效果，在攻擊和特攻之間，\n用數值相對較高的一項給\n予對方傷害"}, "searingSunrazeSmash": {"name": "索爾迦雷歐Ｚ日光迴旋下蒼穹", "effect": "得到Ｚ力量的索爾迦雷歐將\n全力進行攻擊。可以無視對\n手的特性效果"}, "menacingMoonrazeMaelstrom": {"name": "露奈雅拉Ｚ月華飛濺落靈霄", "effect": "得到Ｚ力量的露奈雅拉將全\n力進行攻擊。可以無視對手\n的特性效果"}, "letsSnuggleForever": {"name": "謎擬丘Ｚ親密無間大亂揍", "effect": "得到Ｚ力量的謎擬Ｑ將全力\n進行亂揍攻擊"}, "splinteredStormshards": {"name": "鬃巖狼人Ｚ狼嘯石牙颶風暴", "effect": "得到Ｚ力量的鬃巖狼人將全\n力進行攻擊。而且會消除場\n地狀態"}, "clangorousSoulblaze": {"name": "杖尾鱗甲龍Ｚ熾魂熱舞烈音爆", "effect": "得到Ｚ力量的杖尾鱗甲龍將\n全力攻擊對手。並且自己的\n能力會提高"}, "zippyZap": {"name": "電電加速", "effect": "迅猛無比的電擊。必定能夠先制攻擊，\n並且提高自己的閃避率"}, "splishySplash": {"name": "滔滔衝浪", "effect": "往巨浪中注入電能後衝撞對\n手進行攻擊。有時會讓對手\n陷入麻痹狀態"}, "floatyFall": {"name": "飄飄墜落", "effect": "輕飄飄地浮起來後，再猛地\n俯衝下去進行攻擊。有時會\n使對手畏縮"}, "pikaPapow": {"name": "閃閃雷光", "effect": "皮卡丘越喜歡訓練家，電擊\n的威力就越強。攻擊必定會\n命中"}, "bouncyBubble": {"name": "活活氣泡", "effect": "投擲水球進行攻擊。攻擊後\n吸水並回復給予對手傷害\n的一半HP"}, "buzzyBuzz": {"name": "麻麻電擊", "effect": "放出電擊攻擊對手。讓對手\n陷入麻痹狀態"}, "sizzlySlide": {"name": "熊熊火爆", "effect": "用燃起大火的身體猛烈地衝\n撞對手。讓對手陷入灼傷狀\n態"}, "glitzyGlow": {"name": "嘩嘩氣場", "effect": "利用念力強攻，粉碎對方信\n心。製造一道能減弱對手特\n殊攻擊的神奇牆壁"}, "baddyBad": {"name": "壞壞領域", "effect": "惡行惡相地進行攻擊。製造\n一道能減弱對手物理攻擊的\n神奇牆壁"}, "sappySeed": {"name": "茁茁炸彈", "effect": "長出巨大的藤蔓，播撒種子\n進行攻擊。種子每回合都會\n吸取對手的HP"}, "freezyFrost": {"name": "冰冰霜凍", "effect": "利用冰冷的黑霧結晶進行攻\n擊。使全體寶可夢的能力變\n回原點"}, "sparklySwirl": {"name": "亮亮風暴", "effect": "利用芬芳刺鼻的龍捲風吞噬\n對方。能治癒我方寶可夢的\n異常狀態"}, "veeveeVolley": {"name": "砰砰擊破", "effect": "伊布越喜歡訓練家，衝撞的\n威力就越強。攻擊必定會命\n中"}, "doubleIronBash": {"name": "鋼拳雙擊", "effect": "以胸口的螺帽爲中心旋轉，\n並連續２次揮動手臂打擊對\n手。有時會使對手畏縮"}, "maxGuard": {"name": "極巨防壁", "effect": "完全抵擋對手的攻擊。連續\n使出則容易失敗"}, "dynamaxCannon": {"name": "極巨炮", "effect": "將凝縮在體內的能量從核心放出進行攻擊，\n對手等級比當前波次的等級上限越高，造成的傷害越高，最多兩倍。"}, "snipeShot": {"name": "狙擊", "effect": "能無視具有吸引對手招式效\n果的特性或招式的影響。可\n以向選定的對手進行攻擊"}, "jawLock": {"name": "緊咬不放", "effect": "使雙方直到一方昏厥爲止無\n法替換寶可夢。其中一方退\n場則可以解除效果"}, "stuffCheeks": {"name": "大快朵頤", "effect": "喫掉攜帶的樹果，大幅提高\n防禦"}, "noRetreat": {"name": "背水一戰", "effect": "提高自己的所有能力，但無\n法替換或逃走"}, "tarShot": {"name": "瀝青射擊", "effect": "潑灑黏糊糊的瀝青，降低對\n手的速度。火屬性會變成對\n手的弱點"}, "magicPowder": {"name": "魔法粉", "effect": "向對手噴灑魔法粉，使對手\n變爲超能力屬性"}, "dragonDarts": {"name": "龍箭", "effect": "讓多龍梅西亞進行２次攻擊。\n如果對手有２隻寶可夢，\n則對它們各進行１次攻擊"}, "teatime": {"name": "茶會", "effect": "舉辦一場茶會，場上的所有\n寶可夢都會喫掉自己攜帶的\n樹果"}, "octolock": {"name": "蛸固", "effect": "讓對手無法逃走。對手被固\n定後，每回合都會降低防禦\n和特防"}, "boltBeak": {"name": "電喙", "effect": "用帶電的喙啄刺對手。如果\n比對手先出手攻擊，招式的\n威力會變成２倍"}, "fishiousRend": {"name": "鰓咬", "effect": "用堅硬的腮咬住對手。如果\n比對手先出手攻擊，招式的\n威力會變成２倍"}, "courtChange": {"name": "換場", "effect": "用神奇的力量交換雙方的場\n地效果"}, "maxFlare": {"name": "極巨火爆", "effect": "極巨化寶可夢使出的火屬性\n攻擊。可在５回合內讓日照\n變得強烈"}, "maxFlutterby": {"name": "極巨蟲蠱", "effect": "極巨化寶可夢使出的蟲屬性\n攻擊。會降低對手的特攻"}, "maxLightning": {"name": "極巨閃電", "effect": "極巨化寶可夢使出的電屬性\n攻擊。可在５回合內將腳下\n變成電氣場地"}, "maxStrike": {"name": "極巨攻擊", "effect": "極巨化寶可夢使出的一般屬\n性攻擊。會降低對手的速度"}, "maxKnuckle": {"name": "極巨拳鬥", "effect": "極巨化寶可夢使出的格鬥屬\n性攻擊。會提高我方的攻擊"}, "maxPhantasm": {"name": "極巨幽魂", "effect": "極巨化寶可夢使出的幽靈屬\n性攻擊。會降低對手的防禦"}, "maxHailstorm": {"name": "極巨寒冰", "effect": "極巨化寶可夢使出的冰屬性\n攻擊。在５回合內會下雪"}, "maxOoze": {"name": "極巨酸毒", "effect": "極巨化寶可夢使出的毒屬性\n攻擊。會提高我方的特攻"}, "maxGeyser": {"name": "極巨水流", "effect": "極巨化寶可夢使出的水屬性\n攻擊。可在５回合內降下大\n雨"}, "maxAirstream": {"name": "極巨飛衝", "effect": "極巨化寶可夢使出的飛行屬\n性攻擊。會提高我方的速度"}, "maxStarfall": {"name": "極巨妖精", "effect": "極巨化寶可夢使出的妖精屬\n性攻擊。可在５回合內將腳\n下變成薄霧場地"}, "maxWyrmwind": {"name": "極巨龍騎", "effect": "極巨化寶可夢使出的龍屬性\n攻擊。會降低對手的攻擊"}, "maxMindstorm": {"name": "極巨超能", "effect": "極巨化寶可夢使出的超能力\n屬性攻擊。可在５回合內將\n腳下變成精神場地"}, "maxRockfall": {"name": "極巨岩石", "effect": "極巨化寶可夢使出的岩石屬\n性攻擊。可在５回合內捲起\n沙暴"}, "maxQuake": {"name": "極巨大地", "effect": "極巨化寶可夢使出的地面屬\n性攻擊。會提高我方的特防"}, "maxDarkness": {"name": "極巨惡霸", "effect": "極巨化寶可夢使出的惡屬性\n攻擊。會降低對手的特防"}, "maxOvergrowth": {"name": "極巨草原", "effect": "極巨化寶可夢使出的草屬性\n攻擊。可在５回合內將腳下\n變成青草場地"}, "maxSteelspike": {"name": "極巨鋼鐵", "effect": "極巨化寶可夢使出的鋼屬性\n攻擊。會提高我方的防禦"}, "clangorousSoul": {"name": "魂舞烈音爆", "effect": "削減少許自己的ＨＰ，使所\n有能力都提高"}, "bodyPress": {"name": "撲擊", "effect": "用身體撞向對手進行攻擊。\n防禦越高，給予的傷害就越\n高"}, "decorate": {"name": "裝飾", "effect": "通過裝飾，大幅提高對方的\n攻擊和特攻"}, "drumBeating": {"name": "鼓擊", "effect": "用鼓點來控制鼓的根部進行\n攻擊，從而降低對手的速度"}, "snapTrap": {"name": "捕獸夾", "effect": "使用捕獸夾，在４～５回合\n內，夾住對手進行攻擊"}, "pyroBall": {"name": "火焰球", "effect": "點燃小石子，形成火球攻擊\n對手。有時會使對手陷入灼\n傷狀態"}, "behemothBlade": {"name": "巨獸斬", "effect": "以全身力氣舉起強大的劍，\n猛烈地劈向對手進行攻擊"}, "behemothBash": {"name": "巨獸彈", "effect": "將全身變化爲堅固的盾，猛\n烈地撞向對手進行攻擊"}, "auraWheel": {"name": "氣場輪", "effect": "用儲存在頰囊裏的能量進行\n攻擊，並提高自己的速度。\n如果由莫魯貝可使用，\n其屬性會隨着它的樣子而改變"}, "breakingSwipe": {"name": "廣域破壞", "effect": "用堅韌的尾巴猛掃對手進行\n攻擊，從而降低對手的攻擊"}, "branchPoke": {"name": "木枝突刺", "effect": "使用尖銳的樹枝刺向對手進\n行攻擊"}, "overdrive": {"name": "破音", "effect": "奏響吉他和貝斯，釋放出發\n出巨響的劇烈震動攻擊對手"}, "appleAcid": {"name": "蘋果酸", "effect": "使用從酸蘋果中提取出來的\n酸性液體進行攻擊。降低對\n手的特防"}, "gravApple": {"name": "萬有引力", "effect": "從高處落下蘋果，給予對手\n傷害。可降低對手的防禦"}, "spiritBreak": {"name": "靈魂衝擊", "effect": "用足以讓對手一蹶不振的氣\n勢進行攻擊。會降低對手的\n特攻"}, "strangeSteam": {"name": "神奇蒸汽", "effect": "噴出煙霧攻擊對手。有時會\n使對手混亂"}, "lifeDew": {"name": "生命水滴", "effect": "噴灑出神奇的水，回覆自己\n和場上同伴的ＨＰ"}, "obstruct": {"name": "攔堵", "effect": "完全抵擋對手的攻擊。連續\n使出則容易失敗。一旦觸碰，\n防禦就會大幅降低"}, "falseSurrender": {"name": "假跪真撞", "effect": "裝作低頭認錯的樣子，用凌\n亂的頭髮進行突刺。攻擊必\n定會命中"}, "meteorAssault": {"name": "流星突擊", "effect": "大力揮舞粗壯的莖進行攻擊。\n但同時自己也會被晃暈，\n下一回合自己將無法動彈"}, "eternabeam": {"name": "無極光束", "effect": "無極汰那變回原來的樣子後，\n發動的最強攻擊。下一回\n合自己將無法動彈"}, "steelBeam": {"name": "鐵蹄光線", "effect": "將從全身聚集的鋼鐵化爲光\n束，激烈地發射出去。自己\n也會受到傷害"}, "expandingForce": {"name": "廣域戰力", "effect": "利用精神力量攻擊對手。在\n精神場地上威力會有所提高，\n能對所有對手造成傷害"}, "steelRoller": {"name": "鐵滾輪", "effect": "在破壞場地的同時攻擊對手。\n如果腳下沒有任何場地狀\n態存在，使出此招式時便會\n失敗"}, "scaleShot": {"name": "鱗射", "effect": "發射鱗片進行攻擊。連續攻\n擊２～５次。速度會提高但\n防禦會降低"}, "meteorBeam": {"name": "流星光束", "effect": "第１回合聚集宇宙之力提高\n特攻，第２回合攻擊對手"}, "shellSideArm": {"name": "臂貝武器", "effect": "從物理攻擊和特殊攻擊中選\n擇可造成較多傷害的方式進\n行攻擊。有時會讓對手陷入\n中毒狀態"}, "mistyExplosion": {"name": "薄霧炸裂", "effect": "對自己周圍的所有寶可夢進\n行攻擊，但使出後，自己會\n陷入昏厥。在薄霧場地上，\n招式威力會提高"}, "grassyGlide": {"name": "青草滑梯", "effect": "彷彿在地面上滑行般地攻擊\n對手。在青草場地上，必定\n能夠先制攻擊"}, "risingVoltage": {"name": "電力上升", "effect": "用從地面升騰而起的電擊進\n行攻擊。當對手處於電氣場\n地上時，招式威力會變成２\n倍"}, "terrainPulse": {"name": "大地波動", "effect": "藉助場地的力量進行攻擊。\n視使出招式時場地狀態不同，\n招式的屬性和威力會有所\n變化"}, "skitterSmack": {"name": "爬擊", "effect": "從對手背後爬近後進行攻擊。\n會降低對手的特攻"}, "burningJealousy": {"name": "妒火", "effect": "用嫉妒的能量攻擊對手。會\n讓在該回合內能力有所提高\n的寶可夢陷入灼傷狀態"}, "lashOut": {"name": "泄憤", "effect": "攻擊對手以發泄對其感到的\n惱怒情緒。如果在該回合內\n自身能力遭到降低，招式的\n威力會變成２倍"}, "poltergeist": {"name": "靈騷", "effect": "操縱對手的持有物進行攻擊。\n當對手沒有攜帶道具時，\n使出此招式時便會失敗"}, "corrosiveGas": {"name": "腐蝕氣體", "effect": "用具有強酸性的氣體包裹住\n自己周圍所有的寶可夢，並\n融化其所攜帶的道具"}, "coaching": {"name": "指導", "effect": "通過進行正確合理的指導，\n提高我方全員的攻擊和防禦"}, "flipTurn": {"name": "快速折返", "effect": "在攻擊之後急速返回，和後\n備寶可夢進行替換"}, "tripleAxel": {"name": "三旋擊", "effect": "連續３次踢對手進行攻擊。\n每踢中一次，威力就會提高"}, "dualWingbeat": {"name": "雙翼", "effect": "將翅膀撞向對手進行攻擊。\n連續２次給予傷害"}, "scorchingSands": {"name": "熱沙大地", "effect": "將滾燙的沙子砸向對手進行\n攻擊。有時會讓對手陷入灼\n傷狀態"}, "jungleHealing": {"name": "叢林治療", "effect": "與叢林融爲一體，回覆自己\n和場上同伴的ＨＰ和狀態"}, "wickedBlow": {"name": "闇冥強擊", "effect": "將惡之流派修煉至大成的猛\n烈一擊。必定會擊中要害"}, "surgingStrikes": {"name": "水流連打", "effect": "將水之流派修煉至大成的仿\n若行雲流水般的３次連擊。\n必定會擊中要害"}, "thunderCage": {"name": "雷電囚籠", "effect": "將對手困在電流四濺的囚籠\n中，在４～５回合內進行攻\n擊"}, "dragonEnergy": {"name": "巨龍威能", "effect": "把生命力轉換爲力量攻擊對\n手。自己的ＨＰ越少，招式\n的威力越小"}, "freezingGlare": {"name": "冰冷視線", "effect": "從雙眼發射精神力量進行攻\n擊。有時會讓對手陷入冰凍\n狀態"}, "fieryWrath": {"name": "怒火中燒", "effect": "將憤怒轉化爲火焰般的氣場\n進行攻擊。有時會使對手畏\n縮"}, "thunderousKick": {"name": "雷鳴蹴擊", "effect": "以雷電般的動作戲耍對手的\n同時使出腳踢。可降低對手\n的防禦"}, "glacialLance": {"name": "雪矛", "effect": "向對手投擲掀起暴風雪的冰\n矛進行攻擊"}, "astralBarrage": {"name": "星碎", "effect": "用大量的小靈體向對手發起\n攻擊"}, "eerieSpell": {"name": "詭異咒語", "effect": "用強大的精神力量攻擊。讓\n對手最後使用的招式減少３\nＰＰ"}, "direClaw": {"name": "克命爪", "effect": "以破滅之爪進行攻擊。有時\n還會讓對手陷入中毒、麻痹\n、睡眠之中的一種狀態"}, "psyshieldBash": {"name": "屏障猛攻", "effect": "讓意念的能量覆蓋全身，撞\n向對手進行攻擊。會提高自\n己的防禦"}, "powerShift": {"name": "力量轉換", "effect": "將自己的攻擊與防禦互相交\n換"}, "stoneAxe": {"name": "巖斧", "effect": "用岩石之斧進行攻擊。散落\n的岩石碎片會飄浮在對手周\n圍"}, "springtideStorm": {"name": "陽春風暴", "effect": "用交織着愛與恨的烈風席捲\n對手進行攻擊。有時會降低\n對手的攻擊"}, "mysticalPower": {"name": "神祕之力", "effect": "放出不可思議的力量攻擊。\n會提高自己的特攻"}, "ragingFury": {"name": "大憤慨", "effect": "在２～３回合內，一邊放出\n火焰，一邊瘋狂亂打。大鬧\n一番後自己會陷入混亂"}, "waveCrash": {"name": "波動衝", "effect": "讓水覆蓋全身後撞向對手。\n自己也會受到不少傷害"}, "chloroblast": {"name": "葉綠爆震", "effect": "將自己的葉綠素凝聚起來後\n放出去進行攻擊。自己也會\n受到傷害"}, "mountainGale": {"name": "冰山風", "effect": "將冰山般巨大的冰塊砸向對\n手進行攻擊。有時會使對手\n畏縮"}, "victoryDance": {"name": "勝利之舞", "effect": "激烈地跳起喚來勝利的舞蹈，\n提高自己的攻擊、防禦和\n速度"}, "headlongRush": {"name": "突飛猛撲", "effect": "向對手使出灌注了全心全力\n的撞擊。自己的防禦和特防\n會降低"}, "barbBarrage": {"name": "毒千針", "effect": "用無數的毒針進行攻擊。有\n時還會讓對手陷入中毒狀態。\n攻擊處於中毒狀態的對手\n時，威力會變成２倍"}, "esperWing": {"name": "氣場之翼", "effect": "用經過氣場強化的翅膀撕裂\n對手。容易擊中要害。會提\n高自己的速度"}, "bitterMalice": {"name": "冤冤相報", "effect": "用令人毛骨悚然的怨念進行\n攻擊。會降低對手的攻擊"}, "shelter": {"name": "閉關", "effect": "將皮膚變得堅硬如鐵盾，從\n而大幅提高自己的防禦"}, "tripleArrows": {"name": "三連箭", "effect": "使出一記腿技後同時發射３\n箭。有時會降低對手的防禦\n或使對手畏縮。容易擊中要\n害"}, "infernalParade": {"name": "羣魔亂舞", "effect": "用無數的火球進行攻擊。有\n時會讓對手陷入灼傷狀態。\n攻擊處於異常狀態的對手時，\n威力會變成２倍"}, "ceaselessEdge": {"name": "祕劍・千重濤", "effect": "用貝殼之劍進行攻擊。散落\n的貝殼碎片會散落在對手腳\n下成爲撒菱"}, "bleakwindStorm": {"name": "枯葉風暴", "effect": "用足以讓身心都止不住顫抖\n的冰冷狂風進行攻擊。有時\n會降低對手的速度"}, "wildboltStorm": {"name": "鳴雷風暴", "effect": "呼喚雷雲引起風暴，用雷與\n風進行激烈的攻擊。有時會\n讓對手陷入麻痹狀態"}, "sandsearStorm": {"name": "熱沙風暴", "effect": "用灼熱的沙子和強烈的風席\n卷對手進行攻擊。有時會讓\n對手陷入灼傷狀態"}, "lunarBlessing": {"name": "新月祈禱", "effect": "向新月獻上祈禱，回覆自己\n和場上同伴的ＨＰ和狀態"}, "takeHeart": {"name": "勇氣填充", "effect": "鼓起衝勁，治癒自己的異常\n狀態，同時提高自己的特攻\n和特防"}, "gMaxWildfire": {"name": "超極巨深淵滅焰", "effect": "超極巨化的噴火龍使出的火\n屬性攻擊。可在４回合內給\n予對手傷害"}, "gMaxBefuddle": {"name": "超極巨蝶影蠱惑", "effect": "超極巨化的巴大蝶使出的蟲\n屬性攻擊。會讓對手陷入中\n毒、麻痹或睡眠狀態"}, "gMaxVoltCrash": {"name": "超極鉅萬雷轟頂", "effect": "超極巨化的皮卡丘使出的電\n屬性攻擊。會讓對手陷入麻\n痹狀態"}, "gMaxGoldRush": {"name": "超極巨特大金幣", "effect": "超極巨化的喵喵使出的一般\n屬性攻擊。會讓對手陷入混\n亂狀態，並可獲得金錢"}, "gMaxChiStrike": {"name": "超極巨會心一擊", "effect": "超極巨化的怪力使出的格鬥\n屬性攻擊。會變得容易擊中\n要害"}, "gMaxTerror": {"name": "超極巨幻影幽魂", "effect": "超極巨化的耿鬼使出的幽靈\n屬性攻擊。會踩住對手的影\n子，讓其無法被替換"}, "gMaxResonance": {"name": "超極巨極光旋律", "effect": "超極巨化的拉普拉斯使出的\n冰屬性攻擊。可在５回合內\n減弱受到的傷害"}, "gMaxCuddle": {"name": "超極巨熱情擁抱", "effect": "超極巨化的伊布使出的一般\n屬性攻擊。會讓對手陷入着\n迷狀態"}, "gMaxReplenish": {"name": "超極巨資源再生", "effect": "超極巨化的卡比獸使出的一\n般屬性攻擊。會讓喫掉的樹\n果再生"}, "gMaxMalodor": {"name": "超極巨臭氣沖天", "effect": "超極巨化的灰塵山使出的毒\n屬性攻擊。會讓對手陷入中\n毒狀態"}, "gMaxStonesurge": {"name": "超極巨巖陣以待", "effect": "超極巨化的暴噬龜使出的水\n屬性攻擊。會發射無數銳利\n的岩石"}, "gMaxWindRage": {"name": "超極巨旋風襲捲", "effect": "超極巨化的鋼鎧鴉使出的飛\n行屬性攻擊。可消除反射壁\n和光牆"}, "gMaxStunShock": {"name": "超極巨異毒電場", "effect": "超極巨化的顫弦蠑螈使出的\n電屬性攻擊。會讓對手陷入\n中毒或麻痹狀態"}, "gMaxFinale": {"name": "超極巨幸福圓滿", "effect": "超極巨化的霜奶仙使出的妖\n精屬性攻擊。可回覆我方的\nＨＰ"}, "gMaxDepletion": {"name": "超極巨劣化衰變", "effect": "超極巨化的鋁鋼龍使出的龍\n屬性攻擊。可減少對手最後\n使用的招式的ＰＰ"}, "gMaxGravitas": {"name": "超極巨天道七星", "effect": "超極巨化的以歐路普使出的\n超能力屬性攻擊。在５回合\n內重力會產生變化"}, "gMaxVolcalith": {"name": "超極巨炎石噴發", "effect": "超極巨化的巨炭山使出的巖\n石屬性攻擊。可在４回合內\n給予對手傷害"}, "gMaxSandblast": {"name": "超極巨沙塵漫天", "effect": "超極巨化的沙螺蟒使出的地\n面屬性攻擊。在４～５回合\n內會狂刮沙暴"}, "gMaxSnooze": {"name": "超極巨睡魔降臨", "effect": "超極巨化的長毛巨魔使出的\n惡屬性攻擊。會通過打大哈\n欠讓對手產生睡意"}, "gMaxTartness": {"name": "超極巨酸不溜丟", "effect": "超極巨化的蘋裹龍使出的草\n屬性攻擊。會降低對手的閃\n避率"}, "gMaxSweetness": {"name": "超極巨瓊漿玉液", "effect": "超極巨化的豐蜜龍使出的草\n屬性攻擊。會治癒我方的異\n常狀態"}, "gMaxSmite": {"name": "超極巨天譴雷誅", "effect": "超極巨化的布莉姆溫使出的\n妖精屬性攻擊。會讓對手陷\n入混亂狀態"}, "gMaxSteelsurge": {"name": "超極巨鋼鐵陣法", "effect": "超極巨化的大王銅象使出的\n鋼屬性攻擊。會發射無數銳\n利的刺"}, "gMaxMeltdown": {"name": "超極巨液金熔擊", "effect": "超極巨化的美錄梅塔使出的\n鋼屬性攻擊。會讓對手無法\n連續使出相同的招式"}, "gMaxFoamBurst": {"name": "超極巨激漩泡渦", "effect": "超極巨化的巨鉗蟹使出的水\n屬性攻擊。會大幅降低對手\n的速度"}, "gMaxCentiferno": {"name": "超極巨百火焚野", "effect": "超極巨化的焚焰蚣使出的火\n屬性攻擊。可在４～５回合\n內將對手困在火焰中"}, "gMaxVineLash": {"name": "超極巨灰飛鞭滅", "effect": "超極巨化的妙蛙花使出的草\n屬性攻擊。可在４回合內給\n予對手傷害"}, "gMaxCannonade": {"name": "超極巨水炮轟滅", "effect": "超極巨化的水箭龜使出的水\n屬性攻擊。可在４回合內給\n予對手傷害"}, "gMaxDrumSolo": {"name": "超極巨狂擂亂打", "effect": "超極巨化的轟擂金剛猩使出\n的草屬性攻擊。不會受到對\n手特性的干擾"}, "gMaxFireball": {"name": "超極巨破陣火球", "effect": "超極巨化的閃焰王牌使出的\n火屬性攻擊。不會受到對手\n特性的干擾"}, "gMaxHydrosnipe": {"name": "超極巨狙擊神射", "effect": "超極巨化的千面避役使出的\n水屬性攻擊。不會受到對手\n特性的干擾"}, "gMaxOneBlow": {"name": "超極巨奪命一擊", "effect": "超極巨化的武道熊師使出的\n惡屬性攻擊。是可以無視極\n巨防壁的一擊"}, "gMaxRapidFlow": {"name": "超極巨流水連擊", "effect": "超極巨化的武道熊師使出的\n水屬性攻擊。是可以無視極\n巨防壁的連擊"}, "teraBlast": {"name": "太晶爆發", "effect": "太晶化時，會放出太晶屬性\n的能量攻擊。比較自己的攻\n擊和特攻，用數值相對較高\n的一項給予對方傷害。（其\n他屬性）／用攻擊和特攻數\n值較高的一項給予傷害。對\n正處於太晶化的對手效果絕\n佳。自己的攻擊和特攻會降\n低。（星晶"}, "silkTrap": {"name": "線阱", "effect": "用絲設置陷阱。防住對方攻\n擊的同時，能夠降低所接觸\n到的對手的速度"}, "axeKick": {"name": "下壓踢", "effect": "將踢起的腳跟往下劈向對手\n進行攻擊。有時會使對手混\n亂。如果劈偏則自己會受到\n傷害"}, "lastRespects": {"name": "掃墓", "effect": "爲了化解夥伴的悔恨而進行\n攻擊。被打倒的我方寶可夢\n越多，招式的威力越高"}, "luminaCrash": {"name": "琉光衝激", "effect": "放出連精神都能影響到的奇\n妙怪光進行攻擊。會大幅降\n低對方的特防"}, "orderUp": {"name": "上菜", "effect": "以瀟灑的身手進行攻擊。若\n口中有米立龍，會按其樣子\n提高能力"}, "jetPunch": {"name": "噴射拳", "effect": "將激流覆蓋於拳頭，以肉眼\n無法辨識的速度打出拳擊。\n必定能夠先制攻擊"}, "spicyExtract": {"name": "辣椒精華", "effect": "放出極爲辛辣的精華。對手\n的攻擊會大幅提高，防禦會\n大幅降低"}, "spinOut": {"name": "疾速轉輪", "effect": "通過往腿上增加負荷，以激\n烈的旋轉給予對手傷害。自\n己的速度會大幅降低"}, "populationBomb": {"name": "鼠數兒", "effect": "夥伴們會紛紛趕來集合，以\n羣體行動給予對手攻擊。連\n續命中１～１０次"}, "iceSpinner": {"name": "冰旋", "effect": "腳上覆蓋薄冰，旋轉着撞擊\n對手。通過旋轉的動作破壞\n場地"}, "glaiveRush": {"name": "巨劍突擊", "effect": "有勇無謀的捨身突擊。使出\n招式後，對手的攻擊必定會\n命中，且傷害會變成２倍"}, "revivalBlessing": {"name": "復生祈禱", "effect": "通過以慈愛之心祈禱，讓陷\n入昏厥的後備寶可夢以回覆\n一半ＨＰ的狀態復活"}, "saltCure": {"name": "鹽醃", "effect": "使對手陷入鹽醃狀態，每回\n合給予對手傷害。對手爲鋼\n或水屬性時會更痛苦"}, "tripleDive": {"name": "三連鑽", "effect": "以默契的跳躍濺起水花擊向\n對手。連續３次給予傷害"}, "mortalSpin": {"name": "晶光轉轉", "effect": "通過旋轉來攻擊對手。可以\n擺脫綁緊、緊束、寄生種子\n等招式。還能讓對手陷入中\n毒狀態"}, "doodle": {"name": "描繪", "effect": "把握並映射出對手的本質，\n讓自己和同伴寶可夢的特性\n變得和對手相同"}, "filletAway": {"name": "甩肉", "effect": "削減自己的ＨＰ，大幅提高\n攻擊和特攻以及速度"}, "kowtowCleave": {"name": "僕刀", "effect": "下跪讓對手大意後發起襲擊\n劈向對手。攻擊必定會命中"}, "flowerTrick": {"name": "千變萬花", "effect": "將做了手腳的花束扔向對手\n進行攻擊。必定會命中，且\n會擊中要害"}, "torchSong": {"name": "閃焰高歌", "effect": "如唱歌一樣噴出熊熊燃燒的\n火焰燒焦對手。會提高自己\n的特攻"}, "aquaStep": {"name": "流水旋舞", "effect": "以盈盈欲滴的輕快步伐戲耍\n對手並給予其傷害。會提高\n自己的速度"}, "ragingBull": {"name": "怒牛", "effect": "狂怒暴牛的猛烈衝撞。招式\n的屬性隨形態改變，光牆和\n反射壁等招式也能破壞"}, "makeItRain": {"name": "淘金潮", "effect": "扔出大量硬幣攻擊。自己的\n特攻會降低，戰鬥後還可以\n拿到錢"}, "psyblade": {"name": "精神劍", "effect": "用無形的利刃劈開對手。處\n於電氣場地時，招式威力會\n變成１．５倍"}, "hydroSteam": {"name": "水蒸氣", "effect": "將煮得翻滾的開水猛烈地噴\n向對手。日照強烈時，招式\n威力不但不會降低，還會變\n成１．５倍"}, "ruination": {"name": "大災難", "effect": "引發毀滅性的災厄，使對手\n的ＨＰ減半"}, "collisionCourse": {"name": "全開猛撞", "effect": "邊變形邊兇暴地落下，並引\n發起古老的大爆炸。若針對\n到弱點，威力會進一步"}, "electroDrift": {"name": "閃電猛衝", "effect": "邊變形邊高速奔走，並以未\n知的電擊貫穿對手。若針對\n到弱點，威力會進一步"}, "shedTail": {"name": "斷尾", "effect": "削減自己的ＨＰ，製造分身\n後會返回，並和後備寶可夢\n進行替換"}, "chillyReception": {"name": "冷笑話", "effect": "留下冷場的冷笑話後，和後\n備寶可夢進行替換。在５回\n合內會下雪"}, "tidyUp": {"name": "大掃除", "effect": "將撒菱、隱形巖、黏黏網、\n毒菱、替身全部掃除掉。自\n己的攻擊和速度會提高"}, "snowscape": {"name": "雪景", "effect": "在５回合內會下雪。冰屬性\n的防禦會提高"}, "pounce": {"name": "蟲撲", "effect": "飛撲向對手攻擊。會降低對\n手的速度"}, "trailblaze": {"name": "起草", "effect": "跳出草叢進行攻擊。通過輕\n快的步伐會提高自己的速度"}, "chillingWater": {"name": "潑冷水", "effect": "潑灑冰冷得足以讓對手失去\n活力的水進行攻擊。會降低\n對手的攻擊"}, "hyperDrill": {"name": "強力鑽", "effect": "急速旋轉尖銳的身體部位貫\n穿對手。可以無視守住和看\n穿等招式"}, "twinBeam": {"name": "雙光束", "effect": "從兩眼發射出神奇的光線攻\n擊。連續２次給予傷害"}, "rageFist": {"name": "憤怒之拳", "effect": "將憤怒化爲力量攻擊。受到\n攻擊的次數越多，招式的威\n力越高"}, "armorCannon": {"name": "鎧農炮", "effect": "熊熊燃燒自己的鎧甲，將其\n做成炮彈射出攻擊。自己的\n防禦和特防會降低"}, "bitterBlade": {"name": "悔念劍", "effect": "將對世間的留戀聚集於劍尖，\n並斬擊對手。可以回覆給\n予對手傷害的一半ＨＰ"}, "doubleShock": {"name": "電光雙擊", "effect": "將全身所有的電力放出，給\n予對手大大的傷害。自己的\n電屬性將會消失"}, "gigatonHammer": {"name": "巨力錘", "effect": "連同身體轉起巨大的錘子進\n行攻擊。這個招式無法連續\n使出２次"}, "comeuppance": {"name": "復仇", "effect": "使出招式前，將最後受到的\n招式的傷害大力返還給對手"}, "aquaCutter": {"name": "水波刀", "effect": "如刀刃般噴射出加壓的水切\n開對手。容易擊中要害"}, "blazingTorque": {"name": "灼熱暴衝", "effect": "攻擊目標造成傷害，\n有30%的幾率使目標陷入\n灼傷狀態。"}, "wickedTorque": {"name": "黑暗暴衝", "effect": "攻擊目標造成傷害，\n有30%的幾率使目標陷入\n睡眠狀態。"}, "noxiousTorque": {"name": "劇毒暴衝", "effect": "攻擊目標造成傷害，\n有30%的幾率使目標陷入\n中毒狀態。"}, "combatTorque": {"name": "格鬥暴衝", "effect": "攻擊目標造成傷害，\n有30%的幾率使目標陷入\n麻痹狀態。此招式可以命中\n幽靈屬性的寶可夢。"}, "magicalTorque": {"name": "魔法暴衝", "effect": "攻擊目標造成傷害，\n有30%的幾率使目標陷入\n混亂狀態。"}, "bloodMoon": {"name": "血月", "effect": "從赤紅如血的滿月發射出全\n部的氣勢。這個招式無法連\n續使出２次"}, "matchaGotcha": {"name": "刷刷茶炮", "effect": "發射經攪拌的茶的大炮，可\n以回覆給予對手傷害的一半\nＨＰ，有時會讓對手陷入灼\n傷狀態"}, "syrupBomb": {"name": "糖漿炸彈", "effect": "使粘稠的麥芽糖漿爆炸，讓\n對手陷入滿身糖狀態，在３\n回合內持續降低其速度"}, "ivyCudgel": {"name": "棘藤棒", "effect": "用纏有藤蔓的棍棒毆打。屬\n性會隨所戴的面具而改變。\n容易擊中要害"}, "electroShot": {"name": "電光束", "effect": "第１回合收集電力提高特攻，\n第２回合將高壓的電力發\n射出去。下雨天氣時能立刻\n發射"}, "teraStarstorm": {"name": "晶光星羣", "effect": "照射出結晶的力量來驅逐敵\n人。太樂巴戈斯在星晶形態\n下使出時，能對所有對手造\n成傷害"}, "fickleBeam": {"name": "隨機光", "effect": "發射光線進行攻擊。有時其\n他的頭也會合力發射鐳射，\n讓招式威力變成２倍"}, "burningBulwark": {"name": "火焰守護", "effect": "用超高溫的體毛防住對手攻\n擊的同時，讓接觸到自己的\n對手灼傷"}, "thunderclap": {"name": "迅雷", "effect": "可以比對手先使出電擊進行\n攻擊。對手使出的招式如果\n不是攻擊招式則會失敗"}, "mightyCleave": {"name": "強刃攻擊", "effect": "用積蓄在頭部的光來斬切對\n手。可以無視守護進行攻擊"}, "tachyonCutter": {"name": "迅子利刃", "effect": "接連發射出粒子的利刃，連\n續２次給予傷害。攻擊必定\n會命中"}, "hardPress": {"name": "硬壓", "effect": "用手臂或鉗子壓迫對手。對\n手剩餘的ＨＰ越多，威力越\n大"}, "dragonCheer": {"name": "龍聲鼓舞", "effect": "以龍之鼓舞提高士氣，讓我\n方的招式變得容易擊中要害。\n對龍屬性的鼓舞效果會更\n強"}, "alluringVoice": {"name": "魅誘之聲", "effect": "用天使般的歌聲攻擊對手。\n會讓此回合內能力有提高的\n寶可夢陷入混亂狀態"}, "temperFlare": {"name": "豁出去", "effect": "以自暴自棄的氣勢進行攻擊。\n如果上一回合招式沒有命\n中，威力就會翻倍"}, "supercellSlam": {"name": "閃電強襲", "effect": "讓身體帶電後壓向對手。如\n果沒有命中則自己會受到傷\n害"}, "psychicNoise": {"name": "精神噪音", "effect": "用令對手不舒服的音波進行\n攻擊。讓對手在２回合內無\n法通過招式、特性或攜帶的\n道具回覆ＨＰ"}, "upperHand": {"name": "快手還擊", "effect": "察覺到對手的動作後用掌根\n攻擊，讓對手畏縮。如果對\n手使出的招式不是先制攻擊，\n則會失敗"}, "malignantChain": {"name": "邪毒鎖鏈", "effect": "用由毒形成的鎖鏈纏住對手\n注入毒素加以侵蝕。有時會\n讓對手陷入劇毒狀態"}}