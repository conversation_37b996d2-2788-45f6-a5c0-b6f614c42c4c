{"ModifierType": {"AddPokeballModifierType": {"name": "{{modifierCount}}x {{pokeball<PERSON>ame}}", "description": "获得 {{pokeballName}}x {{modifierCount}} (已有：{{pokeballAmount}}) \n捕捉倍率：{{catchRate}}。"}, "AddVoucherModifierType": {"name": "{{modifierCount}}x {{voucherTypeName}}", "description": "获得 {{voucherTypeName}} x{{modifierCount}}。"}, "PokemonHeldItemModifierType": {"extra": {"inoperable": "{{pokemon<PERSON>ame}} 无法携带\n这个物品！", "tooMany": "{{pokemon<PERSON>ame}} 已有太多\n这个物品！"}}, "PokemonHpRestoreModifierType": {"description": "为一只宝可梦回复{{restorePoints}}HP或{{restorePercent}}%HP，取较大值。", "extra": {"fully": "为一只宝可梦回复全部HP。", "fullyWithStatus": "为一只宝可梦回复全部HP并消除所有负面\n状态。"}}, "PokemonReviveModifierType": {"description": "复活一只宝可梦并回复 {{restorePercent}}% HP。"}, "PokemonStatusHealModifierType": {"description": "为一只宝可梦消除所有负面状态。"}, "PokemonPpRestoreModifierType": {"description": "为一只宝可梦的一个招式回复 {{restorePoints}} PP。", "extra": {"fully": "完全回复一只宝可梦一个招式的PP。"}}, "PokemonAllMovePpRestoreModifierType": {"description": "为一只宝可梦的所有招式回复 {{restorePoints}} PP。", "extra": {"fully": "为一只宝可梦的所有招式回复所有PP。"}}, "PokemonPpUpModifierType": {"description": "选择一只宝可梦的一个招式使用\n使其PP最大值提升基础的20％ (最多3次)。"}, "PokemonNatureChangeModifierType": {"name": "{{natureName}}薄荷", "description": "将一只宝可梦的性格改为{{natureName}}并为\n该宝可梦永久解锁该性格。"}, "DoubleBattleChanceBoosterModifierType": {"description": "遭遇双打概率提升四倍，持续{{battleCount}}场战斗。"}, "TempStatStageBoosterModifierType": {"description": "提升全队的{{stat}}{{amount}}，持续5场战斗。", "extra": {"stage": "1阶", "percentage": "30%"}}, "AttackTypeBoosterModifierType": {"description": "一只宝可梦的{{moveType}}系招式威力提升20%。"}, "PokemonLevelIncrementModifierType": {"description": "使一只宝可梦的等级提升{{levels}}级。"}, "AllPokemonLevelIncrementModifierType": {"description": "使所有宝可梦的等级提升{{levels}}级。"}, "BaseStatBoosterModifierType": {"description": "增加10%持有者的{{stat}}，\n个体值越高堆叠上限越高。"}, "PokemonBaseStatFlatModifierType": {"name": "森之羊羹", "description": "增加持有者{{stats}}的基础属性{{statValue}}，来自于一场怪梦。"}, "AllPokemonFullHpRestoreModifierType": {"description": "所有宝可梦完全回复HP。"}, "AllPokemonFullReviveModifierType": {"description": "复活所有濒死的宝可梦，\n并完全回复HP。"}, "MoneyRewardModifierType": {"description": "获得{{moneyMultiplier}}金钱(₽{{moneyAmount}})。", "extra": {"small": "少量", "moderate": "中等", "large": "大量"}}, "ExpBoosterModifierType": {"description": "经验值获取量增加{{boostPercent}}%。"}, "PokemonExpBoosterModifierType": {"description": "持有者经验值获取量增加{{boostPercent}}%。"}, "PokemonFriendshipBoosterModifierType": {"description": "亲密度获取量增加50%。"}, "PokemonMoveAccuracyBoosterModifierType": {"description": "招式命中率增加{{accuracyAmount}}。"}, "PokemonMultiHitModifierType": {"description": "将持有者本次伤害的25%转化为一次独立的攻击。"}, "TmModifierType": {"name": "招式学习器\n{{moveId}} - {{moveName}}", "description": "教会一只宝可梦{{moveName}}。"}, "TmModifierTypeWithInfo": {"name": "招式学习器\n{{moveId}} - {{moveName}}", "description": "教会一只宝可梦{{moveName}}\n(按住C或者Shift查看更多信息)。"}, "EvolutionItemModifierType": {"description": "使某些宝可梦进化。"}, "FormChangeItemModifierType": {"description": "使某些宝可梦更改形态。"}, "FusePokemonModifierType": {"description": "融合两只宝可梦（改变特性, 平分基础点数\n和属性, 共享招式池）。"}, "TerastallizeModifierType": {"name": "{{teraType}}太晶碎块", "description": "将宝可梦的太晶属性变为{{teraType}}。"}, "ContactHeldItemTransferChanceModifierType": {"description": "攻击时{{chancePercent}}%概率\n偷取对手物品。"}, "TurnHeldItemTransferModifierType": {"description": "持有者每回合从对手那里\n获得一个持有的物品。"}, "EnemyAttackStatusEffectChanceModifierType": {"description": "攻击时{{chancePercent}}%概率造成{{statusEffect}}。"}, "EnemyEndureChanceModifierType": {"description": "敌方增加{{chancePercent}}%的概率\n在本回合不会倒下。"}, "RARE_CANDY": {"name": "神奇糖果"}, "RARER_CANDY": {"name": "超神奇糖果"}, "MEGA_BRACELET": {"name": "MEGA手镯", "description": "能让携带着MEGA石战斗的宝可梦\n进行MEGA进化。"}, "DYNAMAX_BAND": {"name": "极巨腕带", "description": "能让携带着极巨菇菇战斗的宝可梦\n进行超极巨化。"}, "TERA_ORB": {"name": "太晶珠", "description": "能让战斗的宝可梦进行太晶化。\n缓慢充能，太晶碎片也会出现在商店中。"}, "MAP": {"name": "地图", "description": "有概率允许你在切换地区时\n选择目的地。"}, "POTION": {"name": "伤药"}, "SUPER_POTION": {"name": "好伤药"}, "HYPER_POTION": {"name": "厉害伤药"}, "MAX_POTION": {"name": "全满药"}, "FULL_RESTORE": {"name": "全复药"}, "REVIVE": {"name": "活力碎片"}, "MAX_REVIVE": {"name": "活力块"}, "FULL_HEAL": {"name": "万灵药"}, "SACRED_ASH": {"name": "圣灰"}, "REVIVER_SEED": {"name": "复活种子", "description": "受到技能攻击伤害濒死时，\n恢复该宝可梦的HP至1/2。"}, "WHITE_HERB": {"name": "白色香草", "description": "当携带它的宝可梦能力降低时，\n仅能回到之前的状态１次。"}, "ETHER": {"name": "PP单项小补剂"}, "MAX_ETHER": {"name": "PP单项全补剂"}, "ELIXIR": {"name": "PP多项小补剂"}, "MAX_ELIXIR": {"name": "PP多项全补剂"}, "PP_UP": {"name": "PP提升剂"}, "PP_MAX": {"name": "PP极限提升剂"}, "LURE": {"name": "引虫香水"}, "SUPER_LURE": {"name": "白银香水"}, "MAX_LURE": {"name": "黄金香水"}, "MEMORY_MUSHROOM": {"name": "回忆蘑菇", "description": "回忆一个宝可梦已经遗忘的招式。"}, "EXP_SHARE": {"name": "学习装置", "description": "未参加对战的宝可梦获得20%的经验值。"}, "EXP_BALANCE": {"name": "均衡型学习装置", "description": "经验值会更多分给队伍中等级最低的宝可梦。"}, "OVAL_CHARM": {"name": "圆形护符", "description": "当多只宝可梦参与战斗，分别获得总经验值\n10%的额外经验值。"}, "EXP_CHARM": {"name": "经验护符"}, "SUPER_EXP_CHARM": {"name": "超级经验护符"}, "GOLDEN_EXP_CHARM": {"name": "黄金经验护符"}, "LUCKY_EGG": {"name": "幸运蛋"}, "GOLDEN_EGG": {"name": "金蛋"}, "SOOTHE_BELL": {"name": "安抚之铃"}, "SCOPE_LENS": {"name": "焦点镜", "description": "能看见弱点的镜片。携带它的宝可梦的招式\n会变得容易击中要害。"}, "DIRE_HIT": {"name": "要害攻击", "extra": {"raises": "会心"}}, "LEEK": {"name": "大葱", "description": "非常长且坚硬的茎。让大葱鸭携带后，\n招式会变得容易击中要害。"}, "EVIOLITE": {"name": "进化奇石", "description": "携带后，还能进化的宝可梦的\n防御和特防就会提高。"}, "SOUL_DEW": {"name": "心之水滴", "description": "增加10%宝可梦性格对数值的影响 (加算)。"}, "NUGGET": {"name": "金珠"}, "BIG_NUGGET": {"name": "巨大金珠"}, "RELIC_GOLD": {"name": "古代金币"}, "AMULET_COIN": {"name": "护符金币", "description": "获得的金钱增加20%。"}, "GOLDEN_PUNCH": {"name": "黄金拳头", "description": "将50%造成的伤害转换为金钱。"}, "COIN_CASE": {"name": "代币盒", "description": "每10场战斗, 获得自己金钱10%的利息。"}, "LOCK_CAPSULE": {"name": "上锁的容器", "description": "允许在商店中刷新物品时，\n锁定物品的稀有度。"}, "GRIP_CLAW": {"name": "紧缠钩爪"}, "WIDE_LENS": {"name": "广角镜"}, "MULTI_LENS": {"name": "多重镜"}, "HEALING_CHARM": {"name": "治愈护符", "description": "HP回复量增加10% (不含复活)。"}, "CANDY_JAR": {"name": "糖果罐", "description": "神奇糖果提供的升级额外增加1级。"}, "BERRY_POUCH": {"name": "树果袋", "description": "使用树果时增加30%的几率不会消耗树果。"}, "FOCUS_BAND": {"name": "气势头带", "description": "携带该道具的宝可梦增加10%几率在受到攻击\n而将陷入濒死状态时，保留1点HP不陷入濒死状态。"}, "QUICK_CLAW": {"name": "先制之爪", "description": "增加10%的几率无视速度优先使出招式\n(先制技能优先)。"}, "KINGS_ROCK": {"name": "王者之证", "description": "使用任意原本不会造成畏缩状态的攻击，\n增加10%几率使目标陷入畏缩状态。"}, "LEFTOVERS": {"name": "吃剩的东西", "description": "携带后，在每个回合结束时恢复\n最大HP的1/16。"}, "SHELL_BELL": {"name": "贝壳之铃", "description": "携带后，在攻击对方成功造成伤害时，\n携带者的HP会恢复其所造成伤害的1/8。"}, "TOXIC_ORB": {"name": "剧毒宝珠", "description": "触碰后会放出毒的神奇宝珠。\n携带后，在战斗时会变成剧毒状态。"}, "FLAME_ORB": {"name": "火焰宝珠", "description": "触碰后会放出热量的神奇宝珠。\n携带后，在战斗时会变成灼伤状态。"}, "MYSTICAL_ROCK": {"name": "神秘石块", "description": "每堆叠一次，可将招式或能力造成的\n地形和天气的持续时间延长 2 回合。"}, "EVOLUTION_TRACKER_GIMMIGHOUL": {"name": "宝藏金币", "description": "这个宝可梦最爱金币！多收集点金币的话会发生什么呢？"}, "EVOLUTION_TRACKER_PRIMEAPE": {"name": "愤怒之拳", "description": "当怒气突破临界点时，它获得了能够摆脱肉体束缚的力量！\n用愤怒之拳来将愤怒化为力量吧！"}, "EVOLUTION_TRACKER_STANTLER": {"name": "屏障猛攻", "description": "惊角鹿为了适应严酷的环境而进化。用屏障猛攻来加强它的防御吧！"}, "BATON": {"name": "接力棒", "description": "允许在切换宝可梦时保留能力变化, 对陷阱\n同样生效。"}, "SHINY_CHARM": {"name": "闪耀护符", "description": "显著增加野生宝可梦的闪光概率。"}, "ABILITY_CHARM": {"name": "特性护符", "description": "显著增加野生宝可梦有隐藏特性的概率。"}, "CATCHING_CHARM": {"name": "防晃护符", "description": "增加关键捕获的几率。"}, "IV_SCANNER": {"name": "个体值探测器", "description": "允许扫描野生宝可梦的个体值。"}, "DNA_SPLICERS": {"name": "基因之楔"}, "MINI_BLACK_HOLE": {"name": "迷你黑洞"}, "GOLDEN_POKEBALL": {"name": "黄金精灵球", "description": "在每场战斗结束后，增加一个额外物品选项。"}, "ENEMY_DAMAGE_BOOSTER": {"name": "伤害硬币", "description": "造成5%额外伤害（乘算）。"}, "ENEMY_DAMAGE_REDUCTION": {"name": "防御硬币", "description": "受到2.5%更少伤害（乘算）。"}, "ENEMY_HEAL": {"name": "回复硬币", "description": "每回合回复2%最大HP。"}, "ENEMY_ATTACK_POISON_CHANCE": {"name": "剧毒硬币"}, "ENEMY_ATTACK_PARALYZE_CHANCE": {"name": "麻痹硬币"}, "ENEMY_ATTACK_BURN_CHANCE": {"name": "灼烧硬币"}, "ENEMY_STATUS_EFFECT_HEAL_CHANCE": {"name": "万灵药硬币", "description": "增加2.5%每回合治愈异常状态的概率。"}, "ENEMY_ENDURE_CHANCE": {"name": "忍受硬币"}, "ENEMY_FUSED_CHANCE": {"name": "融合硬币", "description": "增加1%野生融合宝可梦出现概率。"}, "MYSTERY_ENCOUNTER_SHUCKLE_JUICE_GOOD": {"name": "甜壶壶果汁", "description": "增加持有者的所有基础个体值。你被壶壶祝福了！"}, "MYSTERY_ENCOUNTER_SHUCKLE_JUICE_BAD": {"name": "腐烂的壶壶果汁", "description": "减少持有者的所有基础个体值。你被壶壶诅咒了！"}, "MYSTERY_ENCOUNTER_BLACK_SLUDGE": {"name": "黑色污泥", "description": "由于恶臭扑鼻，商店会以非常高昂的价格向您出售商品。"}, "MYSTERY_ENCOUNTER_MACHO_BRACE": {"name": "强制锻炼器", "description": "击败对手后获得一层锻炼等级。每层会略微提升属性，\n达到最大层数时还会获得额外奖励。"}, "MYSTERY_ENCOUNTER_OLD_GATEAU": {"name": "森之羊羔", "description": "增加持有者的部分个体值。"}, "MYSTERY_ENCOUNTER_GOLDEN_BUG_NET": {"name": "金捕虫网", "description": "赋予主人好运，使其更容易\n找到虫属性宝可梦，手感很奇妙。"}}, "SpeciesBoosterItem": {"LIGHT_BALL": {"name": "电气球", "description": "让皮卡丘携带后，\n攻击和特攻就会提高的神奇之球。"}, "THICK_CLUB": {"name": "粗骨头", "description": "某种坚硬的骨头。\n让卡拉卡拉或嘎啦嘎啦携带后，攻击就会提高。"}, "METAL_POWDER": {"name": "金属粉", "description": "让百变怪携带后，防御就会提高的神奇粉末。\n非常细腻坚硬。"}, "QUICK_POWDER": {"name": "速度粉", "description": "让百变怪携带后，速度就会提高的神奇粉末。\n非常细腻坚硬。"}, "DEEP_SEA_SCALE": {"name": "深海鳞片", "description": "闪耀着粉红色光泽的鳞片。$让珍珠贝携带后，特防就会提高。"}, "DEEP_SEA_TOOTH": {"name": "深海之牙", "description": "散发着闪亮银光的锐利牙齿。$让珍珠贝携带后，特攻就会提高。"}}, "TempStatStageBoosterItem": {"x_attack": "力量强化", "x_defense": "防御强化", "x_sp_atk": "特攻强化", "x_sp_def": "特防强化", "x_speed": "速度强化", "x_accuracy": "命中强化"}, "AttackTypeBoosterItem": {"silk_scarf": "丝绸围巾", "black_belt": "黑带", "sharp_beak": "锐利鸟嘴", "poison_barb": "毒针", "soft_sand": "柔软沙子", "hard_stone": "硬石头", "silver_powder": "银粉", "spell_tag": "诅咒之符", "metal_coat": "金属膜", "charcoal": "木炭", "mystic_water": "神秘水滴", "miracle_seed": "奇迹种子", "magnet": "磁铁", "twisted_spoon": "弯曲的汤匙", "never_melt_ice": "不融冰", "dragon_fang": "龙之牙", "black_glasses": "黑色眼镜", "fairy_feather": "妖精之羽"}, "BaseStatBoosterItem": {"hp_up": "HP增强剂", "protein": "攻击增强剂", "iron": "防御增强剂", "calcium": "特攻增强剂", "zinc": "特防增强剂", "carbos": "速度增强剂"}, "EvolutionItem": {"NONE": "无", "LINKING_CORD": "联系绳", "SUN_STONE": "日之石", "MOON_STONE": "月之石", "LEAF_STONE": "叶之石", "FIRE_STONE": "火之石", "WATER_STONE": "水之石", "THUNDER_STONE": "雷之石", "ICE_STONE": "冰之石", "DUSK_STONE": "暗之石", "DAWN_STONE": "觉醒之石", "SHINY_STONE": "光之石", "CRACKED_POT": "破裂的茶壶", "SWEET_APPLE": "甜甜苹果", "TART_APPLE": "酸酸苹果", "STRAWBERRY_SWEET": "草莓糖饰", "UNREMARKABLE_TEACUP": "凡作茶碗", "UPGRADE": "升级数据", "DUBIOUS_DISC": "可疑补丁", "DRAGON_SCALE": "龙之鳞片", "PRISM_SCALE": "美丽鳞片", "RAZOR_CLAW": "锐利之爪", "RAZOR_FANG": "锐利之牙", "REAPER_CLOTH": "灵界之布", "ELECTIRIZER": "电力增幅器", "MAGMARIZER": "熔岩增幅器", "PROTECTOR": "护具", "SACHET": "香袋", "WHIPPED_DREAM": "泡沫奶油", "LEADERS_CREST": "头领凭证", "SUN_FLUTE": "太阳之笛", "MOON_FLUTE": "月亮之笛", "CHIPPED_POT": "缺损的茶壶", "BLACK_AUGURITE": "黑奇石", "GALARICA_CUFF": "伽勒豆蔻手环", "GALARICA_WREATH": "伽勒豆蔻花圈", "PEAT_BLOCK": "泥炭块", "AUSPICIOUS_ARMOR": "庆祝之铠", "MALICIOUS_ARMOR": "咒术之铠", "MASTERPIECE_TEACUP": "杰作茶碗", "METAL_ALLOY": "复合金属", "SCROLL_OF_DARKNESS": "恶之挂轴", "SCROLL_OF_WATERS": "水之挂轴", "SYRUPY_APPLE": "蜜汁苹果"}, "FormChangeItem": {"NONE": "无", "ABOMASITE": "暴雪王进化石", "ABSOLITE": "阿勃梭鲁进化石", "AERODACTYLITE": "化石翼龙进化石", "AGGRONITE": "波士可多拉进化石", "ALAKAZITE": "胡地进化石", "ALTARIANITE": "七夕青鸟进化石", "AMPHAROSITE": "电龙进化石", "AUDINITE": "差不多娃娃进化石", "BANETTITE": "诅咒娃娃进化石", "BEEDRILLITE": "大针蜂进化石", "BLASTOISINITE": "水箭龟进化石", "BLAZIKENITE": "火焰鸡进化石", "CAMERUPTITE": "喷火驼进化石", "CHARIZARDITE_X": "喷火龙进化石Ｘ", "CHARIZARDITE_Y": "喷火龙进化石Ｙ", "DIANCITE": "蒂安希进化石", "GALLADITE": "艾路雷朵进化石", "GARCHOMPITE": "烈咬陆鲨进化石", "GARDEVOIRITE": "沙奈朵进化石", "GENGARITE": "耿鬼进化石", "GLALITITE": "冰鬼护进化石", "GYARADOSITE": "暴鲤龙进化石", "HERACRONITE": "赫拉克罗斯进化石", "HOUNDOOMINITE": "黑鲁加进化石", "KANGASKHANITE": "袋兽进化石", "LATIASITE": "拉帝亚斯进化石", "LATIOSITE": "拉帝欧斯进化石", "LOPUNNITE": "长耳兔进化石", "LUCARIONITE": "路卡利欧进化石", "MANECTITE": "雷电兽进化石", "MAWILITE": "大嘴娃进化石", "MEDICHAMITE": "恰雷姆进化石", "METAGROSSITE": "巨金怪进化石", "MEWTWONITE_X": "超梦进化石Ｘ", "MEWTWONITE_Y": "超梦进化石Ｙ", "PIDGEOTITE": "大比鸟进化石", "PINSIRITE": "凯罗斯进化石", "RAYQUAZITE": "烈空坐进化石", "SABLENITE": "勾魂眼进化石", "SALAMENCITE": "暴飞龙进化石", "SCEPTILITE": "蜥蜴王进化石", "SCIZORITE": "巨钳螳螂进化石", "SHARPEDONITE": "巨牙鲨进化石", "SLOWBRONITE": "呆壳兽进化石", "STEELIXITE": "大钢蛇进化石", "SWAMPERTITE": "巨沼怪进化石", "TYRANITARITE": "班基拉斯进化石", "VENUSAURITE": "妙蛙花进化石", "BLUE_ORB": "靛蓝色宝珠", "RED_ORB": "朱红色宝珠", "SHARP_METEORITE": "锐利陨石", "HARD_METEORITE": "坚硬陨石", "SMOOTH_METEORITE": "光滑陨石", "ADAMANT_CRYSTAL": "大金刚宝玉", "LUSTROUS_GLOBE": "大白宝玉", "GRISEOUS_CORE": "大白金宝玉", "REVEAL_GLASS": "现形镜", "GRACIDEA": "葛拉西蒂亚花", "MAX_MUSHROOMS": "极巨菇菇", "DARK_STONE": "黑暗石", "LIGHT_STONE": "光明<PERSON>", "PRISON_BOTTLE": "惩戒之壶", "N_LUNARIZER": "奈克洛露奈合体器", "N_SOLARIZER": "奈克洛索尔合体器", "RUSTED_SWORD": "腐朽的剑", "RUSTED_SHIELD": "腐朽的盾", "ICY_REINS_OF_UNITY": "牵绊缰绳(冰)", "SHADOW_REINS_OF_UNITY": "牵绊缰绳(幽灵)", "WELLSPRING_MASK": "水井面具", "HEARTHFLAME_MASK": "火灶面具", "CORNERSTONE_MASK": "础石面具", "SHOCK_DRIVE": "闪电卡带", "BURN_DRIVE": "火焰卡带", "CHILL_DRIVE": "冰冻卡带", "DOUSE_DRIVE": "水流卡带", "ULTRANECROZIUM_Z": "究极奈克洛Z", "FIST_PLATE": "拳头石板", "SKY_PLATE": "蓝天石板", "TOXIC_PLATE": "剧毒石板", "EARTH_PLATE": "大地石板", "STONE_PLATE": "岩石石板", "INSECT_PLATE": "玉虫石板", "SPOOKY_PLATE": "妖怪石板", "IRON_PLATE": "钢铁石板", "FLAME_PLATE": "火球石板", "SPLASH_PLATE": "水滴石板", "MEADOW_PLATE": "碧绿石板", "ZAP_PLATE": "雷电石板", "MIND_PLATE": "神奇石板", "ICICLE_PLATE": "冰柱石板", "DRACO_PLATE": "龙之石板", "DREAD_PLATE": "恶颜石板", "PIXIE_PLATE": "妖精石板", "BLANK_PLATE": "净空石板", "LEGEND_PLATE": "传说石板", "FIGHTING_MEMORY": "战斗存储碟", "FLYING_MEMORY": "飞翔存储碟", "POISON_MEMORY": "毒存储碟", "GROUND_MEMORY": "大地存储碟", "ROCK_MEMORY": "岩石存储碟", "BUG_MEMORY": "虫子存储碟", "GHOST_MEMORY": "幽灵存储碟", "STEEL_MEMORY": "钢铁存储碟", "FIRE_MEMORY": "火焰存储碟", "WATER_MEMORY": "清水存储碟", "GRASS_MEMORY": "青草存储碟", "ELECTRIC_MEMORY": "电子存储碟", "PSYCHIC_MEMORY": "精神存储碟", "ICE_MEMORY": "冰雪存储碟", "DRAGON_MEMORY": "龙存储碟", "DARK_MEMORY": "黑暗存储碟", "FAIRY_MEMORY": "妖精存储碟", "NORMAL_MEMORY": "一般存储碟"}}