{"pikachu": "普通", "pikachuCosplay": "换装", "pikachuCoolCosplay": "摇滚巨星", "pikachuBeautyCosplay": "贵妇", "pikachuCuteCosplay": "流行偶像", "pikachuSmartCosplay": "博士", "pikachuToughCosplay": "面罩摔跤手", "pikachuPartner": "搭档", "eevee": "普通", "eeveePartner": "搭档", "pichu": "普通", "pichuSpiky": "刺刺耳", "unownA": "A", "unownB": "B", "unownC": "C", "unownD": "D", "unownE": "E", "unownF": "F", "unownG": "G", "unownH": "H", "unownI": "I", "unownJ": "J", "unownK": "K", "unownL": "L", "unownM": "M", "unownN": "N", "unownO": "O", "unownP": "P", "unownQ": "Q", "unownR": "R", "unownS": "S", "unownT": "T", "unownU": "U", "unownV": "V", "unownW": "W", "unownX": "X", "unownY": "Y", "unownZ": "Z", "unownExclamation": "!", "unownQuestion": "?", "castform": "普通", "castformSunny": "晴天", "castformRainy": "雨天", "castformSnowy": "雪天", "deoxysNormal": "普通", "deoxysAttack": "攻击", "deoxysDefense": "防御", "deoxysSpeed": "速度", "burmyPlant": "草木蓑衣", "burmySandy": "砂土蓑衣", "burmyTrash": "垃圾蓑衣", "cherubiOvercast": "阴天", "cherubiSunshine": "晴天", "shellosEast": "东海", "shellosWest": "西海", "rotom": "普通", "rotomHeat": "加热", "rotomWash": "清洗", "rotomFrost": "结冰", "rotomFan": "旋转", "rotomMow": "切割", "dialga": "普通", "dialgaOrigin": "起源", "palkia": "普通", "palkiaOrigin": "起源", "giratinaAltered": "别种", "giratinaOrigin": "起源", "shayminLand": "陆上", "shayminSky": "天空", "basculinRedStriped": "红条纹", "basculinBlueStriped": "蓝条纹", "basculinWhiteStriped": "白条纹", "darumaka": "普通模式", "darumakaZen": "达摩模式", "deerlingSpring": "春天", "deerlingSummer": "夏天", "deerlingAutumn": "秋天", "deerlingWinter": "冬天", "tornadusIncarnate": "化身", "tornadusTherian": "灵兽", "thundurusIncarnate": "化身", "thundurusTherian": "灵兽", "landorusIncarnate": "化身", "landorusTherian": "灵兽", "kyurem": "普通", "kyuremBlack": "暗黑", "kyuremWhite": "焰白", "keldeoOrdinary": "平常的样子", "keldeoResolute": "觉悟的样子", "meloettaAria": "歌声", "meloettaPirouette": "舞步", "genesect": "普通", "genesectShock": "闪电驱动", "genesectBurn": "燃烧驱动", "genesectChill": "寒冰驱动", "genesectDouse": "水流驱动", "froakie": "普通", "froakieBattleBond": "牵绊变身", "froakieAsh": "小智", "scatterbugMeadow": "花园花纹", "scatterbugIcySnow": "冰雪花纹", "scatterbugPolar": "雪国花纹", "scatterbugTundra": "雪原花纹", "scatterbugContinental": "大陆花纹", "scatterbugGarden": "庭园花纹", "scatterbugElegant": "高雅花纹", "scatterbugModern": "摩登花纹", "scatterbugMarine": "大海花纹", "scatterbugArchipelago": "群岛花纹", "scatterbugHighPlains": "荒野花纹", "scatterbugSandstorm": "沙尘花纹", "scatterbugRiver": "大河花纹", "scatterbugMonsoon": "骤雨花纹", "scatterbugSavanna": "热带草原花纹", "scatterbugSun": "太阳花纹", "scatterbugOcean": "大洋花纹", "scatterbugJungle": "热带雨林花纹", "scatterbugFancy": "幻彩花纹", "scatterbugPokeBall": "球球花纹", "flabebeRed": "红花", "flabebeYellow": "黄花", "flabebeOrange": "橙花", "flabebeBlue": "蓝花", "flabebeWhite": "白花", "furfrou": "野生的样子", "furfrouHeart": "心形造型", "furfrouStar": "星形造型", "furfrouDiamond": "菱形造型", "furfrouDebutante": "淑女造型", "furfrouMatron": "贵妇造型", "furfrouDandy": "绅士造型", "furfrouLaReine": "女王造型", "furfrouKabuki": "歌舞伎造型", "furfrouPharaoh": "国王造型", "espurrMale": "雄性", "espurrFemale": "雌性", "honedgeShield": "盾牌", "honedgeBlade": "刀剑", "pumpkaboo": "平均尺寸", "pumpkabooSmall": "小尺寸", "pumpkabooLarge": "大尺寸", "pumpkabooSuper": "特大尺寸", "xerneasNeutral": "放松模式", "xerneasActive": "活跃模式", "zygarde50": "５０％", "zygarde10": "１０％", "zygarde50Pc": "５０％ 群聚变形", "zygarde10Pc": "１０％ 群聚变形", "zygardeComplete": "完全体", "hoopa": "惩戒", "hoopaUnbound": "解放", "oricorioBaile": "热辣热辣风格", "oricorioPompom": "啪滋啪滋风格", "oricorioPau": "呼拉呼拉风格", "oricorioSensu": "轻盈轻盈风格", "rockruff": "普通", "rockruffOwnTempo": "特殊岩狗狗", "rockruffMidday": "白昼的样子", "rockruffMidnight": "黑夜的样子", "rockruffDusk": "黄昏的样子", "wishiwashi": "单独", "wishiwashiSchool": "鱼群的样子", "typeNullNormal": "属性：一般", "typeNullFighting": "属性：战斗", "typeNullFlying": "属性：飞翔", "typeNullPoison": "属性：毒", "typeNullGround": "属性：大地", "typeNullRock": "属性：岩石", "typeNullBug": "属性：虫子", "typeNullGhost": "属性：幽灵", "typeNullSteel": "属性：钢铁", "typeNullFire": "属性：火焰", "typeNullWater": "属性：清水", "typeNullGrass": "属性：青草", "typeNullElectric": "属性：电子", "typeNullPsychic": "属性：精神", "typeNullIce": "属性：冰雪", "typeNullDragon": "属性：龙", "typeNullDark": "属性：黑暗", "typeNullFairy": "属性：妖精", "miniorRedMeteor": "红色流星的样子", "miniorOrangeMeteor": "橙色流星的样子", "miniorYellowMeteor": "黄色流星的样子", "miniorGreenMeteor": "绿色流星的样子", "miniorBlueMeteor": "浅蓝色流星的样子", "miniorIndigoMeteor": "蓝色流星的样子", "miniorVioletMeteor": "紫色流星的样子", "miniorRed": "红色核心", "miniorOrange": "橙色核心", "miniorYellow": "黄色核心", "miniorGreen": "绿色核心", "miniorBlue": "浅蓝色核心", "miniorIndigo": "蓝色核心", "miniorViolet": "紫色核心", "mimikyuDisguised": "化形的样子", "mimikyuBusted": "现形的样子", "cosmog": "普通", "cosmogRadiantSun": "旭日状态", "cosmogFullMoon": "满月状态", "necrozma": "普通", "necrozmaDuskMane": "黄昏之鬃", "necrozmaDawnWings": "拂晓之翼", "necrozmaUltra": "究极", "magearna": "普通", "magearnaOriginal": "500年前的颜色", "marshadow": "普通", "marshadowZenith": "全力", "cramorant": "普通", "cramorantGulping": "一口吞的样子", "cramorantGorging": "大口吞的样子", "toxelAmped": "高调的样子", "toxelLowkey": "低调的样子", "sinisteaPhony": "赝品", "sinisteaAntique": "真品", "milceryVanillaCream": "奶香香草", "milceryRubyCream": "奶香红钻", "milceryMatchaCream": "奶香抹茶", "milceryMintCream": "奶香薄荷", "milceryLemonCream": "奶香柠檬", "milcerySaltedCream": "奶香海燕", "milceryRubySwirl": "红钻综合", "milceryCaramelSwirl": "教堂综合", "milceryRainbowSwirl": "三色综合", "eiscue": "冰冻头", "eiscueNoIce": "解冻头", "indeedeeMale": "雄性", "indeedeeFemale": "雌性", "morpekoFullBelly": "满腹花纹", "morpekoHangry": "空腹花纹", "zacianHeroOfManyBattles": "百战勇者", "zacianCrowned": "剑之王", "zamazentaHeroOfManyBattles": "百战勇者", "zamazentaCrowned": "盾之王", "kubfuSingleStrike": "一击流", "kubfuRapidStrike": "连击流", "zarude": "普通", "zarudeDada": "老爹", "calyrex": "普通", "calyrexIce": "骑白马的样子", "calyrexShadow": "骑黑马的样子", "basculinMale": "雄性", "basculinFemale": "雌性", "enamorusIncarnate": "化身", "enamorusTherian": "灵兽", "lechonkMale": "雄性", "lechonkFemale": "雌性", "tandemausFour": "四只家庭", "tandemausThree": "三只家庭", "squawkabillyGreenPlumage": "绿羽毛", "squawkabillyBluePlumage": "蓝羽毛", "squawkabillyYellowPlumage": "黄羽毛", "squawkabillyWhitePlumage": "白羽毛", "finizenZero": "平凡", "finizenHero": "全能", "revavroomSeginStarmobile": "阁道二天星队车", "revavroomSchedarStarmobile": "王良四天星队车", "revavroomNaviStarmobile": "策星天星队车", "revavroomRuchbahStarmobile": "阁道三天星队车", "revavroomCaphStarmobile": "王良一天星队车", "tatsugiriCurly": "上弓姿势", "tatsugiriDroopy": "下垂姿势", "tatsugiriStretchy": "平挺姿势", "dunsparceTwoSegment": "两节", "dunsparceThreeSegment": "三节", "gimmighoulChest": "宝箱形态", "gimmighoulRoaming": "徒步形态", "koraidonApexBuild": "顶尖形态", "koraidonLimitedBuild": "限制形态", "koraidonSprintingBuild": "冲刺形态", "koraidonSwimmingBuild": "游泳形态", "koraidonGlidingBuild": "滑翔形态", "miraidonUltimateMode": "极限模式", "miraidonLowPowerMode": "节能模式", "miraidonDriveMode": "驾驶模式", "miraidonAquaticMode": "水上模式", "miraidonGlideMode": "滑翔模式", "poltchageistCounterfeit": "冒牌货的样子", "poltchageistArtisan": "高档货的样子", "poltchageistUnremarkable": "凡作的样子", "poltchageistMasterpiece": "杰作的样子", "ogerponTealMask": "碧草面具", "ogerponTealMaskTera": "碧草面具太晶化", "ogerponWellspringMask": "水井面具", "ogerponWellspringMaskTera": "水井面具太晶化", "ogerponHearthflameMask": "火灶面具", "ogerponHearthflameMaskTera": "火灶面具太晶化", "ogerponCornerstoneMask": "础石面具", "ogerponCornerstoneMaskTera": "础石面具太晶化", "terapagos": "普通", "terapagosTerastal": "太晶", "terapagosStellar": "星晶", "galarDarumaka": "通常", "galarDarumakaZen": "达摩", "paldeaTaurosCombat": "斗战种", "paldeaTaurosBlaze": "火炽种", "paldeaTaurosAqua": "水澜种", "floetteEternalFlower": "永恒之花", "ursalunaBloodmoon": "赫月", "regionalForm": {"alola": "阿罗拉的样子", "galar": "伽勒尔的样子", "hisui": "洗翠的样子", "paldea": "帕底亚的样子"}, "appendForm": {"generic": "{{pokemonName}} ({{formName}})", "alola": "阿罗拉{{pokemonName}}", "galar": "伽勒尔{{pokemonName}}", "hisui": "洗翠{{pokemon<PERSON>ame}}", "paldea": "帕底亚{{pokemonName}}", "eternal": "永恒之花{{pokemonName}}", "bloodmoon": "赫月{{pokemonName}}"}, "battleForm": {"mega": "超级", "megaX": "超級X", "megaY": "超級Y", "primal": "原始回归", "gigantamax": "超极巨化", "gigantamaxSingle": "超极巨化：一击流", "gigantamaxRapid": "超极巨化：连击流", "eternamax": "无极巨化"}}