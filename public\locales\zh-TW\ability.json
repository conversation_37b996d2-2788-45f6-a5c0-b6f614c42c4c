{"stench": {"name": "惡臭", "description": "發出臭氣，在攻擊的時候，\n有時會使對手畏縮。"}, "drizzle": {"name": "降雨", "description": "出場時，會將天氣變為下雨\n。"}, "speedBoost": {"name": "加速", "description": "每一回合速度會變快。"}, "battleArmor": {"name": "戰鬥盔甲", "description": "被堅硬的甲殼守護著，不會\n被對手的攻擊擊中要害。"}, "sturdy": {"name": "結實", "description": "在ＨＰ全滿時受到招式攻擊\n不會被一擊打倒。一擊必殺\n的招式也沒有效果。"}, "damp": {"name": "濕氣", "description": "透過把周圍都弄溼，使誰都\n無法使用自爆等爆炸類的招\n式。"}, "limber": {"name": "柔軟", "description": "因為身體柔軟，不會變為麻\n痹狀態。"}, "sandVeil": {"name": "沙隱", "description": "在沙暴中閃避率會提高。"}, "static": {"name": "靜電", "description": "身上帶有靜電，有時會令接\n觸到的對手麻痹。"}, "voltAbsorb": {"name": "蓄電", "description": "受到電屬性的招式攻擊時，\n不會受到傷害，而是會回復。"}, "waterAbsorb": {"name": "儲水", "description": "受到水屬性的招式攻擊時，\n不會受到傷害，而是會回復。"}, "oblivious": {"name": "遲鈍", "description": "感覺遲鈍，不會陷入著迷和\n被挑釁狀態。面對威嚇也不\n會動搖。"}, "cloudNine": {"name": "無關天氣", "description": "任何天氣的影響都會消失。"}, "compoundEyes": {"name": "複眼", "description": "因為擁有複眼，會提高招式\n的命中率。"}, "insomnia": {"name": "不眠", "description": "因為有著睡不著的體質，所\n以不會陷入睡眠狀態。"}, "colorChange": {"name": "變色", "description": "自己的屬性會變為擊中自己\n的對手招式的屬性。"}, "immunity": {"name": "免疫", "description": "因為體內擁有免疫能力，不\n會變為中毒狀態。"}, "flashFire": {"name": "引火", "description": "受到火屬性的招式攻擊時，\n吸收火焰，自己使出的火屬\n性招式會變強。"}, "shieldDust": {"name": "鱗粉", "description": "被鱗粉守護著，不會受到招\n式的追加效果影響。"}, "ownTempo": {"name": "我行我素", "description": "因為我行我素，不會陷入混\n亂狀態。面對威嚇也不會動\n搖。"}, "suctionCups": {"name": "吸盤", "description": "用吸盤將自己牢牢吸附在地\n面上，讓替換寶可夢的招式\n和道具失效。"}, "intimidate": {"name": "威嚇", "description": "出場時威嚇對手，使其退縮\n，從而降低對手的攻擊。"}, "shadowTag": {"name": "踩影", "description": "踩住對手的影子使其無法逃\n走或替換。"}, "roughSkin": {"name": "粗糙皮膚", "description": "受到攻擊時，用粗糙的皮膚\n弄傷接觸到自己的對手。"}, "wonderGuard": {"name": "神奇守護", "description": "不可思議的力量，只有效果\n絕佳的招式才能擊中。"}, "levitate": {"name": "飄浮", "description": "從地面浮起，從而不會受到\n地面屬性招式的攻擊。"}, "effectSpore": {"name": "孢子", "description": "受到攻擊時，有時會把接觸\n到自己的對手變為中毒、麻\n痹或睡眠狀態。"}, "synchronize": {"name": "同步"}, "clearBody": {"name": "恆淨之軀", "description": "不會因對手的招式或特性而\n被降低能力。"}, "naturalCure": {"name": "自然回復", "description": "異常狀態會在離場後治癒。"}, "lightningRod": {"name": "避雷針", "description": "將電屬性的招式吸引到自己\n身上，不但不會受到傷害，\n反而會提高特攻。"}, "sereneGrace": {"name": "天恩"}, "swiftSwim": {"name": "悠遊自如", "description": "天氣為下雨時，速度會提高。"}, "chlorophyll": {"name": "葉綠素", "description": "天氣為晴朗時，速度會提高。"}, "illuminate": {"name": "發光"}, "trace": {"name": "複製", "description": "出場時，複製對手的特性，\n變為與之相同的特性。"}, "hugePower": {"name": "大力士", "description": "物理攻擊的威力會變為２倍\n。"}, "poisonPoint": {"name": "毒刺", "description": "有時會讓接觸到自己的對手\n變為中毒狀態。"}, "innerFocus": {"name": "精神力", "description": "靠著經過鍛鍊的精神，不會\n因對手的攻擊而畏縮。面對\n威嚇也不會動搖。"}, "magmaArmor": {"name": "熔岩鎧甲", "description": "將熾熱的熔岩覆蓋在身上，\n不會陷入冰凍狀態。"}, "waterVeil": {"name": "水幕", "description": "將水幕裹在身上，不會陷入\n灼傷狀態。"}, "magnetPull": {"name": "磁力", "description": "用磁力吸住鋼屬性的寶可夢\n，使其無法逃走。"}, "soundproof": {"name": "隔音", "description": "透過遮蔽聲音，不受到聲音\n招式的影響。"}, "rainDish": {"name": "雨盤", "description": "天氣為下雨時，會緩緩回復\nＨＰ。"}, "sandStream": {"name": "揚沙", "description": "出場時，會把天氣變為沙暴。"}, "pressure": {"name": "壓迫感", "description": "給予對手壓迫感，大量減少\n其使用招式的ＰＰ。"}, "thickFat": {"name": "厚脂肪", "description": "被厚厚的脂肪保護著，能夠\n讓火屬性和冰屬性招式的傷\n害減半。"}, "earlyBird": {"name": "早起", "description": "即使陷入睡眠狀態，也能以\n２倍的速度提早醒來。"}, "flameBody": {"name": "火焰之軀", "description": "有時會讓接觸到自己的對手\n變為灼傷狀態。"}, "runAway": {"name": "逃跑", "description": "一定能從野生寶可夢那裡逃\n走。"}, "keenEye": {"name": "銳利目光", "description": "靠著銳利的目光，命中率不\n會被降低。"}, "hyperCutter": {"name": "怪力鉗", "description": "因為擁有以力量自豪的鉗子，\n不會被對手降低攻擊。"}, "pickup": {"name": "撿拾", "description": "有時會撿來對手用過的道具，\n冒險過程中也會撿到。"}, "truant": {"name": "懶惰", "description": "如果使出招式，下一回合就\n需要休息。"}, "hustle": {"name": "活力", "description": "自己的攻擊雖會變高，但命\n中率會降低。"}, "cuteCharm": {"name": "迷人之軀", "description": "有時會讓接觸到自己的對手陷\n入著迷狀態。"}, "plus": {"name": "正電", "description": "場上的夥伴之中，如果有正\n電或負電特性的寶可夢，自\n己的特攻會提高。"}, "minus": {"name": "負電", "description": "場上的夥伴之中，如果有正\n電或負電特性的寶可夢，自\n己的特攻會提高。"}, "forecast": {"name": "陰晴不定", "description": "在天氣的影響下，會變成水\n屬性、火屬性或冰屬性之中\n的一種。"}, "stickyHold": {"name": "黏著", "description": "道具會黏在具有黏性的身體\n上，不會被對手奪走。"}, "shedSkin": {"name": "蛻皮", "description": "透過蛻去身上的皮，有時會\n治癒異常狀態。"}, "guts": {"name": "毅力", "description": "陷入異常狀態時，會拿出毅\n力，攻擊會提高。"}, "marvelScale": {"name": "神奇鱗片", "description": "陷入異常狀態時，神奇鱗片\n會發生反應，防禦會提高。"}, "liquidOoze": {"name": "污泥漿", "description": "吸收了污泥漿的對手會因為\n強烈的惡臭而使得ＨＰ減少。"}, "overgrow": {"name": "茂盛", "description": "ＨＰ減少的時候，草屬性的\n招式威力會提高。"}, "blaze": {"name": "猛火", "description": "ＨＰ減少的時候，火屬性的\n招式威力會提高。"}, "torrent": {"name": "激流", "description": "ＨＰ減少的時候，水屬性的\n招式威力會提高。"}, "swarm": {"name": "蟲之預感", "description": "ＨＰ減少的時候，蟲屬性的\n招式威力會提高。"}, "rockHead": {"name": "堅硬腦袋", "description": "即使使出會受反作用力傷害\n的招式，ＨＰ也不會減少。"}, "drought": {"name": "日照", "description": "出場時，會將天氣變為晴朗。"}, "arenaTrap": {"name": "沙穴"}, "vitalSpirit": {"name": "幹勁", "description": "透過激發出幹勁，不會變為\n睡眠狀態。"}, "whiteSmoke": {"name": "白色煙霧", "description": "被白色煙霧保護著，不會被\n對手降低能力。"}, "purePower": {"name": "瑜伽之力", "description": "因瑜伽的力量，物理攻擊的\n威力會變為２倍。"}, "shellArmor": {"name": "硬殼盔甲", "description": "被堅硬的殼保護著，對手的\n攻擊不會擊中要害。"}, "airLock": {"name": "氣閘", "description": "所有天氣的影響都會消失。"}, "tangledFeet": {"name": "蹣跚", "description": "陷入混亂狀態時，閃避率會\n提高。"}, "motorDrive": {"name": "電氣引擎", "description": "受到電屬性的招式攻擊時，\n不但不會受到傷害，反而速\n度會提高。"}, "rivalry": {"name": "鬥爭心", "description": "面對性別相同的對手，會燃\n起鬥爭心，變得更強。面對\n性別不同的對手時則會變弱。"}, "steadfast": {"name": "不屈之心", "description": "每次畏縮時，不屈之心就會\n燃起，速度也會提高。"}, "snowCloak": {"name": "雪隱", "description": "天氣為下雪時，閃避率會提\n高。"}, "gluttony": {"name": "貪吃鬼", "description": "原本ＨＰ變得很少時才會吃\n樹果，在ＨＰ還有一半時就\n會把它吃掉。"}, "angerPoint": {"name": "憤怒穴位", "description": "要害被擊中時會大發雷霆。\n攻擊力會提高到最大。"}, "unburden": {"name": "輕裝", "description": "失去所持有的道具時，速度\n會提高。"}, "heatproof": {"name": "耐熱", "description": "靠著耐熱的體質，讓火屬性\n的招式傷害減半。"}, "simple": {"name": "單純", "description": "能力變化會變為平時的２倍。"}, "drySkin": {"name": "乾燥皮膚", "description": "下雨天氣時和受到水屬性的\n招式時，ＨＰ會回復。晴朗\n天氣時和受到火屬性的招式\n時，ＨＰ會減少。"}, "download": {"name": "下載", "description": "比較對手的防禦和特防，根\n據較低的那項能力相應地提\n高自己的攻擊或特攻。"}, "ironFist": {"name": "鐵拳", "description": "使用到拳頭的招式威力會\n提高。"}, "poisonHeal": {"name": "毒療", "description": "陷入中毒狀態時，ＨＰ不會\n減少，反而會漸漸增加。"}, "adaptability": {"name": "適應力", "description": "與自身同屬性的招式威力會\n提高。"}, "skillLink": {"name": "連續攻擊", "description": "使用連續招式時，每回都能\n以最多次數進行攻擊。"}, "hydration": {"name": "濕潤之軀", "description": "天氣為下雨時，會治癒異常\n狀態。"}, "solarPower": {"name": "太陽之力", "description": "天氣為晴朗時特攻會提高，\n但每回合ＨＰ會減少。"}, "quickFeet": {"name": "飛毛腿", "description": "陷入異常狀態時，速度會提\n高。"}, "normalize": {"name": "一般皮膚", "description": "無論是什麼屬性的招式，全\n部都會變為一般屬性。威力\n會少量提高。"}, "sniper": {"name": "狙擊手", "description": "擊中要害時，威力會進一步\n提高。"}, "magicGuard": {"name": "魔法防守", "description": "不會受到攻擊以外的傷害。"}, "noGuard": {"name": "無防守"}, "stall": {"name": "慢出", "description": "使出招式的順序必定會變為\n最後。"}, "technician": {"name": "技術高手", "description": "可讓威力低的招式提高威力\n來進行攻擊。"}, "leafGuard": {"name": "葉子防守", "description": "天氣為晴朗時，不會陷入異\n常狀態。"}, "klutz": {"name": "笨拙", "description": "無法使用持有的道具。"}, "moldBreaker": {"name": "破格", "description": "可不受特性影響，向對手使\n出招式。"}, "superLuck": {"name": "超幸運", "description": "因為非常幸運，容易擊中對\n手的要害。"}, "aftermath": {"name": "引爆", "description": "瀕死時，會對接觸到自己的\n對手造成傷害。"}, "anticipation": {"name": "危險預知", "description": "察覺對手持有的危險招式。"}, "forewarn": {"name": "預知夢", "description": "出場時，預見１個對手持有\n的招式。"}, "unaware": {"name": "純樸"}, "tintedLens": {"name": "有色眼鏡", "description": "可將效果不好的招式以正常\n的威力使出。"}, "filter": {"name": "過濾", "description": "受到效果絕佳的攻擊時，可\n減弱其威力。"}, "slowStart": {"name": "慢啓動", "description": "在５回合內，攻擊和速度會\n減半。"}, "scrappy": {"name": "膽量", "description": "一般屬性和格鬥屬性的招式\n可擊中幽靈屬性的寶可夢。\n面對威嚇也不會動搖。"}, "stormDrain": {"name": "引水", "description": "將水屬性的招式引到自己身\n上，不但不會受到傷害，反\n而會提高特攻。"}, "iceBody": {"name": "冰凍之軀", "description": "天氣為下雪時，會漸漸回復\nＨＰ。"}, "solidRock": {"name": "堅硬岩石", "description": "受到效果絕佳的攻擊時，可\n減弱其威力。"}, "snowWarning": {"name": "降雪", "description": "出場時，會將天氣變為下雪。"}, "honeyGather": {"name": "採蜜"}, "frisk": {"name": "察覺", "description": "出場時，可以察覺對手的特\n性。"}, "reckless": {"name": "捨身", "description": "會讓自己因反作用力而受傷\n的招式威力會提高。"}, "multitype": {"name": "多屬性"}, "flowerGift": {"name": "花之禮", "description": "天氣為晴朗時，自己和同伴\n的攻擊和特防能力會提高。"}, "badDreams": {"name": "夢魘", "description": "給予陷入睡眠狀態的對手傷\n害。"}, "pickpocket": {"name": "順手牽羊", "description": "盜取接觸到自己的對手的道\n具。"}, "sheerForce": {"name": "強行", "description": "招式會失去追加效果，但可\n以用更高的威力使出招式。"}, "contrary": {"name": "唱反調", "description": "能力的變化會逆轉，原本提\n高時會降低，原本降低時會\n提高。"}, "unnerve": {"name": "緊張感", "description": "讓對手感到緊張，無法吃樹\n果。"}, "defiant": {"name": "不服輸", "description": "被對手降低能力時，攻擊會\n大幅提高。"}, "defeatist": {"name": "軟弱", "description": "ＨＰ降到一半以下時，會變\n得軟弱而使得攻擊和特攻減\n半。"}, "cursedBody": {"name": "詛咒之軀", "description": "受到攻擊時，有時會把對手\n的招式變為定身法狀態。"}, "healer": {"name": "治癒之心", "description": "有時會治癒同伴的異常狀態。"}, "friendGuard": {"name": "友情防守", "description": "可以減少我方受到的傷害。"}, "weakArmor": {"name": "碎裂鎧甲", "description": "因物理招式受到傷害時，防\n禦會降低，速度會大幅提高。"}, "heavyMetal": {"name": "重金屬", "description": "自己的重量會變為２倍。"}, "lightMetal": {"name": "輕金屬", "description": "自己的重量會減半。"}, "multiscale": {"name": "多重鱗片", "description": "ＨＰ全滿時，受到的傷害會\n變少。"}, "toxicBoost": {"name": "中毒激升", "description": "陷入中毒狀態時，物理招式\n的威力會提高。"}, "flareBoost": {"name": "受熱激升", "description": "陷入灼傷狀態時，特殊招式\n的威力會提高。"}, "harvest": {"name": "收穫", "description": "可多次採收已被使用過的樹果。"}, "telepathy": {"name": "心靈感應", "description": "讀取我方的攻擊，並閃避其\n招式傷害。"}, "moody": {"name": "心情不定", "description": "每一回合，能力中的某項會\n大幅提高，而某項會降低。"}, "overcoat": {"name": "防塵", "description": "不會受到沙暴的傷害。也不\n會受到粉末類和孢子類招式\n的影響。"}, "poisonTouch": {"name": "毒手", "description": "有時僅是接觸就能讓對手中\n毒。"}, "regenerator": {"name": "再生力", "description": "退回同行隊伍後，ＨＰ會少\n量回復。"}, "bigPecks": {"name": "健壯胸肌", "description": "不會受到降低防禦的效果影\n響。"}, "sandRush": {"name": "撥沙", "description": "天氣為沙暴時，速度會提高。"}, "wonderSkin": {"name": "奇蹟皮膚", "description": "不易受到變化類招式攻擊的\n身體。"}, "analytic": {"name": "分析", "description": "如果在最後使出招式，招式\n的威力就會變強。"}, "illusion": {"name": "幻覺", "description": "假扮成同行隊伍中的最後一\n隻寶可夢出場，迷惑對手。"}, "imposter": {"name": "變身者", "description": "變身為當前面對的寶可夢。"}, "infiltrator": {"name": "穿透", "description": "可穿透對手的屏障或替身進\n行攻擊。"}, "mummy": {"name": "木乃伊", "description": "被對手接觸到後，會將對手\n變為木乃伊。"}, "moxie": {"name": "自信過度", "description": "如果打倒對手，會充滿自信\n並提高攻擊。"}, "justified": {"name": "正義之心", "description": "受到惡屬性的招式攻擊時，\n因為正義感，攻擊會提高。"}, "rattled": {"name": "膽怯", "description": "受到惡屬性、幽靈屬性和蟲\n屬性的招式攻擊，或受到威\n嚇時，會因膽怯而使得速度\n提高。"}, "magicBounce": {"name": "魔法鏡", "description": "可不受到由對手使出的變化\n類招式所影響，並將其反彈。"}, "sapSipper": {"name": "食草", "description": "受到草屬性的招式攻擊時，\n不但不會受到傷害，反而攻\n擊會提高。"}, "prankster": {"name": "惡作劇之心", "description": "可以搶先使出變化類招式。"}, "sandForce": {"name": "沙之力", "description": "天氣為沙暴時，岩石屬性、\n地面屬性和鋼屬性招式的威\n力會提高。"}, "ironBarbs": {"name": "鐵刺", "description": "用鐵刺給予接觸到自己的對\n手傷害。"}, "zenMode": {"name": "達摩模式", "description": "ＨＰ變為一半以下時，樣子\n會改變。"}, "victoryStar": {"name": "勝利之星", "description": "自己和同伴的命中率會提高。"}, "turboblaze": {"name": "渦輪火焰", "description": "可以不受對手特性的干擾，\n向對手使出招式。"}, "teravolt": {"name": "兆級電壓", "description": "可以不受對手特性的干擾，\n向對手使出招式。"}, "aromaVeil": {"name": "芳香幕", "description": "可防住向自己和同伴發出的\n心靈攻擊。"}, "flowerVeil": {"name": "花幕", "description": "我方的草屬性寶可夢能力不\n會降低。也不會陷入異常狀\n態。"}, "cheekPouch": {"name": "頰囊", "description": "無論是哪種樹果，吃下去後\nＨＰ都會回復。"}, "protean": {"name": "變幻自如", "description": "每次出場戰鬥時，變為與自\n己使出的招式相同的屬性１\n次。"}, "furCoat": {"name": "毛皮大衣", "description": "對手的物理招式造成的傷害\n會減半。"}, "magician": {"name": "魔術師", "description": "奪走被自己的招式擊中的對\n手的道具。"}, "bulletproof": {"name": "防彈", "description": "可防住對手的球和彈類的招\n式。"}, "competitive": {"name": "好勝", "description": "被對手降低能力時，特攻會\n大幅提高。"}, "strongJaw": {"name": "強壯之顎", "description": "顎部強壯，會提高啃咬類招\n式的威力。"}, "refrigerate": {"name": "冰凍皮膚", "description": "一般屬性的招式會變為冰屬\n性。威力會少量提高。"}, "sweetVeil": {"name": "甜幕", "description": "自己和我方的寶可夢不會陷\n入睡眠狀態。"}, "stanceChange": {"name": "戰鬥切換", "description": "若使出攻擊招式，會變為刀\n劍形態，若使出招式「王者\n盾牌」，會變為盾牌形態。"}, "galeWings": {"name": "疾風之翼", "description": "ＨＰ全滿時，可以搶先在對\n手之前使出飛行屬性的招式。"}, "megaLauncher": {"name": "超級發射器", "description": "波動和波導類招式的威力會\n提高。"}, "grassPelt": {"name": "草之毛皮", "description": "在青草場地時，防禦會提高。"}, "symbiosis": {"name": "共生", "description": "同伴使用道具時，會把自己\n持有的道具傳遞給同伴。"}, "toughClaws": {"name": "硬爪", "description": "接觸到對手的招式威力會提\n高。"}, "pixilate": {"name": "妖精皮膚", "description": "一般屬性的招式會變為妖精\n屬性。威力會少量提高。"}, "gooey": {"name": "黏滑", "description": "對手用攻擊接觸到自己時，\n降低此對手的速度。"}, "aerilate": {"name": "飛行皮膚", "description": "一般屬性的招式會變為飛行\n屬性。威力會少量提高。"}, "parentalBond": {"name": "親子愛", "description": "親子倆可合計攻擊２次。"}, "darkAura": {"name": "暗黑氣場", "description": "全體的惡屬性招式變強。"}, "fairyAura": {"name": "妖精氣場", "description": "全體的妖精屬性招式變強。"}, "auraBreak": {"name": "氣場破壞", "description": "讓氣場的效果逆轉，並降低\n威力。"}, "primordialSea": {"name": "始源之海", "description": "變為讓火屬性攻擊失效的天\n氣。"}, "desolateLand": {"name": "終結之地", "description": "變為讓水屬性攻擊失效的天\n氣。"}, "deltaStream": {"name": "德爾塔氣流", "description": "變為令飛行屬性的弱點消失\n的天氣。"}, "stamina": {"name": "持久力", "description": "受到攻擊時，防禦會提高。"}, "wimpOut": {"name": "躍躍欲逃", "description": "ＨＰ變為一半時，會慌慌張\n張逃走，退回同行隊伍中。"}, "emergencyExit": {"name": "危險迴避", "description": "ＨＰ減到一半時，為了避開\n危險，會退回到同行隊伍中。"}, "waterCompaction": {"name": "遇水凝固", "description": "受到水屬性的招式攻擊時，\n防禦會大幅提高。"}, "merciless": {"name": "不仁不義", "description": "攻擊中毒狀態的對手時，\n必定會擊中要害。"}, "shieldsDown": {"name": "界限盾殼", "description": "ＨＰ變為一半時，殼會壞掉，\n變得更有攻擊性。"}, "stakeout": {"name": "蹲守", "description": "可以向替換出場的對手以２\n倍的傷害進行攻擊。"}, "waterBubble": {"name": "水泡", "description": "降低自己受到的火屬性招式\n的威力，不會灼傷。"}, "steelworker": {"name": "鋼能力者", "description": "鋼屬性的招式威力會提高。"}, "berserk": {"name": "怒火沖天", "description": "ＨＰ因對手的攻擊降到一半\n時，特攻會提高。"}, "slushRush": {"name": "撥雪", "description": "天氣為下雪時，速度會提高。"}, "longReach": {"name": "遠隔", "description": "可以不接觸對手就使出所有\n的招式。"}, "liquidVoice": {"name": "溼潤之聲", "description": "所有的聲音招式都變為水屬\n性。"}, "triage": {"name": "先行治療", "description": "可以搶先使出回復招式。"}, "galvanize": {"name": "電氣皮膚", "description": "一般屬性的招式會變為電屬\n性。威力會少量提高。"}, "surgeSurfer": {"name": "衝浪之尾", "description": "電氣場地時，速度會變為２\n倍。"}, "schooling": {"name": "魚羣", "description": "ＨＰ多的時候會聚起來變強。\nＨＰ剩餘量變少時，群體\n會分崩離析。"}, "disguise": {"name": "畫皮", "description": "用畫皮覆蓋住身體，可防住\n１次攻擊。"}, "battleBond": {"name": "牽絆變身", "description": "打倒對手時，與訓練家的牽\n絆會加深，自己的攻擊、特\n攻和速度會提高。"}, "powerConstruct": {"name": "群聚變形", "description": "ＨＰ變為一半時，細胞們會\n趕來支援，變為完全體形態。"}, "corrosion": {"name": "腐蝕", "description": "就算對方是鋼屬性或毒屬性\n寶可夢，也可讓對方陷入中\n毒狀態。"}, "comatose": {"name": "絕對睡眠"}, "queenlyMajesty": {"name": "女王的威嚴", "description": "向對手施加威懾力，使其無\n法對我方使出先制招式。"}, "innardsOut": {"name": "飛出的內在物", "description": "被對手打倒的時候，會給予\n對手相當於ＨＰ剩餘量的傷\n害。"}, "dancer": {"name": "舞者", "description": "當有誰使出跳舞招式時，自\n己也能接著使出跳舞招式。"}, "battery": {"name": "蓄電池", "description": "會提高我方的特殊招式的威\n力。"}, "fluffy": {"name": "毛茸茸", "description": "會將對手所給予的接觸類招\n式的傷害減半，但火屬性招\n式的傷害會變為２倍。"}, "dazzling": {"name": "鮮豔之軀", "description": "讓對手嚇一跳，使其無法對\n我方使出先制招式。"}, "soulHeart": {"name": "魂心", "description": "每當場上有寶可夢陷入瀕死\n狀態時，特攻就會提高。"}, "tanglingHair": {"name": "捲髮", "description": "對手用攻擊接觸到自己時，\n降低此對手的速度。"}, "receiver": {"name": "接球手", "description": "繼承被打倒的同伴的特性，\n變為相同的特性。"}, "powerOfAlchemy": {"name": "化學之力", "description": "繼承被打倒的同伴的特性，\n變為相同的特性。"}, "beastBoost": {"name": "異獸提升", "description": "打倒對手的時候，會提高自\n己最高的那項能力。"}, "rksSystem": {"name": "ＡＲ系統", "description": "根據持有的記憶碟，自己的\n屬性會改變。"}, "electricSurge": {"name": "電氣製造者", "description": "出場時，會布下電氣場地。"}, "psychicSurge": {"name": "精神製造者", "description": "出場時，會布下精神場地。"}, "mistySurge": {"name": "薄霧製造者", "description": "出場時，會布下薄霧場地。"}, "grassySurge": {"name": "青草製造者", "description": "出場時，會布下青草場地。"}, "fullMetalBody": {"name": "金屬防護", "description": "不會因對手的招式或特性而\n被降低能力。"}, "shadowShield": {"name": "幻影防守", "description": "ＨＰ全滿時，受到的傷害會\n變少。"}, "prismArmor": {"name": "棱鏡裝甲", "description": "受到效果絕佳的攻擊時，可\n減弱其威力。"}, "neuroforce": {"name": "腦核之力", "description": "可進一步提升效果絕佳招式\n的威力。"}, "intrepidSword": {"name": "不撓之劍", "description": "在戰鬥中首次出場時，攻擊\n會提高。"}, "dauntlessShield": {"name": "不屈之盾", "description": "在戰鬥中首次出場時，防禦\n會提高。"}, "libero": {"name": "自由者", "description": "每次出場戰鬥時，變為與自\n己使出的招式相同的屬性１\n次。"}, "ballFetch": {"name": "撿球", "description": "當寶可夢沒有攜帶道具時，\n會撿回第１個投出後捕捉失\n敗的精靈球。"}, "cottonDown": {"name": "棉絮", "description": "受到攻擊時會撒下棉絮，降\n低除自己以外的所有寶可夢\n的速度。"}, "propellerTail": {"name": "螺旋尾鰭", "description": "能無視具有吸引對手招式效\n果的特性或招式的影響。"}, "mirrorArmor": {"name": "鏡甲", "description": "只反彈自己受到的能力降低\n效果。"}, "gulpMissile": {"name": "一口導彈", "description": "衝浪或潛水時會叼來獵物。\n當受到傷害時，會吐出獵物\n攻擊對手。"}, "stalwart": {"name": "堅毅", "description": "能無視具有吸引對手招式效\n果的特性或招式的影響。"}, "steamEngine": {"name": "蒸汽機", "description": "受到水屬性或火屬性招式攻\n擊時，速度會極大幅提高。"}, "punkRock": {"name": "龐克搖滾", "description": "聲音招式的威力會提高。受\n到聲音招式的傷害會減半。"}, "sandSpit": {"name": "吐沙", "description": "受到攻擊時，會刮起沙暴。"}, "iceScales": {"name": "冰鱗粉", "description": "得到冰鱗粉的守護，受到的\n特殊攻擊傷害會減半。"}, "ripen": {"name": "熟成", "description": "讓樹果成熟，使效果變為２\n倍。"}, "iceFace": {"name": "結凍頭", "description": "頭部的冰會代替自己承受物\n理攻擊，但是樣子會改變。\n下雪時，冰會恢復原狀。"}, "powerSpot": {"name": "能量點", "description": "只要站在旁邊，招式的威力\n就會提高。"}, "mimicry": {"name": "擬態", "description": "寶可夢的屬性會根據場地的\n狀態而改變。"}, "screenCleaner": {"name": "除障", "description": "出場時，敵方和我方的光牆\n、反射壁和極光幕的效果會\n消失。"}, "steelySpirit": {"name": "鋼之意志", "description": "我方的鋼屬性攻擊威力會提\n高。"}, "perishBody": {"name": "滅亡之軀", "description": "在受到接觸類招式攻擊時，\n３個回合後雙方都會陷入瀕\n死。替換寶可夢後效果就\n會消失。"}, "wanderingSpirit": {"name": "遊魂", "description": "與使用接觸類招式攻擊自己\n的寶可夢互換特性。"}, "gorillaTactics": {"name": "一猩一意", "description": "攻擊雖然會提高，但只能使\n出最初選擇的招式。"}, "neutralizingGas": {"name": "化學變化氣體"}, "pastelVeil": {"name": "粉彩護幕", "description": "自己和我方同伴都不會陷入\n中毒的異常狀態。"}, "hungerSwitch": {"name": "飽了又餓", "description": "在每個回合結束時，會在滿\n腹花紋和空腹花紋之間交替\n改變樣子。"}, "quickDraw": {"name": "速擊", "description": "有時能比對手先一步行動。"}, "unseenFist": {"name": "無形拳", "description": "只要是接觸到對手的招式，\n就可以無視對手的防守效果\n進行攻擊。"}, "curiousMedicine": {"name": "怪藥", "description": "出場時，會從貝殼撒藥，將\n我方的能力變化復原。"}, "transistor": {"name": "電晶體", "description": "電屬性的招式威力會提高。"}, "dragonsMaw": {"name": "龍顎", "description": "龍屬性的招式威力會提高。"}, "chillingNeigh": {"name": "蒼白嘶鳴", "description": "打倒對手時會用冰冷的聲音\n嘶鳴並提高攻擊。"}, "grimNeigh": {"name": "漆黑嘶鳴", "description": "打倒對手時會用恐怖的聲音\n嘶鳴並提高特攻。"}, "asOneGlastrier": {"name": "人馬一體", "description": "兼備蕾冠王的緊張感和雪暴\n馬的蒼白嘶鳴這２種特性。"}, "asOneSpectrier": {"name": "人馬一體", "description": "兼備蕾冠王的緊張感和靈幽\n馬的漆黑嘶鳴這２種特性。"}, "lingeringAroma": {"name": "甩不掉的氣味", "description": "被對手接觸到時，甩不掉的\n氣味會沾染給對手。"}, "seedSower": {"name": "掉出種子", "description": "受到攻擊時，會將腳下變成\n青草場地。"}, "thermalExchange": {"name": "熱交換", "description": "受到火屬性的招式攻擊時，\n攻擊會提高，不會陷入灼傷\n狀態。"}, "angerShell": {"name": "憤怒甲殼", "description": "ＨＰ因對手的攻擊降到一半\n時，會因憤怒而降低防禦和\n特防，但攻擊、特攻和速度\n會提高。"}, "purifyingSalt": {"name": "潔淨之鹽", "description": "因潔淨的鹽而不會陷入異常\n狀態。能夠讓幽靈屬性招式\n的傷害減半。"}, "wellBakedBody": {"name": "焦香之軀", "description": "受到火屬性的招式攻擊時，\n不但不會受到傷害，反而防\n禦會大幅提高"}, "windRider": {"name": "乘風", "description": "吹起順風或受到風的招式攻\n擊時，不但不會受到傷害，\n反而攻擊會提高。"}, "guardDog": {"name": "看門犬", "description": "受到威嚇時，攻擊會提高。\n會讓替換寶可夢的招式和道\n具失效。"}, "rockyPayload": {"name": "搬巖", "description": "岩石屬性的招式威力會提高。"}, "windPower": {"name": "風力發電", "description": "受到風的招式攻擊時，會變\n成充電狀態。"}, "zeroToHero": {"name": "全能變身", "description": "離場後會變為全能形態。"}, "commander": {"name": "發號施令"}, "electromorphosis": {"name": "電力轉換", "description": "受到傷害時，會變成充電狀\n態。"}, "protosynthesis": {"name": "古代活性", "description": "攜帶著驅勁能量或天氣為晴\n朗時，數值最高的能力會提\n高。"}, "quarkDrive": {"name": "夸克充能", "description": "攜帶著驅勁能量或在電氣場\n地上時，數值最高的能力會\n提高。"}, "goodAsGold": {"name": "黃金之軀", "description": "既不氧化又堅韌的黃金之軀\n不會受到對手的變化類招式\n攻擊。"}, "vesselOfRuin": {"name": "災禍之鼎", "description": "在喚來災厄之鼎的力量下，\n除自己以外的特攻會變弱。"}, "swordOfRuin": {"name": "災禍之劍", "description": "在喚來災厄之劍的力量下，\n除自己以外的防禦會變弱。"}, "tabletsOfRuin": {"name": "災禍之簡", "description": "在喚來災厄之木簡的力量下\n，除自己以外的攻擊會變弱。"}, "beadsOfRuin": {"name": "災禍之玉", "description": "在喚來災厄之木簡的力量下\n，除自己以外的特防會變弱。"}, "orichalcumPulse": {"name": "緋紅脈動", "description": "出場時，會將天氣變為晴朗\n。日照很強時，會因為古代\n的脈動而使攻擊升高。"}, "hadronEngine": {"name": "強子引擎", "description": "出場時，會布下電氣場地。\n在電氣場地時，會因為未來\n的機關而使特攻升高。"}, "opportunist": {"name": "跟風", "description": "對手的能力提高時，自己也\n會跟著提高能力。"}, "cudChew": {"name": "反芻", "description": "食用樹果後，會在下一回合\n結束時從胃裡取出，以１次\n為限再次食用。"}, "sharpness": {"name": "鋒銳", "description": "切斬對手的招式威力會提高。"}, "supremeOverlord": {"name": "大將", "description": "出場時，先前每有１隻同伴\n被打倒，攻擊和特攻就會提\n高少許。"}, "costar": {"name": "同台共演", "description": "出場時，會複製同伴的能力\n變化。"}, "toxicDebris": {"name": "毒滿地", "description": "因物理招式受到傷害時，會\n在對手腳下散布毒菱。"}, "armorTail": {"name": "尾甲", "description": "包覆著頭部的神秘尾巴使對\n手無法對我方使出先制招式。"}, "earthEater": {"name": "食土", "description": "受到地面屬性的招式攻擊時\n，不會受到傷害，而是會回\n復。"}, "myceliumMight": {"name": "菌絲之力", "description": "使出變化類招式時，行動一\n定會變緩慢，但不會受到對\n手特性的干擾。"}, "mindsEye": {"name": "心眼", "description": "一般屬性和格鬥屬性的招式\n可以擊中幽靈屬性的寶可夢。\n無視對手的閃避率的變化，\n且命中率不會被降低。"}, "supersweetSyrup": {"name": "甘露之蜜"}, "hospitality": {"name": "款待", "description": "出場時款待同伴，使其回復\n少量ＨＰ。"}, "toxicChain": {"name": "毒鎖鏈", "description": "靠著含有毒素的鎖鏈的力量\n，有時會讓被招式擊中的對\n手陷入劇毒狀態。"}, "embodyAspectTeal": {"name": "面影輝映", "description": "將回憶映於心中，使碧草面\n具發出光輝，提高自己的速\n度。"}, "embodyAspectWellspring": {"name": "面影輝映", "description": "將回憶映於心中，使水井面\n具發出光輝，提高自己的特\n防。"}, "embodyAspectHearthflame": {"name": "面影輝映", "description": "將回憶映於心中，使火灶面\n具發出光輝，提高自己的攻\n擊。"}, "embodyAspectCornerstone": {"name": "面影輝映", "description": "將回憶映於心中，使礎石面\n具發出光輝，提高自己的防\n御。"}, "teraShift": {"name": "太晶變形", "description": "出場時，會吸收周圍的能量\n，變為太晶形態。"}, "teraShell": {"name": "太晶甲殼", "description": "蘊藏著所有屬性力量的甲殼\n會將自身ＨＰ全滿時受到的\n傷害全都變為效果不好。"}, "teraformZero": {"name": "歸零化境", "description": "太樂巴戈斯變為星晶形態時\n，蘊藏其身的力量會將天氣\n和場地的影響全部歸零。"}, "poisonPuppeteer": {"name": "毒傀儡", "description": "因為此寶可夢的招式而陷入中毒狀態的對手\n同時也會陷入混亂狀態。"}}