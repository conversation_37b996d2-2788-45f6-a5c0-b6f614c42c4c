{"hitWithRecoil": "{{pokemon<PERSON>ame}}\n受到了反作用力造成的伤害！", "cutHpPowerUpMove": "{{pokemon<PERSON>ame}}\n削减了体力并提升了招式威力！", "absorbedElectricity": "{{pokemon<PERSON>ame}}\n吸收了电力！", "switchedStatChanges": "{{pokemon<PERSON>ame}}和对手互换了\n自己的能力变化！", "switchedTwoStatChanges": "{{pokemonName}} 和对手互换了自己的{{firstStat}}和{{secondStat}}的能力变化！", "switchedStat": "{{pokemonName}} 互换了各自的{{stat}}！", "sharedGuard": "{{pokemon<PERSON>ame}} 平分了各自的防守！", "sharedPower": "{{pokemon<PERSON>ame}} 平分了各自的力量！", "shiftedStats": "{{pokemonName}}互换了\n{{statToSwitch}}和{{statToSwitchWith}}！", "goingAllOutForAttack": "{{pokemon<PERSON>ame}}拿出全力了！", "regainedHealth": "{{pokemon<PERSON>ame}}的\n体力回复了！", "keptGoingAndCrashed": "{{pokemon<PERSON>ame}}因势头过猛\n而撞到了地面！", "fled": "{{pokemon<PERSON>ame}}\n逃走了！", "cannotBeSwitchedOut": "{{pokemon<PERSON>ame}}\n无法被收回！", "swappedAbilitiesWithTarget": "{{pokemon<PERSON>ame}}\n互换了各自的特性！", "coinsScatteredEverywhere": "金币散落一地！", "attackedByItem": "{{pokemonName}}被\n{{itemName}}袭击了！", "whippedUpAWhirlwind": "{{pokemon<PERSON>ame}}周围的\n空气产生了旋涡！", "flewUpHigh": "{{pokemon<PERSON>ame}}\n飞向了高空！", "tookInSunlight": "{{pokemon<PERSON>ame}}\n吸收了光！", "dugAHole": "{{pokemon<PERSON>ame}}\n钻入了地里！", "loweredItsHead": "{{pokemon<PERSON>ame}}\n把头缩了进去！", "isGlowing": "强光包围了{{pokemonName}}\n！", "bellChimed": "铃声响彻四周！", "foresawAnAttack": "{{pokemon<PERSON>ame}}\n预知了未来的攻击！", "isTighteningFocus": "{{pokemon<PERSON>ame}}正在集中注意力！", "lostFocus": "{{pokemonName}}聚气时受到干扰，无法使出招式！", "hidUnderwater": "{{pokemon<PERSON>ame}}\n潜入了水中！", "soothingAromaWaftedThroughArea": "怡人的香气扩散了开来！", "sprangUp": "{{pokemon<PERSON>ame}}\n高高地跳了起来！", "choseDoomDesireAsDestiny": "{{pokemon<PERSON>ame}}\n将破灭之愿托付给了未来！", "vanishedInstantly": "{{pokemon<PERSON>ame}}的身影\n瞬间消失了！", "tookTargetIntoSky": "{{pokemonName}}将{{targetName}}\n带上了高空！", "becameCloakedInFreezingLight": "{{pokemon<PERSON>ame}}\n被冷光包围了！", "becameCloakedInFreezingAir": "{{pokemon<PERSON>ame}}\n被冰冻的空气包围了！", "isChargingPower": "{{pokemon<PERSON>ame}}\n正在积蓄力量！", "burnedItselfOut": "{{pokemon<PERSON>ame}}的火焰燃尽了！", "startedHeatingUpBeak": "{{pokemon<PERSON>ame}}\n开始给鸟嘴加热了！", "setUpShellTrap": "{{pokemon<PERSON>ame}}\n设置了陷阱甲壳！", "isOverflowingWithSpacePower": "{{pokemon<PERSON>ame}}身上\n溢出了宇宙之力！", "usedUpAllElectricity": "{{pokemon<PERSON>ame}}\n用尽电力了！", "stoleItem": "{{pokemonName}}从{{targetName}}那里\n夺取了{{itemName}}！", "incineratedItem": "{{pokemonName}}烧没了\n{{targetName}}的{{itemName}}！", "knockedOffItem": "{{pokemonName}}拍落了\n{{targetName}}的{{itemName}}！", "tookMoveAttack": "{{pokemonName}}\n受到了{{moveName}}的攻击！", "cutOwnHpAndMaximizedStat": "{{pokemonName}}\n削减了体力并释放了全部{{statName}}！", "copiedStatChanges": "{{pokemonName}}复制了\n{{targetName}}的能力变化！", "magnitudeMessage": "震级{{magnitude}}！", "tookAimAtTarget": "{{pokemonName}}将目标对准了\n{{targetName}}！", "transformedIntoType": "{{pokemonName}} \n变成了{{typeName}}属性！", "copiedMove": "{{pokemonName}}\n复制了{{moveName}}！", "sketchedMove": "{{pokemonName}}\n对{{moveName}}进行了写生！", "acquiredAbility": "{{pokemonName}}的特性\n变为{{abilityName}}了！", "copiedTargetAbility": "{{pokemonName}}复制了\n{{targetName}}的{{abilityName}}！", "transformedIntoTarget": "{{pokemonName}}\n变身成了{{targetName}}！", "tryingToTakeFoeDown": "{{pokemon<PERSON>ame}}\n想和对手同归于尽！", "addType": "{{pokemonName}}\n增加了{{typeName}}属性！", "cannotUseMove": "{{pokemonName}}\n无法使用{{moveName}}！", "healHp": "{{pokemon<PERSON>ame}}的\n体力回复了！", "sacrificialFullRestore": "{{pokemon<PERSON>ame}}的\n治愈之愿实现了！", "invertStats": "{{pokemon<PERSON>ame}}的\n能力变化颠倒过来了！", "resetStats": "{{pokemon<PERSON>ame}}的\n能力变化复原了！", "statEliminated": "所有能力都复原了！", "faintCountdown": "{{pokemonName}}\n将在{{turnCount}}回合后灭亡！", "copyType": "{{pokemonName}}\n变成了{{targetPokemonName}}的属性！", "suppressAbilities": "{{pokemon<PERSON>ame}}的特性\n变得无效了！", "revivalBlessing": "{{pokemon<PERSON>ame}}复活了！", "swapArenaTags": "{{pokemon<PERSON>ame}}\n交换了双方的场地效果！", "chillyReception": "{{pokemon<PERSON>ame}}\n说出了冷笑话！", "exposedMove": "{{pokemonName}}识破了\n{{targetPokemonName}}的原型！", "safeguard": "{{targetName}}\n正受到神秘之幕的保护！", "restBecameHealthy": "{{pokemonName}}睡着了，\n并且变得精力充沛！", "substituteOnOverlap": "但是，{{pokemonName}}的替身已经出现了！", "substituteNotEnoughHp": "但是，体力已经不够\n放出替身了！", "afterYou": "{{targetName}}\n接受了对手的好意！", "combiningPledge": "两个招式合二为一！\n这是合体招式！！", "awaitingPledge": "{{userPokemonName}}正在等待\n{{allyPokemonName}}……", "corrosiveGasItem": "{{pokemonName}}把{{targetName}}的{{itemName}}融化了！", "instructingMove": "{{targetPokemonName}}听从了\n{{userPokemonName}}的指示！", "lunarDanceRestore": "{{pokemon<PERSON>ame}}\n被神秘的月光包围了！", "stealPositiveStats": "{{pokemonName}}夺取了{{targetName}}提高的那部分能力！", "naturePowerUse": "{{pokemonName}}的自然之力\n变成了{{moveName}}！", "forceLast": "延后了{{targetPokemonName}}的顺序！", "splash": "但是什么都没有发生！", "fallDown": "{{targetPokemonName}}\n被击落，掉到了地面！", "celebrate": "恭喜，{{playerName}}！", "struggle": "{{pokemon<PERSON>ame}}\n没有可用来施展的招式！"}