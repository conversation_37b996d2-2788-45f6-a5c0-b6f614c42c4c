{"pound": {"name": "拍击", "effect": "使用长长的尾巴或手等拍打对手进行攻击"}, "karateChop": {"name": "空手劈", "effect": "用锋利的手刀劈向对手进行攻击。\n容易击中要害"}, "doubleSlap": {"name": "连环巴掌", "effect": "用连环巴掌拍打对手进行攻击。\n连续攻击２～５次"}, "cometPunch": {"name": "连续拳", "effect": "用拳头怒涛般的殴打对手进行攻击。\n连续攻击２～５次"}, "megaPunch": {"name": "百万吨重拳", "effect": "用充满力量的拳头攻击对手"}, "payDay": {"name": "聚宝功", "effect": "向对手的身体投掷小金币进行攻击。\n战斗后可以拿到钱"}, "firePunch": {"name": "火焰拳", "effect": "用充满火焰的拳头攻击对手。\n有时会让对手陷入灼伤状态"}, "icePunch": {"name": "冰冻拳", "effect": "用充满寒气的拳头攻击对手。\n有时会让对手陷入冰冻状态"}, "thunderPunch": {"name": "雷电拳", "effect": "用充满电流的拳头攻击对手。\n有时会让对手陷入麻痹状态"}, "scratch": {"name": "抓", "effect": "用坚硬且无比锋利的爪子抓对手进行攻击"}, "viseGrip": {"name": "夹住", "effect": "将对手从两侧夹住，给予伤害"}, "guillotine": {"name": "极落钳", "effect": "用大钳子或剪刀等夹断对手进行攻击。\n只要命中就会一击昏厥"}, "razorWind": {"name": "旋风刀", "effect": "制造风之刃，于第２回合攻击对手。\n容易击中要害"}, "swordsDance": {"name": "剑舞", "effect": "激烈地跳起战舞提高气势。\n大幅提高自己的攻击"}, "cut": {"name": "居合劈", "effect": "用镰刀或爪子等切斩对手进行攻击"}, "gust": {"name": "起风", "effect": "用翅膀将刮起的狂风袭向对手进行攻击"}, "wingAttack": {"name": "翅膀攻击", "effect": "大大地展开美丽的翅膀，\n将其撞向对手进行攻击"}, "whirlwind": {"name": "吹飞", "effect": "吹飞对手，强制拉后备宝可梦上场。\n如果对手为野生宝可梦，\n战斗将直接结束"}, "fly": {"name": "飞翔", "effect": "第１回合飞上天空，第２回合攻击对手"}, "bind": {"name": "绑紧", "effect": "使用长长的身体或藤蔓等，\n在４～５回合内绑紧对手进行攻击"}, "slam": {"name": "摔打", "effect": "使用长长的尾巴或藤蔓等摔打对手\n进行攻击"}, "vineWhip": {"name": "藤鞭", "effect": "用如同鞭子般弯曲而细长的藤蔓摔\n打对手进行攻击"}, "stomp": {"name": "踩踏", "effect": "用大脚踩踏对手进行攻击。\n有时会使对手畏缩"}, "doubleKick": {"name": "二连踢", "effect": "用２只脚踢飞对手进行攻击。\n连续２次给予伤害"}, "megaKick": {"name": "百万吨重踢", "effect": "使出力大无穷的重踢踢飞对手进行攻击"}, "jumpKick": {"name": "飞踢", "effect": "使出高高的腾空踢攻击对手。\n如果踢偏则自己会受到伤害"}, "rollingKick": {"name": "回旋踢", "effect": "一边使身体快速旋转，\n一边踢飞对手进行攻击。\n有时会使对手畏缩"}, "sandAttack": {"name": "泼沙", "effect": "向对手脸上泼沙子，从而降低命中率"}, "headbutt": {"name": "头锤", "effect": "将头伸出，笔直地扑向对手进行攻击。\n有时会使对手畏缩"}, "hornAttack": {"name": "角撞", "effect": "用尖锐的角攻击对手"}, "furyAttack": {"name": "乱击", "effect": "用角或喙刺向对手进行攻击。\n连续攻击２～５次"}, "hornDrill": {"name": "角钻", "effect": "用旋转的角刺入对手进行攻击。\n只要命中就会一击昏厥"}, "tackle": {"name": "撞击", "effect": "用整个身体撞向对手进行攻击"}, "bodySlam": {"name": "泰山压顶", "effect": "用整个身体压住对手进行攻击。\n有时会让对手陷入麻痹状态"}, "wrap": {"name": "紧束", "effect": "使用长长的身体或藤蔓等，\n在４～５回合内紧束对手进行攻击"}, "takeDown": {"name": "猛撞", "effect": "以惊人的气势撞向对手进行攻击。\n自己也会受到少许伤害"}, "thrash": {"name": "大闹一番", "effect": "在２～３回合内，乱打一气地攻击对手。\n大闹一番后自己会陷入混乱"}, "doubleEdge": {"name": "舍身冲撞", "effect": "拼命地猛撞向对手进行攻击。\n自己也会受到不小的伤害"}, "tailWhip": {"name": "摇尾巴", "effect": "可爱地左右摇晃尾巴，\n诱使对手疏忽大意。会降低对手的防御"}, "poisonSting": {"name": "毒针", "effect": "将有毒的针刺入对手进行攻击。\n有时会让对手陷入中毒状态"}, "twineedle": {"name": "双针", "effect": "将２根针刺入对手，连续２次给予伤害。\n有时会让对手陷入中毒状态"}, "pinMissile": {"name": "飞弹针", "effect": "向对手发射锐针进行攻击。\n连续攻击２～５次"}, "leer": {"name": "瞪眼", "effect": "用犀利的眼神使其害怕，\n从而降低对手的防御"}, "bite": {"name": "咬住", "effect": "用尖锐的牙咬住对手进行攻击。\n有时会使对手畏缩"}, "growl": {"name": "叫声", "effect": "让对手听可爱的叫声，\n引开注意力使其疏忽，\n从而降低对手的攻击"}, "roar": {"name": "吼叫", "effect": "放走对手，强制拉后备宝可梦上场。\n如果对手为野生宝可梦，\n战斗将直接结束"}, "sing": {"name": "唱歌", "effect": "让对手听舒适、美妙的歌声，\n从而陷入睡眠状态"}, "supersonic": {"name": "超音波", "effect": "从身体发出特殊的音波，\n从而使对手混乱"}, "sonicBoom": {"name": "音爆", "effect": "将冲击波撞向对手进行攻击。\n必定会给予20的伤害"}, "disable": {"name": "定身法", "effect": "阻碍对手行动，之前使出的招式将\n在４回合内无法使用"}, "acid": {"name": "溶解液", "effect": "将强酸泼向对手进行攻击。\n有时会降低对手的特防"}, "ember": {"name": "火花", "effect": "向对手发射小型火焰进行攻击。\n有时会让对手陷入灼伤状态"}, "flamethrower": {"name": "喷射火焰", "effect": "向对手发射烈焰进行攻击。\n有时会让对手陷入灼伤状态"}, "mist": {"name": "白雾", "effect": "用白雾覆盖身体。在５回合内不会\n让对手降低自己的能力"}, "waterGun": {"name": "水枪", "effect": "向对手猛烈地喷射水流进行攻击"}, "hydroPump": {"name": "水炮", "effect": "向对手猛烈地喷射大量水流进行攻击"}, "surf": {"name": "冲浪", "effect": "利用大浪攻击自己周围所有的宝可梦"}, "iceBeam": {"name": "冰冻光束", "effect": "向对手发射冰冻光束进行攻击。\n有时会让对手陷入冰冻状态"}, "blizzard": {"name": "暴风雪", "effect": "将猛烈的暴风雪刮向对手进行攻击。\n有时会让对手陷入冰冻状态"}, "psybeam": {"name": "幻象光线", "effect": "向对手发射神奇的光线进行攻击。\n有时会使对手混乱"}, "bubbleBeam": {"name": "泡沫光线", "effect": "向对手猛烈地喷射泡沫进行攻击。\n有时会降低对手的速度"}, "auroraBeam": {"name": "极光束", "effect": "向对手发射虹色光束进行攻击。\n有时会降低对手的攻击"}, "hyperBeam": {"name": "破坏光线", "effect": "向对手发射强烈的光线进行攻击。\n下一回合自己将无法动弹"}, "peck": {"name": "啄", "effect": "用尖锐的喙或角刺向对手进行攻击"}, "drillPeck": {"name": "啄钻", "effect": "一边旋转，一边将尖喙刺入对手进行攻击"}, "submission": {"name": "深渊翻滚", "effect": "将对手连同自己一起摔向地面进行攻击。\n自己也会受到少许伤害"}, "lowKick": {"name": "踢倒", "effect": "用力踢对手的脚，使其摔倒进行攻击。\n对手越重，威力越大"}, "counter": {"name": "双倍奉还", "effect": "从对手那里受到物理攻击的伤害将\n以２倍返还给同一个对手"}, "seismicToss": {"name": "地球上投", "effect": "利用引力将对手甩飞出去。\n给予对手和自己等级相同的伤害"}, "strength": {"name": "怪力", "effect": "使出浑身力气殴打对手进行攻击"}, "absorb": {"name": "吸取", "effect": "吸取对手的养分进行攻击。\n可以回复给予对手伤害的一半ＨＰ"}, "megaDrain": {"name": "超级吸取", "effect": "吸取对手的养分进行攻击。\n可以回复给予对手伤害的一半ＨＰ"}, "leechSeed": {"name": "寄生种子", "effect": "植入寄生种子后，将在每回合一点\n一点吸取对手的ＨＰ，\n从而用来回复自己的ＨＰ"}, "growth": {"name": "生长", "effect": "让身体一下子长大，从而提高攻击和特攻"}, "razorLeaf": {"name": "飞叶快刀", "effect": "飞出叶片，切斩对手进行攻击。\n容易击中要害"}, "solarBeam": {"name": "日光束", "effect": "第１回合收集满满的日光，\n第２回合发射光束进行攻击"}, "poisonPowder": {"name": "毒粉", "effect": "撒出毒粉，从而让对手陷入中毒状态"}, "stunSpore": {"name": "麻痹粉", "effect": "撒出麻痹粉，从而让对手陷入麻痹状态"}, "sleepPowder": {"name": "催眠粉", "effect": "撒出催眠粉，从而让对手陷入睡眠状态"}, "petalDance": {"name": "花瓣舞", "effect": "在２～３回合内，散落花瓣攻击对手。\n之后自己会陷入混乱"}, "stringShot": {"name": "吐丝", "effect": "用口中吐出的丝缠绕对手，\n从而大幅降低对手的速度"}, "dragonRage": {"name": "龙之怒", "effect": "将愤怒的冲击波撞向对手进行攻击。\n必定会给予40的伤害"}, "fireSpin": {"name": "火焰旋涡", "effect": "将对手困在激烈的火焰旋涡中，\n在４～５回合内进行攻击"}, "thunderShock": {"name": "电击", "effect": "发出电流刺激对手进行攻击。\n有时会让对手陷入麻痹状态"}, "thunderbolt": {"name": "十万伏特", "effect": "向对手发出强力电击进行攻击。\n有时会让对手陷入麻痹状态"}, "thunderWave": {"name": "电磁波", "effect": "向对手发出微弱的电击，\n从而让对手陷入麻痹状态"}, "thunder": {"name": "打雷", "effect": "向对手劈下暴雷进行攻击。\n有时会让对手陷入麻痹状态"}, "rockThrow": {"name": "落石", "effect": "拿起小岩石，投掷对手进行攻击"}, "earthquake": {"name": "地震", "effect": "利用地震的冲击，攻击自己周围所\n有的宝可梦"}, "fissure": {"name": "地裂", "effect": "让对手掉落于地裂的裂缝中进行攻击。\n只要命中就会一击昏厥"}, "dig": {"name": "挖洞", "effect": "第１回合钻入地底，第２回合攻击对手"}, "toxic": {"name": "剧毒", "effect": "让对手陷入剧毒状态。\n随着回合的推进，中毒伤害会增加"}, "confusion": {"name": "念力", "effect": "向对手发送微弱的念力进行攻击。\n有时会使对手混乱"}, "psychic": {"name": "精神强念", "effect": "向对手发送强大的念力进行攻击。\n有时会降低对手的特防"}, "hypnosis": {"name": "催眠术", "effect": "施以诱导睡意的暗示，\n让对手陷入睡眠状态"}, "meditate": {"name": "瑜伽姿势", "effect": "唤醒身体深处沉睡的力量，\n从而提高自己的攻击"}, "agility": {"name": "高速移动", "effect": "让身体放松变得轻盈，\n以便高速移动。大幅提高自己的速度"}, "quickAttack": {"name": "电光一闪", "effect": "以迅雷不及掩耳之势扑向对手。\n必定能够先制攻击"}, "rage": {"name": "愤怒", "effect": "如果在使出招式后受到攻击的话，\n会因愤怒的力量而提高攻击"}, "teleport": {"name": "瞬间移动", "effect": "当有后备宝可梦时使用，\n就可以进行替换。\n野生的宝可梦使用则会逃走"}, "nightShade": {"name": "黑夜魔影", "effect": "显示恐怖幻影，只给予对手和自己\n等级相同的伤害"}, "mimic": {"name": "模仿", "effect": "可以将对手最后使用的招式，\n在战斗内变成自己的招式"}, "screech": {"name": "刺耳声", "effect": "发出不由自主想要捂起耳朵的刺耳声，\n从而大幅降低对手的防御"}, "doubleTeam": {"name": "影子分身", "effect": "通过快速移动来制造分身，\n扰乱对手，从而提高闪避率"}, "recover": {"name": "自我再生", "effect": "让细胞再生，从而回复自己最大Ｈ\nＰ的一半"}, "harden": {"name": "变硬", "effect": "全身使劲，让身体变硬，\n从而提高自己的防御"}, "minimize": {"name": "变小", "effect": "蜷缩身体显得很小，从而大幅提高\n自己的闪避率"}, "smokescreen": {"name": "烟幕", "effect": "向对手喷出烟或墨汁等，\n从而降低对手的命中率"}, "confuseRay": {"name": "奇异之光", "effect": "显示奇怪的光，扰乱对手。\n使对手混乱"}, "withdraw": {"name": "缩入壳中", "effect": "缩入壳里保护身体，从而提高自己的防御"}, "defenseCurl": {"name": "变圆", "effect": "将身体蜷曲变圆，从而提高自己的防御"}, "barrier": {"name": "屏障", "effect": "制造坚固的壁障，从而大幅提高自\n己的防御"}, "lightScreen": {"name": "光墙", "effect": "利用神奇的墙壁，在５回合内减弱\n从对手那里受到的特殊攻击的伤害"}, "haze": {"name": "黑雾", "effect": "升起黑雾，将正在场上战斗的全体\n宝可梦的能力变回原点"}, "reflect": {"name": "反射壁", "effect": "利用神奇的墙壁，在５回合内减弱\n从对手那里受到的物理攻击的伤害"}, "focusEnergy": {"name": "聚气", "effect": "深深地吸口气，集中精神。\n自己的攻击会变得容易击中要害"}, "bide": {"name": "忍耐", "effect": "在２回合内忍受攻击，\n受到的伤害会２倍返还给对手"}, "metronome": {"name": "挥指", "effect": "挥动手指刺激自己的大脑，\n从许多的招式中随机使出１个"}, "mirrorMove": {"name": "鹦鹉学舌", "effect": "模仿对手使用的招式，\n自己也使用相同招式"}, "selfDestruct": {"name": "玉石俱碎", "effect": "引发爆炸，攻击自己周围所有的宝可梦。\n使用后陷入昏厥"}, "eggBomb": {"name": "炸蛋", "effect": "向对手用力投掷大大的蛋进行攻击"}, "lick": {"name": "舌舔", "effect": "用长长的舌头，舔遍对手进行攻击。\n有时会让对手陷入麻痹状态"}, "smog": {"name": "浊雾", "effect": "将肮脏的浓雾吹向对手进行攻击。\n有时会让对手陷入中毒状态"}, "sludge": {"name": "污泥攻击", "effect": "用污泥投掷对手进行攻击。\n有时会让对手陷入中毒状态"}, "boneClub": {"name": "骨棒", "effect": "用手中的骨头殴打对手进行攻击。\n有时会使对手畏缩"}, "fireBlast": {"name": "大字爆炎", "effect": "用大字形状的火焰烧尽对手。\n有时会让对手陷入灼伤状态"}, "waterfall": {"name": "攀瀑", "effect": "以惊人的气势扑向对手。\n有时会使对手畏缩"}, "clamp": {"name": "贝壳夹击", "effect": "用非常坚固且厚实的贝壳，\n在４～５回合内夹住对手进行攻击"}, "swift": {"name": "高速星星", "effect": "发射星形的光攻击对手。\n攻击必定会命中"}, "skullBash": {"name": "火箭头锤", "effect": "第１回合把头缩进去，\n从而提高防御。第２回合攻击对手"}, "spikeCannon": {"name": "尖刺加农炮", "effect": "向对手发射锐针进行攻击。\n连续攻击２～５次"}, "constrict": {"name": "缠绕", "effect": "用触手或青藤等缠绕进行攻击。\n有时会降低对手的速度"}, "amnesia": {"name": "瞬间失忆", "effect": "将头脑清空，瞬间忘记某事，\n从而大幅提高自己的特防"}, "kinesis": {"name": "折弯汤匙", "effect": "折弯汤匙引开注意，从而降低对手\n的命中率"}, "softBoiled": {"name": "生蛋", "effect": "回复自己最大ＨＰ的一半"}, "highJumpKick": {"name": "飞膝踢", "effect": "跳起后用膝盖撞对手进行攻击。\n如果撞偏则自己会受到伤害"}, "glare": {"name": "大蛇瞪眼", "effect": "用腹部的花纹使对手害怕，\n从而让其陷入麻痹状态"}, "dreamEater": {"name": "食梦", "effect": "吃掉正在睡觉的对手的梦进行攻击。\n回复对手所受到伤害的一半ＨＰ"}, "poisonGas": {"name": "毒瓦斯", "effect": "将毒瓦斯吹到对手的脸上，\n从而让对手陷入中毒状态"}, "barrage": {"name": "投球", "effect": "向对手投掷圆形物体进行攻击。\n连续攻击２～５次"}, "leechLife": {"name": "吸血", "effect": "吸取血液攻击对手。可以回复给予\n对手伤害的一半ＨＰ"}, "lovelyKiss": {"name": "恶魔之吻", "effect": "用恐怖的脸强吻对手。\n让对手陷入睡眠状态"}, "skyAttack": {"name": "神鸟猛击", "effect": "第２回合攻击对手。偶尔使对手畏缩。\n也容易击中要害"}, "transform": {"name": "变身", "effect": "变身成对手宝可梦的样子，\n能够使用和对手完全相同的招式"}, "bubble": {"name": "泡沫", "effect": "向对手用力吹起无数泡泡进行攻击。\n有时会降低对手的速度"}, "dizzyPunch": {"name": "迷昏拳", "effect": "有节奏地出拳攻击对手。\n有时会使对手混乱"}, "spore": {"name": "蘑菇孢子", "effect": "沙沙沙地撒满具有催眠效果的孢子，\n从而让对手陷入睡眠状态"}, "flash": {"name": "闪光", "effect": "使出光芒，从而降低对手的命中率。\n也可在阴暗的洞窟里照亮四周"}, "psywave": {"name": "精神波", "effect": "向对手发射神奇的念波进行攻击。\n每次使用，伤害都会改变"}, "splash": {"name": "跃起", "effect": "也不攻击只是一蹦一蹦地跳，\n什么都不会发生…"}, "acidArmor": {"name": "溶化", "effect": "通过细胞的变化进行液化，\n从而大幅提高自己的防御"}, "crabhammer": {"name": "蟹钳锤", "effect": "用大钳子敲打对手进行攻击。\n容易击中要害"}, "explosion": {"name": "大爆炸", "effect": "引发大爆炸，\n攻击自己周围所有的宝可梦。\n使用后自己会陷入昏厥"}, "furySwipes": {"name": "乱抓", "effect": "用爪子或镰刀等抓对手进行攻击。\n连续攻击２～５次"}, "bonemerang": {"name": "骨头回力镖", "effect": "用手中的骨头投掷对手，\n来回连续２次给予伤害"}, "rest": {"name": "睡觉", "effect": "连续睡上２回合。回复自己的全部\nＨＰ以及治愈所有异常状态"}, "rockSlide": {"name": "岩崩", "effect": "将大岩石猛烈地撞向对手进行攻击。\n有时会使对手畏缩"}, "hyperFang": {"name": "终结门牙", "effect": "用锋利的门牙牢牢地咬住对手进行攻击。\n有时会使对手畏缩"}, "sharpen": {"name": "棱角化", "effect": "增加身体的角，变得棱棱角角，\n从而提高自己的攻击"}, "conversion": {"name": "纹理", "effect": "将自己的属性转换成和已学会的招\n式中第一个招式相同的属性"}, "triAttack": {"name": "三重攻击", "effect": "用３种光线进行攻击。\n有时会让对手陷入麻痹、\n灼伤或冰冻的状态"}, "superFang": {"name": "愤怒门牙", "effect": "用锋利的门牙猛烈地咬住对手进行攻击。\n对手的ＨＰ减半"}, "slash": {"name": "劈开", "effect": "用爪子或镰刀等劈开对手进行攻击。\n容易击中要害"}, "substitute": {"name": "替身", "effect": "削减少许自己的ＨＰ，\n制造分身。分身将成为自己的替身"}, "struggle": {"name": "挣扎", "effect": "当自己的ＰＰ耗尽时，\n努力挣扎攻击对手。\n自己也会受到少许伤害"}, "sketch": {"name": "写生", "effect": "将对手使用的招式变成自己的招式。\n使用１次后写生消失"}, "tripleKick": {"name": "三连踢", "effect": "连续３次踢对手进行攻击。\n每踢中一次，威力就会提高"}, "thief": {"name": "小偷", "effect": "攻击的同时尝试盗取道具。\n有30%的概率获得一件道具。"}, "spiderWeb": {"name": "蛛网", "effect": "将黏糊糊的细丝一层一层缠住对手，\n使其不能从战斗中逃走"}, "mindReader": {"name": "心之眼", "effect": "用心感受对手的行动，\n下次攻击必定会击中对手"}, "nightmare": {"name": "恶梦", "effect": "让在睡眠状态下的对手做恶梦，\n每回合会缓缓减少HP"}, "flameWheel": {"name": "火焰轮", "effect": "让火焰覆盖全身，猛撞向对手进行攻击。\n有时会让对手陷入灼伤状态"}, "snore": {"name": "打鼾", "effect": "在自己睡觉时，发出噪音进行攻击。\n有时会使对手畏缩"}, "curse": {"name": "诅咒", "effect": "幽灵系使用会损失相当于\n其最大生命值一半的生命值，\n并对目标施放诅咒，\n每回合造成伤害。\n否则会降低目标的速度,\n但提升攻击力和防御力。"}, "flail": {"name": "抓狂", "effect": "抓狂般乱打进行攻击。\n自己的ＨＰ越少，招式的威力越大"}, "conversion2": {"name": "纹理２", "effect": "为了可以抵抗对手最后使用的招式，\n从而使自己的属性发生变化"}, "aeroblast": {"name": "气旋攻击", "effect": "发射空气旋涡进行攻击。\n容易击中要害"}, "cottonSpore": {"name": "棉孢子", "effect": "将棉花般柔软的孢子紧贴对手，\n从而大幅降低对手的速度"}, "reversal": {"name": "绝处逢生", "effect": "竭尽全力进行攻击。自己的ＨＰ越少，\n招式的威力越大"}, "spite": {"name": "怨恨", "effect": "对对手最后使用的招式怀有怨恨，\n减少４ＰＰ该招式"}, "powderSnow": {"name": "细雪", "effect": "将冰冷的细雪吹向对手进行攻击。\n有时会让对手陷入冰冻状态"}, "protect": {"name": "守住", "effect": "完全抵挡对手的攻击。\n连续使出则容易失败"}, "machPunch": {"name": "音速拳", "effect": "以迅雷不及掩耳之势出拳。\n必定能够先制攻击"}, "scaryFace": {"name": "可怕面孔", "effect": "用恐怖的表情瞪着对手，\n使其害怕，从而大幅降低对手的速度"}, "feintAttack": {"name": "出奇一击", "effect": "悄悄地靠近对手，趁其不备进行殴打。\n攻击必定会命中"}, "sweetKiss": {"name": "天使之吻", "effect": "像天使般可爱地亲吻对手，\n从而使对手混乱"}, "bellyDrum": {"name": "腹鼓", "effect": "将自己的ＨＰ减少到最大ＨＰ的一半，\n从而最大限度提高自己的攻击"}, "sludgeBomb": {"name": "污泥炸弹", "effect": "用污泥投掷对手进行攻击。\n有时会让对手陷入中毒状态"}, "mudSlap": {"name": "掷泥", "effect": "向对手的脸等投掷泥块进行攻击。\n会降低对手的命中率"}, "octazooka": {"name": "章鱼桶炮", "effect": "向对手的脸等喷出墨汁进行攻击。\n有时会降低对手的命中率"}, "spikes": {"name": "撒菱", "effect": "在对手的脚下扔撒菱。\n对替换出场的对手的宝可梦给予伤害"}, "zapCannon": {"name": "电磁炮", "effect": "发射大炮一样的电流进行攻击。\n让对手陷入麻痹状态"}, "foresight": {"name": "识破", "effect": "使出后对幽灵属性宝可梦没有效果\n的招式以及闪避率高的对手，\n变得能够打中"}, "destinyBond": {"name": "同命", "effect": "使出招式后，当受到对手攻击\n陷入昏厥时，对手也会一同昏厥。\n连续使出则会失败"}, "perishSong": {"name": "终焉之歌", "effect": "倾听歌声的宝可梦经过３回合陷入昏厥。\n替换后效果消失"}, "icyWind": {"name": "冰冻之风", "effect": "将结冰的冷气吹向对手进行攻击。\n会降低对手的速度"}, "detect": {"name": "看穿", "effect": "完全抵挡对手的攻击。\n连续使出则容易失败"}, "boneRush": {"name": "骨棒乱打", "effect": "用坚硬的骨头殴打对手进行攻击。\n连续攻击２～５次"}, "lockOn": {"name": "锁定", "effect": "紧紧瞄准对手，下次攻击必定会打中"}, "outrage": {"name": "逆鳞", "effect": "在２～３回合内，乱打一气地进行攻击。\n大闹一番后自己会陷入混乱"}, "sandstorm": {"name": "沙暴", "effect": "在５回合内扬起沙暴，\n除岩石、地面和钢属性以外的宝可梦，\n都会受到伤害。岩石属性的特防还会提高"}, "gigaDrain": {"name": "终极吸取", "effect": "吸取对手的养分进行攻击。\n可以回复给予对手伤害的一半ＨＰ"}, "endure": {"name": "挺住", "effect": "即使受到攻击，也至少会留下１ＨＰ。\n连续使出则容易失败"}, "charm": {"name": "撒娇", "effect": "可爱地凝视，诱使对手疏忽大意，\n从而大幅降低对手的攻击"}, "rollout": {"name": "滚动", "effect": "在５回合内连续滚动攻击对手。\n招式每次击中，威力就会提高"}, "falseSwipe": {"name": "点到为止", "effect": "对手的ＨＰ至少会留下１ＨＰ，\n如此般手下留情地攻击"}, "swagger": {"name": "虚张声势", "effect": "激怒对手，使其混乱。\n因为愤怒，对手的攻击会大幅提高"}, "milkDrink": {"name": "喝牛奶", "effect": "回复自己最大ＨＰ的一半"}, "spark": {"name": "电光", "effect": "让电流覆盖全身，猛撞向对手进行攻击。\n有时会让对手陷入麻痹状态"}, "furyCutter": {"name": "连斩", "effect": "用镰刀或爪子等切斩对手进行攻击。\n连续击中，威力就会提高"}, "steelWing": {"name": "钢翼", "effect": "用坚硬的翅膀敲打对手进行攻击。\n有时会提高自己的防御"}, "meanLook": {"name": "黑色目光", "effect": "用好似要勾人心魂的黑色目光一动\n不动地凝视对手，使其不能从战斗中逃走"}, "attract": {"name": "迷人", "effect": "♂诱惑♀或♀诱惑♂，让对手着迷。\n对手将很难使出招式"}, "sleepTalk": {"name": "梦话", "effect": "从自己已学会的招式中任意使出１个。\n只能在自己睡觉时使用"}, "healBell": {"name": "治愈铃声", "effect": "让同伴听舒适的铃音，\n从而治愈我方全员的异常状态"}, "return": {"name": "报恩", "effect": "为了训练家而全力攻击对手。\n亲密度越高，威力越大"}, "present": {"name": "礼物", "effect": "递给对手设有圈套的盒子进行攻击。\n也有可能回复对手ＨＰ"}, "frustration": {"name": "迁怒", "effect": "为了发泄不满而全力攻击对手。\n亲密度越低，威力越大"}, "safeguard": {"name": "神秘守护", "effect": "在５回合内被神奇的力量守护，\n从而不会陷入异常状态"}, "painSplit": {"name": "分担痛楚", "effect": "将自己的ＨＰ和对手的ＨＰ相加，\n然后自己和对手友好地平分"}, "sacredFire": {"name": "神圣之火", "effect": "用神秘的火焰烧尽对手进行攻击。\n有时会让对手陷入灼伤状态"}, "magnitude": {"name": "震级", "effect": "晃动地面，攻击自己周围所有的宝可梦。\n招式的威力会有各种变化"}, "dynamicPunch": {"name": "爆裂拳", "effect": "使出浑身力气出拳进行攻击。\n必定会使对手混乱"}, "megahorn": {"name": "超级角击", "effect": "用坚硬且华丽的角狠狠地刺入对手\n进行攻击"}, "dragonBreath": {"name": "龙息", "effect": "将强烈的气息吹向对手进行攻击。\n有时会让对手陷入麻痹状态"}, "batonPass": {"name": "接棒", "effect": "和后备宝可梦进行替换。\n换上的宝可梦能直接继承其能力的变化"}, "encore": {"name": "再来一次", "effect": "让对手接受再来一次，\n连续３次使出最后使用的招式"}, "pursuit": {"name": "追打", "effect": "当对手替换宝可梦上场时\n使出此招式的话，\n能够以２倍的威力进行攻击"}, "rapidSpin": {"name": "高速旋转", "effect": "通过旋转来攻击对手。\n可以摆脱绑紧、紧束、寄生种子等招式。\n还能提高自己的速度"}, "sweetScent": {"name": "甜甜香气", "effect": "用香气大幅降低对手的闪避率"}, "ironTail": {"name": "铁尾", "effect": "使用坚硬的尾巴摔打对手进行攻击。\n有时会降低对手的防御"}, "metalClaw": {"name": "金属爪", "effect": "用钢铁之爪劈开对手进行攻击。\n有时会提高自己的攻击"}, "vitalThrow": {"name": "借力摔", "effect": "会在对手之后进行攻击。\n但是自己的攻击必定会命中"}, "morningSun": {"name": "晨光", "effect": "回复自己的ＨＰ。根据天气的不同，\n回复量也会有所变化"}, "synthesis": {"name": "光合作用", "effect": "回复自己的ＨＰ。根据天气的不同，\n回复量也会有所变化"}, "moonlight": {"name": "月光", "effect": "回复自己的ＨＰ。根据天气的不同，\n回复量也会有所变化"}, "hiddenPower": {"name": "觉醒力量", "effect": "招式的属性会随着使用此招式的宝\n可梦而改变"}, "crossChop": {"name": "十字劈", "effect": "用两手呈十字劈打对手进行攻击。\n容易击中要害"}, "twister": {"name": "龙卷风", "effect": "兴起龙卷风，将对手卷入进行攻击。\n有时会使对手畏缩"}, "rainDance": {"name": "求雨", "effect": "在５回合内一直降雨，\n从而提高水属性的招式威力。\n火属性的招式威力则降低"}, "sunnyDay": {"name": "大晴天", "effect": "在５回合内让日照变得强烈，\n从而提高火属性的招式威力。\n水属性的招式威力则降低"}, "crunch": {"name": "咬碎", "effect": "用利牙咬碎对手进行攻击。\n有时会降低对手的防御"}, "mirrorCoat": {"name": "镜面反射", "effect": "从对手那里受到特殊攻击的伤害将\n以２倍返还给同一个对手"}, "psychUp": {"name": "自我暗示", "effect": "向自己施以自我暗示，\n将能力变化的状态变得和对手一样"}, "extremeSpeed": {"name": "神速", "effect": "以迅雷不及掩耳之势猛\n撞向对手进行攻击。\n必定能够先制攻击"}, "ancientPower": {"name": "原始之力", "effect": "用原始之力进行攻击。\n有时会提高自己所有的能力"}, "shadowBall": {"name": "暗影球", "effect": "投掷一团黑影进行攻击。\n有时会降低对手的特防"}, "futureSight": {"name": "预知未来", "effect": "在使用招式２回合后，\n向对手发送一团念力进行攻击"}, "rockSmash": {"name": "碎岩", "effect": "用拳头进行攻击。有时会降低对手的防御"}, "whirlpool": {"name": "潮旋", "effect": "将对手困在激烈的水流旋涡中，\n在４～５回合内进行攻击"}, "beatUp": {"name": "围攻", "effect": "我方全员进行攻击。同行的宝可梦越多，\n招式的攻击次数越多"}, "fakeOut": {"name": "击掌奇袭", "effect": "进行先制攻击，使对手畏缩。\n要在出场后立刻使出才能成功"}, "uproar": {"name": "吵闹", "effect": "在３回合内大吵大闹攻击对手。\n在此期间谁都不能入眠"}, "stockpile": {"name": "蓄力", "effect": "积蓄力量，提高自己的防御和特防。\n最多积蓄３次"}, "spitUp": {"name": "喷出", "effect": "将积蓄的力量撞向对手进行攻击。\n积蓄得越多，威力越大"}, "swallow": {"name": "吞下", "effect": "将积蓄的力量吞下，\n从而回复自己的ＨＰ。\n积蓄得越多，回复越大"}, "heatWave": {"name": "热风", "effect": "将炎热的气息吹向对手进行攻击。\n有时会让对手陷入灼伤状态"}, "hail": {"name": "冰雹", "effect": "在５回合内一直降冰雹，\n除冰属性的宝可梦以外，\n给予全体宝可梦伤害"}, "torment": {"name": "无理取闹", "effect": "向对手无理取闹，令其不能连续２\n次使出相同招式"}, "flatter": {"name": "吹捧", "effect": "吹捧对手，使其混乱。\n同时还会提高对手的特攻"}, "willOWisp": {"name": "磷火", "effect": "放出怪异的火焰，从而让对手陷入\n灼伤状态"}, "memento": {"name": "临别礼物", "effect": "虽然会使自己陷入昏厥，\n但是能够大幅降低对手的攻击和特攻"}, "facade": {"name": "硬撑", "effect": "当自己处于中毒、麻痹、灼伤状态时，\n向对手使出此招式的话，\n威力会变成２倍"}, "focusPunch": {"name": "真气拳", "effect": "集中精神出拳。在招式使出前若受\n到攻击则会失败"}, "smellingSalts": {"name": "清醒", "effect": "对于麻痹状态下的对手，\n威力会变成２倍。\n但相反对手的麻痹也会被治愈"}, "followMe": {"name": "看我嘛", "effect": "引起对手的注意，将对手的攻击全\n部转移到自己身上"}, "naturePower": {"name": "自然之力", "effect": "用自然之力进行攻击。\n根据所使用场所的不同，\n使出的招式也会有所变化"}, "charge": {"name": "充电", "effect": "变为充电状态，提高下次使出的电\n属性的招式威力。自己的特防也会提高"}, "taunt": {"name": "挑衅", "effect": "使对手愤怒。在３回合内让对手只\n能使出给予伤害的招式"}, "helpingHand": {"name": "帮助", "effect": "帮助伙伴。被帮助的宝可梦，\n其招式威力变得比平时大"}, "trick": {"name": "戏法", "effect": "抓住对手的空隙，交换自己和对手\n的持有物"}, "rolePlay": {"name": "扮演", "effect": "扮演对手，让自己的特性变得和对手相同"}, "wish": {"name": "祈愿", "effect": "在下一回合回复自己或是替换出场\n的宝可梦最大ＨＰ的一半"}, "assist": {"name": "借助", "effect": "向同伴紧急求助，从我方宝可梦已\n学会的招式中随机使用１个"}, "ingrain": {"name": "扎根", "effect": "在大地上扎根，每回合回复自己的ＨＰ。\n因为扎根了，所以不能替换宝可梦"}, "superpower": {"name": "蛮力", "effect": "发挥惊人的力量攻击对手。\n自己的攻击和防御会降低"}, "magicCoat": {"name": "魔法反射", "effect": "当对手使出会变成异常状态的招式\n或寄生种子等时，会将对手的招式\n反射回去"}, "recycle": {"name": "回收利用", "effect": "使战斗中已经消耗掉的\n自己的持有物再生，\n并可以再次使用"}, "revenge": {"name": "报复", "effect": "如果受到对手的招式攻击，\n就能给予对手２倍的伤害"}, "brickBreak": {"name": "劈瓦", "effect": "将手刀猛烈地挥下攻击对手。\n还可以破坏光墙和反射壁等"}, "yawn": {"name": "哈欠", "effect": "打个大哈欠引起睡意。\n在下一回合让对手陷入睡眠状态"}, "knockOff": {"name": "拍落", "effect": "拍落对手的持有物，直到战斗结束\n都不能使用。对手携带道具时会增加伤害"}, "endeavor": {"name": "蛮干", "effect": "给予伤害，使对手的ＨＰ变得和自\n己的ＨＰ一样"}, "eruption": {"name": "喷火", "effect": "爆发怒火攻击对手。自己的ＨＰ越少，\n招式的威力越小"}, "skillSwap": {"name": "特性互换", "effect": "利用超能力互换自己和对手的特性"}, "imprison": {"name": "封印", "effect": "如果对手有和自己相同的招式，\n那么只有对手无法使用该招式"}, "refresh": {"name": "焕然一新", "effect": "让身体休息，治愈自己身上所中的\n毒、麻痹、灼伤的异常状态"}, "grudge": {"name": "怨念", "effect": "因对手的招式而陷入昏厥时给对手\n施加怨念，让该招式的ＰＰ变成０"}, "snatch": {"name": "抢夺", "effect": "将对手打算使用的回复招式或能力\n变化招式夺为己用"}, "secretPower": {"name": "秘密之力", "effect": "根据使用场所不同，该招式的追加\n效果也会有所变化"}, "dive": {"name": "潜水", "effect": "第１回合潜入水中，第２回合浮上\n来进行攻击"}, "armThrust": {"name": "猛推", "effect": "用张开着的双手猛推对手进行攻击。\n连续攻击２～５次"}, "camouflage": {"name": "保护色", "effect": "根据所在场所不同，如水边、草丛\n和洞窟等，可以改变自己的属性"}, "tailGlow": {"name": "萤火", "effect": "凝视闪烁的光芒，集中自己的精神，\n从而巨幅提高特攻"}, "lusterPurge": {"name": "洁净光芒", "effect": "释放耀眼的光芒进行攻击。\n有时会降低对手的特防"}, "mistBall": {"name": "薄雾球", "effect": "用围绕着雾状羽毛的球进行攻击。\n有时会降低对手的特攻"}, "featherDance": {"name": "羽毛舞", "effect": "撒出羽毛，笼罩在对手的周围。\n大幅降低对手的攻击"}, "teeterDance": {"name": "摇晃舞", "effect": "摇摇晃晃地跳起舞蹈，\n让自己周围的宝可梦陷入混乱状态"}, "blazeKick": {"name": "火焰踢", "effect": "攻击对手后，有时会使其陷入灼伤状态。\n也容易击中要害"}, "mudSport": {"name": "玩泥巴", "effect": "一旦使用此招式，\n周围就会弄得到处是泥。\n在５回合内减弱电属性的招式"}, "iceBall": {"name": "冰球", "effect": "在５回合内攻击对手。\n招式每次击中，威力就会提高"}, "needleArm": {"name": "尖刺臂", "effect": "用带刺的手臂猛烈地挥舞进行攻击。\n有时会使对手畏缩"}, "slackOff": {"name": "偷懒", "effect": "偷懒休息。回复自己最大ＨＰ的一半"}, "hyperVoice": {"name": "巨声", "effect": "给予对手又吵又响的巨大震动进行攻击"}, "poisonFang": {"name": "剧毒牙", "effect": "用有毒的牙齿咬住对手进行攻击。\n有时会使对手中剧毒"}, "crushClaw": {"name": "撕裂爪", "effect": "用坚硬的锐爪劈开对手进行攻击。\n有时会降低对手的防御"}, "blastBurn": {"name": "爆炸烈焰", "effect": "用爆炸的火焰烧尽对手进行攻击。\n下一回合自己将无法动弹"}, "hydroCannon": {"name": "加农水炮", "effect": "向对手喷射水炮进行攻击。\n下一回合自己将无法动弹"}, "meteorMash": {"name": "彗星拳", "effect": "使出彗星般的拳头攻击对手。\n有时会提高自己的攻击"}, "astonish": {"name": "惊吓", "effect": "用尖叫声等突然惊吓对手进行攻击。\n有时会使对手畏缩"}, "weatherBall": {"name": "气象球", "effect": "根据使用时的天气，招式属性和威\n力会改变"}, "aromatherapy": {"name": "芳香治疗", "effect": "让同伴闻沁人心脾的香气，\n从而治愈我方全员的异常状态"}, "fakeTears": {"name": "假哭", "effect": "装哭流泪。使对手不知所措，\n从而大幅降低对手的特防"}, "airCutter": {"name": "空气利刃", "effect": "用锐利的风切斩对手进行攻击。\n容易击中要害"}, "overheat": {"name": "过热", "effect": "使出全部力量攻击对手。\n使用之后会因为反作用力，\n自己的特攻大幅降低"}, "odorSleuth": {"name": "气味侦测", "effect": "使出后对幽灵属性宝可梦没有效果\n的招式以及闪避率高的对手，\n变得能够打中"}, "rockTomb": {"name": "岩石封锁", "effect": "投掷岩石进行攻击。封住对手的行动，\n从而降低速度"}, "silverWind": {"name": "银色旋风", "effect": "在风中掺入鳞粉攻击对手。\n有时会提高自己的全部能力"}, "metalSound": {"name": "金属音", "effect": "让对手听摩擦金属般讨厌的声音。\n大幅降低对手的特防"}, "grassWhistle": {"name": "草笛", "effect": "让对手听舒适的笛声，\n从而陷入睡眠状态"}, "tickle": {"name": "挠痒", "effect": "给对手挠痒，使其发笑，\n从而降低对手的攻击和防御"}, "cosmicPower": {"name": "宇宙力量", "effect": "汲取宇宙中神秘的力量，\n从而提高自己的防御和特防"}, "waterSpout": {"name": "喷水", "effect": "掀起潮水进行攻击。自己的ＨＰ越少，\n招式的威力越小"}, "signalBeam": {"name": "信号光束", "effect": "发射神奇的光线进行攻击。\n有时会使对手混乱"}, "shadowPunch": {"name": "暗影拳", "effect": "使出混影之拳。攻击必定会命中"}, "extrasensory": {"name": "神通力", "effect": "发出看不见的神奇力量进行攻击。\n有时会使对手畏缩"}, "skyUppercut": {"name": "冲天拳", "effect": "用冲向天空般高高的上勾拳顶起对\n手进行攻击"}, "sandTomb": {"name": "流沙深渊", "effect": "将对手困在铺天盖地的沙暴中，\n在４～５回合内进行攻击"}, "sheerCold": {"name": "绝对零度", "effect": "给对手一击昏厥。如果是冰属性以\n外的宝可梦使用，就会难以打中"}, "muddyWater": {"name": "浊流", "effect": "向对手喷射浑浊的水进行攻击。\n有时会降低对手的命中率"}, "bulletSeed": {"name": "种子机关枪", "effect": "向对手猛烈地发射种子进行攻击。\n连续攻击２～５次"}, "aerialAce": {"name": "燕返", "effect": "以敏捷的动作戏弄对手后进行切斩。\n攻击必定会命中"}, "icicleSpear": {"name": "冰锥", "effect": "向对手发射锋利的冰柱进行攻击。\n连续攻击２～５次"}, "ironDefense": {"name": "铁壁", "effect": "将皮肤变得坚硬如铁，\n从而大幅提高自己的防御"}, "block": {"name": "挡路", "effect": "张开双手进行阻挡，封住对手的退路，\n使其不能逃走"}, "howl": {"name": "长嚎", "effect": "大声吼叫提高气势，从而提高自己\n和同伴的攻击"}, "dragonClaw": {"name": "龙爪", "effect": "用尖锐的巨爪劈开对手进行攻击"}, "frenzyPlant": {"name": "疯狂植物", "effect": "用大树摔打对手进行攻击。\n下一回合自己将无法动弹"}, "bulkUp": {"name": "健美", "effect": "使出全身力气绷紧肌肉，\n从而提高自己的攻击和防御"}, "bounce": {"name": "弹跳", "effect": "弹跳到高高的空中，第２回合攻击对手。\n有时会让对手陷入麻痹状态"}, "mudShot": {"name": "泥巴射击", "effect": "向对手投掷泥块进行攻击。\n同时降低对手的速度"}, "poisonTail": {"name": "毒尾", "effect": "用尾巴拍打。\n有时会让对手陷入中毒状态，\n也容易击中要害"}, "covet": {"name": "渴望", "effect": "一边可爱地撒娇，\n一边靠近对手进行攻击，\n30%概率夺取对手携带的道具"}, "voltTackle": {"name": "伏特攻击", "effect": "让电流覆盖全身猛撞向对手。\n自己也会受到不小的伤害。\n有时会让对手陷入麻痹状态"}, "magicalLeaf": {"name": "魔法叶", "effect": "散落可以追踪对手的神奇叶片。\n攻击必定会命中"}, "waterSport": {"name": "玩水", "effect": "用水湿透周围。在５回合内减弱火\n属性的招式"}, "calmMind": {"name": "冥想", "effect": "静心凝神，从而提高自己的特攻和特防"}, "leafBlade": {"name": "叶刃", "effect": "像用剑一般操纵叶片切斩对手进行攻击。\n容易击中要害"}, "dragonDance": {"name": "龙之舞", "effect": "激烈地跳起神秘且强有力的舞蹈。\n从而提高自己的攻击和速度"}, "rockBlast": {"name": "岩石爆击", "effect": "向对手发射坚硬的岩石进行攻击。\n连续攻击２～５次"}, "shockWave": {"name": "电击波", "effect": "向对手快速发出电击。\n攻击必定会命中"}, "waterPulse": {"name": "水之波动", "effect": "用水的震动攻击对手。\n有时会使对手混乱"}, "doomDesire": {"name": "破灭之愿", "effect": "使用招式２回合后，会用无数道光\n束攻击对手"}, "psychoBoost": {"name": "精神突进", "effect": "使出全部力量攻击对手。\n使用之后会因为反作用力，\n自己的特攻大幅降低"}, "roost": {"name": "羽栖", "effect": "降到地面，使身体休息。\n回复自己最大ＨＰ的一半"}, "gravity": {"name": "重力", "effect": "在５回合内，飘浮特性和飞行属性\n的宝可梦会被地面属性的招式击中。\n飞向空中的招式也将无法使用"}, "miracleEye": {"name": "奇迹之眼", "effect": "使出后对恶属性宝可梦没有效果的\n招式以及闪避率高的对手，\n变得能够打中"}, "wakeUpSlap": {"name": "唤醒巴掌", "effect": "给予睡眠状态下的对手较大的伤害。\n但相反对手会从睡眠中醒过来"}, "hammerArm": {"name": "臂锤", "effect": "挥舞强力而沉重的拳头，\n给予对手伤害。自己的速度会降低"}, "gyroBall": {"name": "陀螺球", "effect": "让身体高速旋转并撞击对手。\n速度比对手越慢，威力越大"}, "healingWish": {"name": "治愈之愿", "effect": "虽然自己陷入昏厥，但可以治愈后\n备上场的宝可梦的异常状态以及回复ＨＰ"}, "brine": {"name": "盐水", "effect": "当对手的ＨＰ负伤到一半左右时，\n招式威力会变成２倍"}, "naturalGift": {"name": "自然之恩", "effect": "从树果上获得力量进行攻击。\n根据携带的树果，招式属性和威力会改变"}, "feint": {"name": "佯攻", "effect": "能够攻击正在使用守住或看穿等招\n式的对手。解除其守护效果"}, "pluck": {"name": "啄食", "effect": "用喙进行攻击。当对手携带树果时，\n可以食用并获得其效果"}, "tailwind": {"name": "顺风", "effect": "刮起猛烈的旋风，在４回合内提高\n我方全员的速度"}, "acupressure": {"name": "点穴", "effect": "通过点穴让身体舒筋活络。\n大幅提高某１项能力"}, "metalBurst": {"name": "金属爆炸", "effect": "使出招式前，将最后受到的招式的\n伤害大力返还给对手"}, "uTurn": {"name": "急速折返", "effect": "在攻击之后急速返回，\n和后备宝可梦进行替换"}, "closeCombat": {"name": "近身战", "effect": "放弃守护，向对手的怀里突击。\n自己的防御和特防会降低"}, "payback": {"name": "以牙还牙", "effect": "蓄力攻击。如果能在对手之后攻击，\n招式的威力会变成２倍"}, "assurance": {"name": "恶意追击", "effect": "如果此回合内对手已经受到伤害的话，\n招式威力会变成２倍"}, "embargo": {"name": "查封", "effect": "让对手在５回合内不能使用宝可梦\n携带的道具。训练家也不能给那只\n宝可梦使用道具"}, "fling": {"name": "投掷", "effect": "快速投掷携带的道具进行攻击。\n根据道具不同，威力和效果会改变"}, "psychoShift": {"name": "精神转移", "effect": "利用超能力施以暗示，\n从而将自己受到的异常状态转移给对手"}, "trumpCard": {"name": "王牌", "effect": "王牌招式的剩余PP越少，\n招式的威力越大"}, "healBlock": {"name": "回复封锁", "effect": "在５回合内无法通过招式、特性或\n携带的道具来回复HP"}, "wringOut": {"name": "绞紧", "effect": "用力勒紧对手进行攻击。\n对手的HP越多，威力越大"}, "powerTrick": {"name": "力量戏法", "effect": "利用超能力交换自己的攻击和防御的力量"}, "gastroAcid": {"name": "胃液", "effect": "将胃液吐向对手的身体。\n沾上的胃液会消除对手的特性效果"}, "luckyChant": {"name": "幸运咒语", "effect": "向天许愿，从而在５回合内不会被\n对手的攻击打中要害"}, "meFirst": {"name": "抢先一步", "effect": "提高威力，\n抢先使出对手想要使出的招式。\n如果不先使出则会失败"}, "copycat": {"name": "仿效", "effect": "模仿对手刚才使出的招式，\n并使出相同招式。\n如果对手还没出招则会失败"}, "powerSwap": {"name": "力量互换", "effect": "利用超能力互换自己和对手的攻击\n以及特攻的能力变化"}, "guardSwap": {"name": "防守互换", "effect": "利用超能力互换自己和对手的防御\n以及特防的能力变化"}, "punishment": {"name": "惩罚", "effect": "根据能力变化，对手提高的力量越大，\n招式的威力越大"}, "lastResort": {"name": "珍藏", "effect": "当战斗中已学会的招式全部使用过后，\n才能开始使出珍藏的招式"}, "worrySeed": {"name": "烦恼种子", "effect": "种植心神不宁的种子。\n使对手不能入眠，并将特性变成不眠"}, "suckerPunch": {"name": "突袭", "effect": "可以比对手先攻击。对手使出的招\n式如果不是攻击招式则会失败"}, "toxicSpikes": {"name": "毒菱", "effect": "在对手的脚下撒毒菱。\n使对手替换出场的宝可梦中毒"}, "heartSwap": {"name": "心灵互换", "effect": "利用超能力互换自己和对手之间的\n能力变化"}, "aquaRing": {"name": "水流环", "effect": "在自己身体的周围覆盖用水制造的幕。\n每回合回复ＨＰ"}, "magnetRise": {"name": "电磁飘浮", "effect": "利用电气产生的磁力浮在空中。\n在５回合内可以飘浮"}, "flareBlitz": {"name": "闪焰冲锋", "effect": "让火焰覆盖全身猛撞向对手。\n自己也会受到不小的伤害。\n有时会让对手陷入灼伤状态"}, "forcePalm": {"name": "发劲", "effect": "向对手的身体发出冲击波进行攻击。\n有时会让对手陷入麻痹状态"}, "auraSphere": {"name": "波导弹", "effect": "从体内产生出波导之力，\n然后向对手发出。攻击必定会命中"}, "rockPolish": {"name": "岩石打磨", "effect": "打磨自己的身体，减少空气阻力。\n可以大幅提高自己的速度"}, "poisonJab": {"name": "毒击", "effect": "用带毒的触手或手臂刺入对手。\n有时会让对手陷入中毒状态"}, "darkPulse": {"name": "恶之波动", "effect": "从体内发出充满恶意的恐怖气场。\n有时会使对手畏缩"}, "nightSlash": {"name": "暗袭要害", "effect": "抓住瞬间的空隙切斩对手。\n容易击中要害"}, "aquaTail": {"name": "水流尾", "effect": "如惊涛骇浪般挥动大尾巴攻击对手"}, "seedBomb": {"name": "种子炸弹", "effect": "将外壳坚硬的大种子，\n从上方砸下攻击对手"}, "airSlash": {"name": "空气之刃", "effect": "用连天空也能劈开的空气之刃进行攻击。\n有时会使对手畏缩"}, "xScissor": {"name": "十字剪", "effect": "将镰刀或爪子像剪刀般地交叉，\n顺势劈开对手"}, "bugBuzz": {"name": "虫鸣", "effect": "利用振动发出音波进行攻击。\n有时会降低对手的特防"}, "dragonPulse": {"name": "龙之波动", "effect": "从大大的口中掀起冲击波攻击对手"}, "dragonRush": {"name": "龙之俯冲", "effect": "释放出骇人的杀气，一边威慑一边\n撞击对手。有时会使对手畏缩"}, "powerGem": {"name": "力量宝石", "effect": "发射如宝石般闪耀的光芒攻击对手"}, "drainPunch": {"name": "吸取拳", "effect": "用拳头吸取对手的力量。\n可以回复给予对手伤害的一半ＨＰ"}, "vacuumWave": {"name": "真空波", "effect": "挥动拳头，掀起真空波。\n必定能够先制攻击"}, "focusBlast": {"name": "真气弹", "effect": "提高气势，释放出全部力量。\n有时会降低对手的特防"}, "energyBall": {"name": "能量球", "effect": "发射从自然收集的生命力量。\n有时会降低对手的特防"}, "braveBird": {"name": "勇鸟猛攻", "effect": "收拢翅膀，通过低空飞行突击对手。\n自己也会受到不小的伤害"}, "earthPower": {"name": "大地之力", "effect": "向对手脚下释放出大地之力。\n有时会降低对手的特防"}, "switcheroo": {"name": "掉包", "effect": "用一闪而过的速度交换自己和对手\n的持有物"}, "gigaImpact": {"name": "终极冲击", "effect": "使出自己浑身力量突击对手。\n下一回合自己将无法动弹"}, "nastyPlot": {"name": "诡计", "effect": "谋划诡计，激活头脑。\n大幅提高自己的特攻"}, "bulletPunch": {"name": "子弹拳", "effect": "向对手使出如子弹般快速而坚硬的拳头。\n必定能够先制攻击"}, "avalanche": {"name": "雪崩", "effect": "如果受到对手的招式攻击，\n就能给予该对手２倍威力的攻击"}, "iceShard": {"name": "冰砾", "effect": "瞬间制作冰块，快速地扔向对手。\n必定能够先制攻击"}, "shadowClaw": {"name": "暗影爪", "effect": "以影子做成的锐爪，劈开对手。\n容易击中要害"}, "thunderFang": {"name": "雷电牙", "effect": "用蓄满电流的牙齿咬住对手。\n有时会使对手畏缩或陷入麻痹状态"}, "iceFang": {"name": "冰冻牙", "effect": "用藏有冷气的牙齿咬住对手。\n有时会使对手畏缩或陷入冰冻状态"}, "fireFang": {"name": "火焰牙", "effect": "用覆盖着火焰的牙齿咬住对手。\n有时会使对手畏缩或陷入灼伤状态"}, "shadowSneak": {"name": "影子偷袭", "effect": "伸长影子，从对手的背后进行攻击。\n必定能够先制攻击"}, "mudBomb": {"name": "泥巴炸弹", "effect": "向对手发射坚硬的泥弹进行攻击。\n有时会降低对手的命中率"}, "psychoCut": {"name": "精神利刃", "effect": "用实体化的心之利刃劈开对手。\n容易击中要害"}, "zenHeadbutt": {"name": "意念头锤", "effect": "将思念的力量集中在前额进行攻击。\n有时会使对手畏缩"}, "mirrorShot": {"name": "镜光射击", "effect": "抛光自己的身体，\n向对手释放出闪光之力。\n有时会降低对手的命中率"}, "flashCannon": {"name": "加农光炮", "effect": "将身体的光芒聚集在一点释放出去。\n有时会降低对手的特防"}, "rockClimb": {"name": "攀岩", "effect": "发动猛撞攻击，有时会使对手混乱。\n是宝可表的秘传招式之一"}, "defog": {"name": "清除浓雾", "effect": "用强风吹开对手的反射壁或光墙等。\n也会降低对手的闪避率"}, "trickRoom": {"name": "戏法空间", "effect": "制造出离奇的空间。在５回合内速\n度慢的宝可梦可以先行动"}, "dracoMeteor": {"name": "流星群", "effect": "从天空中向对手落下陨石。\n使用之后因为反作用力，\n自己的特攻会大幅降低"}, "discharge": {"name": "放电", "effect": "用耀眼的电击攻击\n自己周围所有的宝可梦。\n有时会陷入麻痹状态"}, "lavaPlume": {"name": "喷烟", "effect": "用熊熊烈火攻击自己周围所有的宝可梦。\n有时会陷入灼伤状态"}, "leafStorm": {"name": "飞叶风暴", "effect": "用尖尖的叶片向对手卷起风暴。\n使用之后因为反作用力自己的特攻会\n大幅降低"}, "powerWhip": {"name": "强力鞭打", "effect": "激烈地挥舞青藤或触手摔打对手进行攻击"}, "rockWrecker": {"name": "岩石炮", "effect": "向对手发射巨大的岩石进行攻击。\n下一回合自己将无法动弹"}, "crossPoison": {"name": "十字毒刃", "effect": "用毒刃劈开对手。有时会让对手陷\n入中毒状态，也容易击中要害"}, "gunkShot": {"name": "垃圾射击", "effect": "用肮脏的垃圾撞向对手进行攻击。\n有时会让对手陷入中毒状态"}, "ironHead": {"name": "铁头", "effect": "用钢铁般坚硬的头部进行攻击。\n有时会使对手畏缩"}, "magnetBomb": {"name": "磁铁炸弹", "effect": "发射吸住对手的钢铁炸弹。\n攻击必定会命中"}, "stoneEdge": {"name": "尖石攻击", "effect": "用尖尖的岩石刺入对手进行攻击。\n容易击中要害"}, "captivate": {"name": "诱惑", "effect": "♂诱惑♀或♀诱惑♂，\n从而大幅降低对手的特攻"}, "stealthRock": {"name": "隐形岩", "effect": "将无数岩石悬浮在对手的周围，\n从而对替换出场的对手的宝可梦给予伤害"}, "grassKnot": {"name": "打草结", "effect": "用草缠住并绊倒对手。\n对手越重，威力越大"}, "chatter": {"name": "喋喋不休", "effect": "用非常烦人的，\n喋喋不休的音波攻击对手。\n使对手混乱"}, "judgment": {"name": "制裁光砾", "effect": "向对手放出无数的光弹。\n属性会根据自己携带的石板不同而改变"}, "bugBite": {"name": "虫咬", "effect": "咬住进行攻击。当对手携带树果时，\n可以食用并获得其效果"}, "chargeBeam": {"name": "充电光束", "effect": "向对手发射电击光束。\n由于蓄满电流，有时会提高自己的特攻"}, "woodHammer": {"name": "木槌", "effect": "用坚硬的躯体撞击对手进行攻击。\n自己也会受到不小的伤害"}, "aquaJet": {"name": "水流喷射", "effect": "以迅雷不及掩耳之势扑向对手。\n必定能够先制攻击"}, "attackOrder": {"name": "攻击指令", "effect": "召唤手下，让其朝对手发起攻击。\n容易击中要害"}, "defendOrder": {"name": "防御指令", "effect": "召唤手下，让其附在自己的身体上。\n可以提高自己的防御和特防"}, "healOrder": {"name": "回复指令", "effect": "召唤手下疗伤。回复自己最大HP的一半"}, "headSmash": {"name": "双刃头锤", "effect": "拼命使出浑身力气，\n向对手进行头锤攻击。\n自己也会受到非常大的伤害"}, "doubleHit": {"name": "二连击", "effect": "使用尾巴等拍打对手进行攻击。\n连续２次给予伤害"}, "roarOfTime": {"name": "时光咆哮", "effect": "释放出扭曲时间般的强大力量攻击对手。\n下一回合自己将无法动弹"}, "spacialRend": {"name": "亚空裂斩", "effect": "将对手连同周围的空间一起撕裂并\n给予伤害。容易击中要害"}, "lunarDance": {"name": "新月舞", "effect": "虽然自己陷入昏厥，但可以治愈后\n备上场的宝可梦的全部状态"}, "crushGrip": {"name": "捏碎", "effect": "用骇人的力量捏碎对手。\n对手剩余的ＨＰ越多，威力越大"}, "magmaStorm": {"name": "熔岩风暴", "effect": "将对手困在熊熊燃烧的火焰中，\n在４～５回合内进行攻击"}, "darkVoid": {"name": "暗黑洞", "effect": "将对手强制拖入黑暗的世界，\n从而让对手陷入睡眠状态"}, "seedFlare": {"name": "种子闪光", "effect": "从身体里产生冲击波。\n有时会大幅降低对手的特防"}, "ominousWind": {"name": "奇异之风", "effect": "突然刮起毛骨悚然的暴风攻击对手。\n有时会提高自己的全部能力"}, "shadowForce": {"name": "暗影潜袭", "effect": "第１回合消失踪影，第２回合攻击对手。\n即使对手正受保护，\n也能击中"}, "honeClaws": {"name": "磨爪", "effect": "将爪子磨得更加锋利。\n提高自己的攻击和命中率"}, "wideGuard": {"name": "广域防守", "effect": "在１回合内防住击打我方全员的攻击"}, "guardSplit": {"name": "防守平分", "effect": "利用超能力将自己和对手的防御和\n特防相加，再进行平分"}, "powerSplit": {"name": "力量平分", "effect": "利用超能力将自己和对手的攻击和\n特攻相加，再进行平分"}, "wonderRoom": {"name": "奇妙空间", "effect": "制造出离奇的空间。在５回合内互\n换所有宝可梦的防御和特防"}, "psyshock": {"name": "精神冲击", "effect": "将神奇的念波实体化攻击对手。\n给予物理伤害"}, "venoshock": {"name": "毒液冲击", "effect": "将特殊的毒液泼向对手。\n对处于中毒状态的对手，威力会变成２倍"}, "autotomize": {"name": "身体轻量化", "effect": "削掉身体上没用的部分。\n大幅提高自己的速度，同时体重也会变轻"}, "ragePowder": {"name": "愤怒粉", "effect": "将令人烦躁的粉末撒在自己身上，\n用以吸引对手的注意。\n使对手的攻击全部指向自己"}, "telekinesis": {"name": "意念移物", "effect": "利用超能力使对手浮起来。\n在３回合内攻击会变得容易打中对手"}, "magicRoom": {"name": "魔法空间", "effect": "制造出离奇的空间。在５回合内所\n有宝可梦携带道具的效果都会消失"}, "smackDown": {"name": "击落", "effect": "扔石头或炮弹，攻击飞行的对手。\n对手会被击落，掉到地面"}, "stormThrow": {"name": "山岚摔", "effect": "向对手使出强烈的一击。\n攻击必定会击中要害"}, "flameBurst": {"name": "烈焰溅射", "effect": "如果击中，爆裂的火焰会攻击到对手。\n爆裂出的火焰还会飞溅到旁边的对手"}, "sludgeWave": {"name": "污泥波", "effect": "用污泥波攻击自己周围所有的宝可梦。\n有时会陷入中毒状态"}, "quiverDance": {"name": "蝶舞", "effect": "轻巧地跳起神秘而又美丽的舞蹈。\n提高自己的特攻、特防和速度"}, "heavySlam": {"name": "重磅冲撞", "effect": "用沉重的身体撞向对手进行攻击。\n自己比对手越重，威力越大"}, "synchronoise": {"name": "同步干扰", "effect": "用神奇电波对周围所有和自己属性\n相同的宝可梦给予伤害"}, "electroBall": {"name": "电球", "effect": "用电气团撞向对手。\n自己比对手速度越快，\n威力越大"}, "soak": {"name": "浸水", "effect": "将大量的水泼向对手，\n从而使其变成水属性"}, "flameCharge": {"name": "蓄能焰袭", "effect": "让火焰覆盖全身，攻击对手。\n积蓄力量来提高自己的速度"}, "coil": {"name": "盘蜷", "effect": "盘蜷着集中精神。提高自己的攻击\n、防御和命中率"}, "lowSweep": {"name": "下盘踢", "effect": "以敏捷的动作瞄准对手的脚进行攻击。\n会降低对手的速度"}, "acidSpray": {"name": "酸液炸弹", "effect": "喷出能溶化对手的液体进行攻击。\n会大幅降低对手的特防"}, "foulPlay": {"name": "欺诈", "effect": "利用对手的力量进行攻击。\n正和自己战斗的对手，其攻击越高，\n伤害越大"}, "simpleBeam": {"name": "单纯光束", "effect": "向对手发送谜之念波。\n接收到念波的对手，其特性会变为单纯"}, "entrainment": {"name": "找伙伴", "effect": "用神奇的节奏跳舞。使对手模仿自\n己的动作，从而将特性变成一样"}, "afterYou": {"name": "您先请", "effect": "支援我方或对手的行动，\n使其紧接着此招式之后行动"}, "round": {"name": "轮唱", "effect": "用歌声攻击对手。大家一起轮唱便\n可以接连使出，威力也会提高"}, "echoedVoice": {"name": "回声", "effect": "用回声攻击对手。如果每回合都有\n宝可梦接着使用该招式，\n威力就会提高"}, "chipAway": {"name": "逐步击破", "effect": "看准机会稳步攻击。\n无视对手的能力变化，\n直接给予伤害"}, "clearSmog": {"name": "清除之烟", "effect": "向对手投掷特殊的泥块进行攻击。\n使其能力变回原点"}, "storedPower": {"name": "辅助力量", "effect": "用蓄积起来的力量攻击对手。\n自己的能力提高得越多，威力就越大"}, "quickGuard": {"name": "快速防守", "effect": "守护自己和同伴，以防对手的先制攻击"}, "allySwitch": {"name": "交换场地", "effect": "用神奇的力量瞬间移动，\n互换自己和同伴所在的位置。\n连续使出则容易失败"}, "scald": {"name": "热水", "effect": "向对手喷射煮得翻滚的开水进行攻击。\n有时会让对手陷入灼伤状态"}, "shellSmash": {"name": "破壳", "effect": "打破外壳，降低自己的防御和特防，\n但大幅提高攻击、特攻和速度"}, "healPulse": {"name": "治愈波动", "effect": "放出治愈波动，从而回复对手最大\nＨＰ的一半"}, "hex": {"name": "祸不单行", "effect": "接二连三地进行攻击。\n对处于异常状态的对手给予较大的伤害"}, "skyDrop": {"name": "自由落体", "effect": "第１回合将对手带到空中，\n第２回合将其摔下进行攻击。\n被带到空中的对手不能动弹"}, "shiftGear": {"name": "换档", "effect": "转动齿轮，不仅提高自己的攻击，\n还会大幅提高速度"}, "circleThrow": {"name": "巴投", "effect": "扔飞对手，强制拉后备宝可梦上场。\n如果对手为野生宝可梦，\n战斗将直接结束"}, "incinerate": {"name": "烧净", "effect": "用火焰攻击对手。对手携带树果等时，\n会烧掉，使其不能使用"}, "quash": {"name": "延后", "effect": "压制对手，从而将其行动顺序放到最后"}, "acrobatics": {"name": "杂技", "effect": "轻巧地攻击对手。自己没有携带道具时，\n会给予较大的伤害"}, "reflectType": {"name": "镜面属性", "effect": "反射对手的属性，让自己也变成一\n样的属性"}, "retaliate": {"name": "报仇", "effect": "为倒下的同伴报仇。如果上一回合\n有同伴倒下，威力就会提高"}, "finalGambit": {"name": "搏命", "effect": "拼命攻击对手。虽然自己陷入昏厥，\n但会给予对手和自己目前ＨＰ等\n量的伤害"}, "bestow": {"name": "传递礼物", "effect": "当对手未携带道具时，\n能够将自己携带的道具交给对手"}, "inferno": {"name": "烈火深渊", "effect": "用烈焰包裹住对手进行攻击。\n让对手陷入灼伤状态"}, "waterPledge": {"name": "水之誓约", "effect": "用水柱进行攻击。如果和火组合，\n威力就会提高，天空中会挂上彩虹"}, "firePledge": {"name": "火之誓约", "effect": "用火柱进行攻击。如果和草组合，\n威力就会提高，周围会变成火海"}, "grassPledge": {"name": "草之誓约", "effect": "用草柱进行攻击。如果和水组合，\n威力就会提高，周围会变成湿地"}, "voltSwitch": {"name": "伏特替换", "effect": "在攻击之后急速返回，\n和后备宝可梦进行替换"}, "struggleBug": {"name": "虫之抵抗", "effect": "抵抗并攻击对手。会降低对手的特攻"}, "bulldoze": {"name": "重踏", "effect": "用力踩踏地面并攻击自己周围所有\n的宝可梦。会降低对方的速度"}, "frostBreath": {"name": "冰息", "effect": "将冰冷的气息吹向对手进行攻击。\n必定会击中要害"}, "dragonTail": {"name": "龙尾", "effect": "弹飞对手，强制拉后备宝可梦上场。\n如果对手为野生宝可梦，\n战斗将直接结束"}, "workUp": {"name": "自我激励", "effect": "激励自己，从而提高攻击和特攻"}, "electroweb": {"name": "电网", "effect": "用电网捉住对手进行攻击。\n会降低对手的速度"}, "wildCharge": {"name": "疯狂伏特", "effect": "让电流覆盖全身，撞向对手进行攻击。\n自己也会受到少许伤害"}, "drillRun": {"name": "直冲钻", "effect": "像钢钻一样，\n一边旋转身体一边撞击对手。\n容易击中要害"}, "dualChop": {"name": "二连劈", "effect": "用身体坚硬的部分拍打对手进行攻击。\n连续２次给予伤害"}, "heartStamp": {"name": "爱心印章", "effect": "以可爱的动作使对手疏忽，\n乘机给出强烈的一击。有时会使对手畏缩"}, "hornLeech": {"name": "木角", "effect": "将角刺入，吸取对手的养分。\n可以回复给予对手伤害的一半ＨＰ"}, "sacredSword": {"name": "圣剑", "effect": "用剑切斩对手进行攻击。\n无视对手的能力变化，直接给予伤害"}, "razorShell": {"name": "贝壳刃", "effect": "用锋利的贝壳切斩对手进行攻击。\n有时会降低对手的防御"}, "heatCrash": {"name": "高温重压", "effect": "用燃烧的身体撞向对手进行攻击。\n自己比对手越重，威力越大"}, "leafTornado": {"name": "青草搅拌器", "effect": "用锋利的叶片包裹住对手进行攻击。\n有时会降低对手的命中率"}, "steamroller": {"name": "疯狂滚压", "effect": "旋转揉成团的身体压扁对手。\n有时会使对手畏缩"}, "cottonGuard": {"name": "棉花防守", "effect": "用软绵绵的绒毛包裹住自己的身体\n进行守护。巨幅提高自己的防御"}, "nightDaze": {"name": "暗黑爆破", "effect": "放出黑暗的冲击波攻击对手。\n有时会降低对手的命中率"}, "psystrike": {"name": "精神击破", "effect": "将神奇的念波实体化攻击对手。\n给予物理伤害"}, "tailSlap": {"name": "扫尾拍打", "effect": "用坚硬的尾巴拍打对手进行攻击。\n连续攻击２～５次"}, "hurricane": {"name": "暴风", "effect": "用强烈的风席卷对手进行攻击。\n有时会使对手混乱"}, "headCharge": {"name": "爆炸头突击", "effect": "用厉害的爆炸头猛撞向对手进行攻击。\n自己也会受到少许伤害"}, "gearGrind": {"name": "齿轮飞盘", "effect": "向对手投掷钢铁齿轮进行攻击。\n连续２次给予伤害"}, "searingShot": {"name": "火焰弹", "effect": "用熊熊烈火攻击自己周围所有的宝可梦。\n有时会陷入灼伤状态"}, "technoBlast": {"name": "高科技光炮", "effect": "向对手放出光弹。属性会根据自己\n携带的卡带不同而改变"}, "relicSong": {"name": "古老之歌", "effect": "让对手听古老之歌，打动对手的内\n心进行攻击。有时会让对手陷入睡眠状态"}, "secretSword": {"name": "神秘之剑", "effect": "用长角切斩对手进行攻击。\n角上拥有的神奇力量将给予物理伤害"}, "glaciate": {"name": "冰封世界", "effect": "将冰冻的冷气吹向对手进行攻击。\n会降低对手的速度"}, "boltStrike": {"name": "雷击", "effect": "让强大的电流覆盖全身，\n猛撞向对手进行攻击。\n有时会让对手陷入麻痹状态"}, "blueFlare": {"name": "青焰", "effect": "用美丽而激烈的青焰\n包裹住对手进行攻击。\n有时会让对手陷入灼伤状态"}, "fieryDance": {"name": "火之舞", "effect": "让火焰覆盖全身，振翅攻击对手。\n有时会提高自己的特攻"}, "freezeShock": {"name": "冰冻伏特", "effect": "用覆盖着电流的冰块，\n在第２回合撞向对手。\n有时会让对手陷入麻痹状态"}, "iceBurn": {"name": "极寒冷焰", "effect": "用能够冻结一切的强烈冷气，\n在第２回合包裹住对手。有时会让对手\n陷入灼伤状态"}, "snarl": {"name": "大声咆哮", "effect": "没完没了地大声斥责，\n从而降低对手的特攻"}, "icicleCrash": {"name": "冰柱坠击", "effect": "用大冰柱激烈地撞向对手进行攻击。\n有时会使对手畏缩"}, "vCreate": {"name": "Ｖ热焰", "effect": "从前额产生灼热的火焰，\n舍身撞击对手。防御、特防和速度会降低"}, "fusionFlare": {"name": "交错火焰", "effect": "释放出巨大的火焰。受到巨大的闪\n电影响时，招式威力会提高"}, "fusionBolt": {"name": "交错闪电", "effect": "释放出巨大的闪电。受到巨大的火\n焰影响时，招式威力会提高"}, "flyingPress": {"name": "飞身重压", "effect": "从空中俯冲向对手。此招式同时带\n有格斗属性和飞行属性"}, "matBlock": {"name": "掀榻榻米", "effect": "将掀起来的榻榻米当作盾牌，\n防住自己和同伴免受招式伤害。\n变化招式无法防住"}, "belch": {"name": "打嗝", "effect": "朝着对手打嗝，并给予伤害。\n如果不吃树果则无法使出"}, "rototiller": {"name": "耕地", "effect": "翻耕土地，使草木更容易成长。\n会提高草属性宝可梦的攻击和特攻"}, "stickyWeb": {"name": "黏黏网", "effect": "在对手周围围上黏黏的网，\n降低替换出场的对手的速度"}, "fellStinger": {"name": "致命针刺", "effect": "如果使用此招式打倒对手，\n攻击会巨幅提高"}, "phantomForce": {"name": "潜灵奇袭", "effect": "第１回合消失在某处，\n第２回合攻击对手。\n可以无视守护进行攻击"}, "trickOrTreat": {"name": "万圣夜", "effect": "邀请对手参加万圣夜。\n使对手被追加幽灵属性"}, "nobleRoar": {"name": "战吼", "effect": "发出战吼威吓对手，从而降低对手\n的攻击和特攻"}, "ionDeluge": {"name": "等离子浴", "effect": "将带电粒子扩散开来，\n使一般属性的招式变成电属性"}, "parabolicCharge": {"name": "抛物面充电", "effect": "给周围全体宝可梦造成伤害。\n可以回复给予伤害的一半HP"}, "forestsCurse": {"name": "森林咒术", "effect": "向对手施加森林咒术。\n中了咒术的对手会被追加草属性"}, "petalBlizzard": {"name": "落英缤纷", "effect": "猛烈地刮起飞雪般的落花，\n攻击周围所有的宝可梦，并给予伤害"}, "freezeDry": {"name": "冷冻干燥", "effect": "急剧冷冻对手，有时会让对手陷入\n冰冻状态。对于水属性宝可梦也是\n效果绝佳"}, "disarmingVoice": {"name": "魅惑之声", "effect": "发出魅惑的叫声，\n给予对手精神上的伤害。\n攻击必定会命中"}, "partingShot": {"name": "抛下狠话", "effect": "抛下狠话威吓对手，降低攻击和特攻后，\n和后备宝可梦进行替换"}, "topsyTurvy": {"name": "颠倒", "effect": "颠倒对手身上的所有能力变化，\n变成和原来相反的状态"}, "drainingKiss": {"name": "吸取之吻", "effect": "用一个吻吸取对手的ＨＰ。\n回复给予对手伤害的一半以上的ＨＰ"}, "craftyShield": {"name": "戏法防守", "effect": "使用神奇的力量防住\n攻击我方的变化招式。\n但无法防住伤害招式的攻击"}, "flowerShield": {"name": "鲜花防守", "effect": "使用神奇的力量提高在场的所有草\n属性宝可梦的防御"}, "grassyTerrain": {"name": "青草场地", "effect": "在５回合内变成青草场地。\n地面上的宝可梦每回合都能回复。\n草属性的招式威力还会提高"}, "mistyTerrain": {"name": "薄雾场地", "effect": "在５回合内，地面上的宝可梦不会\n陷入异常状态。龙属性招式的伤害\n也会减半"}, "electrify": {"name": "输电", "effect": "对手使出招式前，如果输电，\n则该回合对手的招式变成电属性"}, "playRough": {"name": "嬉闹", "effect": "与对手嬉闹并攻击。有时会降低对\n手的攻击"}, "fairyWind": {"name": "妖精之风", "effect": "刮起妖精之风，吹向对手进行攻击"}, "moonblast": {"name": "月亮之力", "effect": "借用月亮的力量攻击对手。\n有时会降低对手的特攻"}, "boomburst": {"name": "爆音波", "effect": "通过震耳欲聋的爆炸声产生的破坏力，\n攻击自己周围所有的宝可梦"}, "fairyLock": {"name": "妖精之锁", "effect": "通过封锁，下一回合所有的宝可梦\n都无法逃走"}, "kingsShield": {"name": "王者盾牌", "effect": "防住对手攻击的同时，\n自己变为防御姿态。\n能够降低所接触到的对手的攻击"}, "playNice": {"name": "和睦相处", "effect": "和对手和睦相处，使其失去战斗的气力，\n从而降低对手的攻击"}, "confide": {"name": "密语", "effect": "和对手进行密语，使其失去集中力，\n从而降低对手的特攻"}, "diamondStorm": {"name": "钻石风暴", "effect": "掀起钻石风暴给予伤害。\n有时会大幅提高自己的防御"}, "steamEruption": {"name": "蒸汽爆炸", "effect": "将滚烫的蒸汽喷向对手。\n有时会让对手灼伤"}, "hyperspaceHole": {"name": "异次元洞", "effect": "通过异次元洞，突然出现在对手的\n侧面进行攻击。还可以无视守住和\n看穿等招式"}, "waterShuriken": {"name": "飞水手里剑", "effect": "用粘液制成的手里剑，\n连续攻击２～５次。必定能够先制攻击"}, "mysticalFire": {"name": "魔法火焰", "effect": "从口中喷出特别灼热的火焰进行攻击。\n降低对手的特攻"}, "spikyShield": {"name": "尖刺防守", "effect": "防住对手攻击的同时，\n削减接触到自己的对手的体力"}, "aromaticMist": {"name": "芳香薄雾", "effect": "通过神奇的芳香，提高我方宝可梦的特防"}, "eerieImpulse": {"name": "怪异电波", "effect": "从身体放射出怪异电波，\n让对手沐浴其中，从而大幅降低其特攻"}, "venomDrench": {"name": "毒液陷阱", "effect": "将特殊的毒液泼向对手。\n对处于中毒状态的对手，其攻击、\n特攻和速度都会降低"}, "powder": {"name": "粉尘", "effect": "如果被撒到粉尘的对手使用火招式，\n则会爆炸并给予伤害"}, "geomancy": {"name": "大地掌控", "effect": "第１回合吸收能量，第２回合大幅\n提高特攻、特防和速度"}, "magneticFlux": {"name": "磁场操控", "effect": "通过操控磁场，会提高特性为正电\n和负电的宝可梦的防御和特防"}, "happyHour": {"name": "欢乐时光", "effect": "如果使用欢乐时光，战斗后得到的\n钱会翻倍"}, "electricTerrain": {"name": "电气场地", "effect": "在５回合内变成电气场地。\n地面上的宝可梦将无法入眠。\n电属性的招式威力还会提高"}, "dazzlingGleam": {"name": "魔法闪耀", "effect": "向对手发射强光，并给予伤害"}, "celebrate": {"name": "庆祝", "effect": "宝可梦为十分开心的你庆祝"}, "holdHands": {"name": "牵手", "effect": "我方宝可梦之间牵手。\n能带来非常幸福的心情"}, "babyDollEyes": {"name": "圆瞳", "effect": "用圆瞳凝视对手，从而降低其攻击。\n必定能够先制攻击"}, "nuzzle": {"name": "蹭蹭脸颊", "effect": "将带电的脸颊蹭蹭对手进行攻击。\n让对手陷入麻痹状态"}, "holdBack": {"name": "手下留情", "effect": "在攻击的时候手下留情，\n从而使对手的ＨＰ至少会留下１ＨＰ"}, "infestation": {"name": "纠缠不休", "effect": "在４～５回合内死缠烂打地进行攻击。\n在此期间对手将无法逃走"}, "powerUpPunch": {"name": "增强拳", "effect": "通过反复击打对手，使自己的拳头\n慢慢变硬。打中对手攻击就会提高"}, "oblivionWing": {"name": "归天之翼", "effect": "从锁定的对手身上吸取ＨＰ。\n回复给予对手伤害的一半以上的ＨＰ"}, "thousandArrows": {"name": "千箭齐发", "effect": "可以击中浮在空中的宝可梦。\n空中的对手被击落后，会掉到地面"}, "thousandWaves": {"name": "千波激荡", "effect": "从地面掀起波浪进行攻击。\n被掀入波浪中的对手，\n将无法从战斗中逃走"}, "landsWrath": {"name": "大地神力", "effect": "聚集大地的力量，\n将此力量集中攻击对手，\n并给予伤害"}, "lightOfRuin": {"name": "破灭之光", "effect": "借用永恒之花的力量，\n发射出强力光线。\n自己也会受到非常大的伤害"}, "originPulse": {"name": "根源波动", "effect": "用无数青白色且闪耀的光线攻击对手"}, "precipiceBlades": {"name": "断崖之剑", "effect": "将大地的力量变化为利刃攻击对手"}, "dragonAscent": {"name": "画龙点睛", "effect": "从天空中急速下降攻击对手。\n自己的防御和特防会降低"}, "hyperspaceFury": {"name": "异次元猛攻", "effect": "用许多手臂，无视对手的守住或看\n穿等招式进行连续攻击，\n自己的防御会降低"}, "breakneckBlitzPhysical": {"name": "一般Ｚ究极无敌大冲撞", "effect": "通过Ｚ力量气势猛烈地全力撞上对手。\n威力会根据原来的招式而改变"}, "breakneckBlitzSpecial": {"name": "一般Ｚ究极无敌大冲撞", "effect": "通过Ｚ力量气势猛烈地全力撞上对手。\n威力会根据原来的招式而改变"}, "allOutPummelingPhysical": {"name": "格斗Ｚ全力无双激烈拳", "effect": "通过Ｚ力量制造出能量弹，\n全力撞向对手。\n威力会根据原来的招式而改变"}, "allOutPummelingSpecial": {"name": "格斗Ｚ全力无双激烈拳", "effect": "通过Ｚ力量制造出能量弹，\n全力撞向对手。\n威力会根据原来的招式而改变"}, "supersonicSkystrikePhysical": {"name": "飞行Ｚ极速俯冲轰烈撞", "effect": "通过Ｚ力量猛烈地飞向天空，\n朝对手全力落下。\n威力会根据原来的招式而改变"}, "supersonicSkystrikeSpecial": {"name": "飞行Ｚ极速俯冲轰烈撞", "effect": "通过Ｚ力量猛烈地飞向天空，\n朝对手全力落下。\n威力会根据原来的招式而改变"}, "acidDownpourPhysical": {"name": "毒Ｚ强酸剧毒灭绝雨", "effect": "通过Ｚ力量使毒沼涌起，\n全力让对手沉下去。\n威力会根据原来的招式而改变"}, "acidDownpourSpecial": {"name": "毒Ｚ强酸剧毒灭绝雨", "effect": "通过Ｚ力量使毒沼涌起，\n全力让对手沉下去。\n威力会根据原来的招式而改变"}, "tectonicRagePhysical": {"name": "地面Ｚ地隆啸天大终结", "effect": "通过Ｚ力量潜入地里最深处，\n全力撞上对手。\n威力会根据原来的招式而改变"}, "tectonicRageSpecial": {"name": "地面Ｚ地隆啸天大终结", "effect": "通过Ｚ力量潜入地里最深处，\n全力撞上对手。\n威力会根据原来的招式而改变"}, "continentalCrushPhysical": {"name": "岩石Ｚ毁天灭地巨岩坠", "effect": "通过Ｚ力量召唤大大的岩山，\n全力撞向对手。\n威力会根据原来的招式而改变"}, "continentalCrushSpecial": {"name": "岩石Ｚ毁天灭地巨岩坠", "effect": "通过Ｚ力量召唤大大的岩山，\n全力撞向对手。\n威力会根据原来的招式而改变"}, "savageSpinOutPhysical": {"name": "虫Ｚ绝对捕食回旋斩", "effect": "通过Ｚ力量将吐出的丝线全力束缚对手。\n威力会根据原来的招式而改变"}, "savageSpinOutSpecial": {"name": "虫Ｚ绝对捕食回旋斩", "effect": "通过Ｚ力量将吐出的丝线全力束缚对手。\n威力会根据原来的招式而改变"}, "neverEndingNightmarePhysical": {"name": "幽灵Ｚ无尽暗夜之诱惑", "effect": "通过Ｚ力量召唤强烈的怨念，\n全力降临到对手身上。\n威力会根据原来的招式而改变"}, "neverEndingNightmareSpecial": {"name": "幽灵Ｚ无尽暗夜之诱惑", "effect": "通过Ｚ力量召唤强烈的怨念，\n全力降临到对手身上。\n威力会根据原来的招式而改变"}, "corkscrewCrashPhysical": {"name": "钢Ｚ超绝螺旋连击", "effect": "通过Ｚ力量进行高速旋转，\n全力撞上对手。\n威力会根据原来的招式而改变"}, "corkscrewCrashSpecial": {"name": "钢Ｚ超绝螺旋连击", "effect": "通过Ｚ力量进行高速旋转，\n全力撞上对手。\n威力会根据原来的招式而改变"}, "infernoOverdrivePhysical": {"name": "火Ｚ超强极限爆焰弹", "effect": "通过Ｚ力量喷出熊熊烈火，\n全力撞向对手。\n威力会根据原来的招式而改变"}, "infernoOverdriveSpecial": {"name": "火Ｚ超强极限爆焰弹", "effect": "通过Ｚ力量喷出熊熊烈火，\n全力撞向对手。\n威力会根据原来的招式而改变"}, "hydroVortexPhysical": {"name": "水Ｚ超级水流大漩涡", "effect": "通过Ｚ力量制造大大的潮旋，\n全力吞没对手。\n威力会根据原来的招式而改变"}, "hydroVortexSpecial": {"name": "水Ｚ超级水流大漩涡", "effect": "通过Ｚ力量制造大大的潮旋，\n全力吞没对手。\n威力会根据原来的招式而改变"}, "bloomDoomPhysical": {"name": "草Ｚ绚烂缤纷花怒放", "effect": "通过Ｚ力量借助花草的能量，\n全力攻击对手。\n威力会根据原来的招式而改变"}, "bloomDoomSpecial": {"name": "草Ｚ绚烂缤纷花怒放", "effect": "通过Ｚ力量借助花草的能量，\n全力攻击对手。\n威力会根据原来的招式而改变"}, "gigavoltHavocPhysical": {"name": "电Ｚ终极伏特狂雷闪", "effect": "通过Ｚ力量将蓄积的强大电流全力\n撞向对手。威力会根据原来的招式而改变"}, "gigavoltHavocSpecial": {"name": "电Ｚ终极伏特狂雷闪", "effect": "通过Ｚ力量将蓄积的强大电流全力\n撞向对手。威力会根据原来的招式而改变"}, "shatteredPsychePhysical": {"name": "超能力Ｚ至高精神破坏波", "effect": "通过Ｚ力量操纵对手，\n全力使其感受到痛苦。\n威力会根据原来的招式而改变"}, "shatteredPsycheSpecial": {"name": "超能力Ｚ至高精神破坏波", "effect": "通过Ｚ力量操纵对手，\n全力使其感受到痛苦。\n威力会根据原来的招式而改变"}, "subzeroSlammerPhysical": {"name": "冰Ｚ激狂大地万里冰", "effect": "通过Ｚ力量急剧降低气温，\n全力冰冻对手。\n威力会根据原来的招式而改变"}, "subzeroSlammerSpecial": {"name": "冰Ｚ激狂大地万里冰", "effect": "通过Ｚ力量急剧降低气温，\n全力冰冻对手。\n威力会根据原来的招式而改变"}, "devastatingDrakePhysical": {"name": "龙Ｚ究极巨龙震天地", "effect": "通过Ｚ力量将气场实体化，\n向对手全力发动袭击。\n威力会根据原来的招式而改变"}, "devastatingDrakeSpecial": {"name": "龙Ｚ究极巨龙震天地", "effect": "通过Ｚ力量将气场实体化，\n向对手全力发动袭击。\n威力会根据原来的招式而改变"}, "blackHoleEclipsePhysical": {"name": "恶Ｚ黑洞吞噬万物灭", "effect": "通过Ｚ力量收集恶能量，\n全力将对手吸入。\n威力会根据原来的招式而改变"}, "blackHoleEclipseSpecial": {"name": "恶Ｚ黑洞吞噬万物灭", "effect": "通过Ｚ力量收集恶能量，\n全力将对手吸入。\n威力会根据原来的招式而改变"}, "twinkleTacklePhysical": {"name": "妖精Ｚ可爱星星飞天撞", "effect": "通过Ｚ力量制造魅惑空间，\n全力捉弄对手。\n威力会根据原来的招式而改变"}, "twinkleTackleSpecial": {"name": "妖精Ｚ可爱星星飞天撞", "effect": "通过Ｚ力量制造魅惑空间，\n全力捉弄对手。\n威力会根据原来的招式而改变"}, "catastropika": {"name": "皮卡丘Ｚ皮卡皮卡必杀击", "effect": "通过Ｚ力量，皮卡丘全身覆盖最强电力，\n全力猛扑对手"}, "shoreUp": {"name": "集沙", "effect": "回复自己最大ＨＰ的一半。\n在沙暴中回复得更多"}, "firstImpression": {"name": "迎头一击", "effect": "威力很高的招式，但只有在出场战斗时，\n立刻使出才能成功"}, "banefulBunker": {"name": "碉堡", "effect": "防住对手攻击的同时，\n让接触到自己的对手中毒"}, "spiritShackle": {"name": "缝影", "effect": "攻击的同时，缝住对手的影子，\n使其无法逃走"}, "darkestLariat": {"name": "ＤＤ金勾臂", "effect": "旋转双臂打向对手。\n无视对手的能力变化，\n直接给予伤害"}, "sparklingAria": {"name": "泡影的咏叹调", "effect": "随着唱歌会放出很多气球。\n受到此招式攻击时，灼伤会被治愈"}, "iceHammer": {"name": "冰锤", "effect": "挥舞强力而沉重的拳头，\n给予对手伤害。自己的速度会降低"}, "floralHealing": {"name": "花疗", "effect": "回复对手最大ＨＰ的一半。\n在青草场地时，效果会提高"}, "highHorsepower": {"name": "十万马力", "effect": "使出全身力量，猛攻对手"}, "strengthSap": {"name": "吸取力量", "effect": "给自己回复和对手攻击力\n相同数值的ＨＰ，\n然后降低对手的攻击"}, "solarBlade": {"name": "日光刃", "effect": "第１回合收集满满的日光，\n第２回合将此力量集中在剑上进行攻击"}, "leafage": {"name": "树叶", "effect": "将叶片打向对手，进行攻击"}, "spotlight": {"name": "聚光灯", "effect": "给宝可梦打上聚光灯，\n该回合只能瞄准该宝可梦"}, "toxicThread": {"name": "毒丝", "effect": "将混有毒的丝吐向对手。\n使其中毒，从而降低对手的速度"}, "laserFocus": {"name": "磨砺", "effect": "集中精神，下次攻击必定会击中要害"}, "gearUp": {"name": "辅助齿轮", "effect": "启动齿轮，提高特性为正电和负电\n的宝可梦的攻击和特攻"}, "throatChop": {"name": "深渊突刺", "effect": "受到此招式攻击的对手，\n会因为地狱般的痛苦，在２回合内，\n变得无法使出声音类招式"}, "pollenPuff": {"name": "花粉团", "effect": "对敌人使用是会爆炸的团子。\n对我方使用则是给予回复的团子"}, "anchorShot": {"name": "掷锚", "effect": "将锚缠住对手进行攻击。\n使对手无法逃走"}, "psychicTerrain": {"name": "精神场地", "effect": "在５回合内，地面上的宝可梦不会\n受到先制招式的攻击。\n超能力属性的招式威力会提高"}, "lunge": {"name": "猛扑", "effect": "全力猛扑对手进行攻击。\n从而降低对手的攻击"}, "fireLash": {"name": "火焰鞭", "effect": "用燃烧的鞭子抽打对手。\n受到攻击的对手防御会降低"}, "powerTrip": {"name": "嚣张", "effect": "耀武扬威地攻击对手，\n自己的能力提高得越多，威力就越大"}, "burnUp": {"name": "燃尽", "effect": "将自己全身燃烧起火焰来，\n给予对手大大的伤害。\n自己的火属性将会消失"}, "speedSwap": {"name": "速度互换", "effect": "将对手和自己的速度进行互换"}, "smartStrike": {"name": "修长之角", "effect": "用尖尖的角刺入对手进行攻击。\n攻击必定会命中"}, "purify": {"name": "净化", "effect": "治愈对手的异常状态。\n治愈后可以回复自己的ＨＰ"}, "revelationDance": {"name": "觉醒之舞", "effect": "全力跳舞进行攻击。此招式的属性\n将变得和自己的属性相同"}, "coreEnforcer": {"name": "核心惩罚者", "effect": "如果给予过伤害的对手已经结束行动，\n其特性就会被消除"}, "tropKick": {"name": "热带踢", "effect": "向对手使出来自南国的火热脚踢。\n从而降低对手的攻击"}, "instruct": {"name": "号令", "effect": "向对手下达指示，让其再次使出刚\n才的招式"}, "beakBlast": {"name": "鸟嘴加农炮", "effect": "先加热鸟嘴后再进行攻击。\n鸟嘴在加热时对手触碰的话，\n就会使其灼伤"}, "clangingScales": {"name": "鳞片噪音", "effect": "摩擦全身鳞片，\n发出响亮的声音进行攻击。\n攻击后自己的防御会降低"}, "dragonHammer": {"name": "龙锤", "effect": "将身体当作锤子，向对手发动袭击，\n给予伤害"}, "brutalSwing": {"name": "狂舞挥打", "effect": "用自己的身体狂舞挥打，\n给予对手伤害"}, "auroraVeil": {"name": "极光幕", "effect": "在５回合内减弱物理和特殊的伤害。\n只有下雪时才能使出"}, "sinisterArrowRaid": {"name": "狙射树枭Ｚ遮天蔽日暗影箭", "effect": "通过Ｚ力量制造出无数箭的狙射树\n枭将全力射穿对手进行攻击"}, "maliciousMoonsault": {"name": "炽焰咆哮虎Ｚ极恶飞跃粉碎击", "effect": "通过Ｚ力量得到强壮肉体的炽焰咆\n哮虎将全力撞向对手进行攻击"}, "oceanicOperetta": {"name": "西狮海壬Ｚ海神庄严交响乐", "effect": "通过Ｚ力量召唤大量水的西狮海壬\n将全力攻击对手"}, "guardianOfAlola": {"name": "卡璞Ｚ巨人卫士・阿罗拉", "effect": "通过Ｚ力量得到阿罗拉之力的土地\n神宝可梦将全力进行攻击。\n对手的剩余HP会减少很多"}, "soulStealing7StarStrike": {"name": "玛夏多Ｚ七星夺魂腿", "effect": "得到Ｚ力量的玛夏多将全力使出拳\n头和脚踢的连续招式叩打对手"}, "stokedSparksurfer": {"name": "阿罗雷Ｚ驾雷驭电戏冲浪", "effect": "得到Ｚ力量的阿罗拉地区的雷丘将\n全力进行攻击。从而让对手陷入麻痹状态"}, "pulverizingPancake": {"name": "卡比兽Ｚ认真起来大爆击", "effect": "通过Ｚ力量使得认真起来的卡比兽\n跃动巨大身躯，全力向对手发动袭击"}, "extremeEvoboost": {"name": "伊布Ｚ九彩昇华齐聚顶", "effect": "得到Ｚ力量的伊布将借助进化后伙\n伴们的力量，大幅提高能力"}, "genesisSupernova": {"name": "梦幻Ｚ起源超新星大爆炸", "effect": "得到Ｚ力量的梦幻将全力攻击对手。\n脚下会变成精神场地"}, "shellTrap": {"name": "陷阱甲壳", "effect": "设下甲壳陷阱。如果对手使出物理招式，\n陷阱就会爆炸并给予对手伤害"}, "fleurCannon": {"name": "花朵加农炮", "effect": "放出强力光束后，自己的特攻会大幅降低"}, "psychicFangs": {"name": "精神之牙", "effect": "利用精神力量咬住对手进行攻击。\n还可以破坏光墙和反射壁等"}, "stompingTantrum": {"name": "跺脚", "effect": "化悔恨为力量进行攻击。\n如果上一回合招式没有打中，\n威力就会翻倍"}, "shadowBone": {"name": "暗影之骨", "effect": "用附有灵魂的骨头殴打对手进行攻击。\n有时会降低对手的防御"}, "accelerock": {"name": "冲岩", "effect": "迅速撞向对手进行攻击。\n必定能够先制攻击"}, "liquidation": {"name": "水流裂破", "effect": "用水之力量撞向对手进行攻击。\n有时会降低对手的防御"}, "prismaticLaser": {"name": "棱镜镭射", "effect": "用棱镜的力量发射强烈光线。\n下一回合自己将无法动弹"}, "spectralThief": {"name": "暗影偷盗", "effect": "潜入对手的影子进行攻击。\n会夺取对手的能力提升"}, "sunsteelStrike": {"name": "流星闪冲", "effect": "以流星般的气势猛撞对手。\n可以无视对手的特性进行攻击"}, "moongeistBeam": {"name": "暗影之光", "effect": "放出奇怪的光线攻击对手。\n可以无视对手的特性进行攻击"}, "tearfulLook": {"name": "泪眼汪汪", "effect": "变得泪眼汪汪，让对手丧失斗志。\n从而降低对手的攻击和特攻"}, "zingZap": {"name": "麻麻刺刺", "effect": "撞向对手，并发出强电，\n使其感到麻麻刺刺的。有时会使对手畏缩"}, "naturesMadness": {"name": "自然之怒", "effect": "向对手释放自然之怒。\n对手的ＨＰ会减半"}, "multiAttack": {"name": "多属性攻击", "effect": "一边覆盖高能量，\n一边撞向对手进行攻击。\n根据存储碟不同，\n属性会改变"}, "tenMillionVoltThunderbolt": {"name": "智皮卡Ｚ千万伏特", "effect": "戴着帽子的皮卡丘将通过Ｚ力量增\n强的电击全力释放给对手。\n容易击中要害"}, "mindBlown": {"name": "惊爆大头", "effect": "让自己的头爆炸，来攻击周围的一切。\n自己也会受到伤害"}, "plasmaFists": {"name": "等离子闪电拳", "effect": "用覆盖着电流的拳头进行攻击。\n使一般属性的招式变成电属性"}, "photonGeyser": {"name": "光子喷涌", "effect": "用光柱来进行攻击。比较自己的攻\n击和特攻，用数值相对较高的一项\n给予对方伤害"}, "lightThatBurnsTheSky": {"name": "究极奈克洛Ｚ焚天灭世炽光爆", "effect": "奈克洛兹玛会无视对手的特性效果，\n在攻击和特攻之间，\n用数值相对较高的一项给予对方伤害"}, "searingSunrazeSmash": {"name": "索尔迦雷欧Ｚ日光回旋下苍穹", "effect": "得到Ｚ力量的索尔迦雷欧\n将全力进行攻击。\n可以无视对手的特性效果"}, "menacingMoonrazeMaelstrom": {"name": "露奈雅拉Ｚ月华飞溅落灵霄", "effect": "得到Ｚ力量的露奈雅拉将全力进行攻击。\n可以无视对手的特性效果"}, "letsSnuggleForever": {"name": "谜拟丘Ｚ亲密无间大乱揍", "effect": "得到Ｚ力量的谜拟Ｑ将全力进行乱揍攻击"}, "splinteredStormshards": {"name": "鬃岩狼人Ｚ狼啸石牙飓风暴", "effect": "得到Ｚ力量的鬃岩狼人将全力进行攻击。\n而且会消除场地状态"}, "clangorousSoulblaze": {"name": "杖尾鳞甲龙Ｚ炽魂热舞烈音爆", "effect": "得到Ｚ力量的杖尾鳞甲龙\n将全力攻击对手。\n并且自己的能力会提高"}, "zippyZap": {"name": "电电加速", "effect": "迅猛无比的电击。必定能够先制攻击，击中对方的要害。"}, "splishySplash": {"name": "滔滔冲浪", "effect": "往巨浪中注入电能后冲撞对手进行攻击。\n有时会让对手陷入麻痹状态"}, "floatyFall": {"name": "飘飘坠落", "effect": "轻飘飘地浮起来后，再猛地俯冲下\n去进行攻击。有时会使对手畏缩"}, "pikaPapow": {"name": "闪闪雷光", "effect": "皮卡丘越喜欢训练家，\n电击的威力就越强。攻击必定会命中"}, "bouncyBubble": {"name": "活活气泡", "effect": "投掷水球进行攻击。吸水后能回复\n等同于造成的伤害的HP"}, "buzzyBuzz": {"name": "麻麻电击", "effect": "放出电击攻击对手。让对手陷入麻痹状态"}, "sizzlySlide": {"name": "熊熊火爆", "effect": "用燃起大火的身体猛烈地冲撞对手。\n让对手陷入灼伤状态"}, "glitzyGlow": {"name": "哗哗气场", "effect": "利用念力强攻，粉碎对方信心。\n制造一道能减弱对手特殊攻击的神奇墙壁"}, "baddyBad": {"name": "坏坏领域", "effect": "恶行恶相地进行攻击。\n制造一道能减弱对手物理攻击的神奇墙壁"}, "sappySeed": {"name": "茁茁炸弹", "effect": "长出巨大的藤蔓，播撒种子进行攻击。\n种子每回合都会吸取对手的HP"}, "freezyFrost": {"name": "冰冰霜冻", "effect": "利用冰冷的黑雾结晶进行攻击。\n使全体宝可梦的能力变回原点"}, "sparklySwirl": {"name": "亮亮风暴", "effect": "利用芬芳刺鼻的龙卷风吞噬对方。\n能治愈我方宝可梦的异常状态"}, "veeveeVolley": {"name": "砰砰击破", "effect": "伊布越喜欢训练家，冲撞的威力就越强。\n攻击必定会命中"}, "doubleIronBash": {"name": "钢拳双击", "effect": "以胸口的螺帽为中心旋转，\n并连续２次挥动手臂打击对手。\n有时会使对手畏缩"}, "maxGuard": {"name": "极巨防壁", "effect": "完全抵挡对手的攻击。\n连续使出则容易失败"}, "dynamaxCannon": {"name": "极巨炮", "effect": "将凝缩在体内的能量从核心放出进行攻击，\n对手等级比当前波次的等级上限越高，造成的伤害越高，最多两倍。"}, "snipeShot": {"name": "狙击", "effect": "能无视具有吸引对手招式效果的特\n性或招式的影响。可以向选定的对\n手进行攻击"}, "jawLock": {"name": "紧咬不放", "effect": "使双方直到一方昏厥为止\n无法替换宝可梦。\n其中一方退场则可以解除效果"}, "stuffCheeks": {"name": "大快朵颐", "effect": "吃掉携带的树果，大幅提高防御"}, "noRetreat": {"name": "背水一战", "effect": "提高自己的所有能力，\n但无法替换或逃走"}, "tarShot": {"name": "沥青射击", "effect": "泼洒黏糊糊的沥青，降低对手的速度。\n火属性会变成对手的弱点"}, "magicPowder": {"name": "魔法粉", "effect": "向对手喷洒魔法粉，使对手变为超\n能力属性"}, "dragonDarts": {"name": "龙箭", "effect": "让多龙梅西亚进行２次攻击。\n如果对手有２只宝可梦，则对它们各进\n行１次攻击"}, "teatime": {"name": "茶会", "effect": "举办一场茶会，场上的所有宝可梦\n都会吃掉自己携带的树果"}, "octolock": {"name": "蛸固", "effect": "让对手无法逃走。对手被固定后，\n每回合都会降低防御和特防"}, "boltBeak": {"name": "电喙", "effect": "用带电的喙啄刺对手。\n如果比对手先出手攻击，\n招式的威力会变成２倍"}, "fishiousRend": {"name": "鳃咬", "effect": "用坚硬的腮咬住对手。\n如果比对手先出手攻击，\n招式的威力会变成２倍"}, "courtChange": {"name": "换场", "effect": "用神奇的力量交换双方的场地效果"}, "maxFlare": {"name": "极巨火爆", "effect": "极巨化宝可梦使出的火属性攻击。\n可在５回合内让日照变得强烈"}, "maxFlutterby": {"name": "极巨虫蛊", "effect": "极巨化宝可梦使出的虫属性攻击。\n会降低对手的特攻"}, "maxLightning": {"name": "极巨闪电", "effect": "极巨化宝可梦使出的电属性攻击。\n可在５回合内将脚下变成电气场地"}, "maxStrike": {"name": "极巨攻击", "effect": "极巨化宝可梦使出的一般属性攻击。\n会降低对手的速度"}, "maxKnuckle": {"name": "极巨拳斗", "effect": "极巨化宝可梦使出的格斗属性攻击。\n会提高我方的攻击"}, "maxPhantasm": {"name": "极巨幽魂", "effect": "极巨化宝可梦使出的幽灵属性攻击。\n会降低对手的防御"}, "maxHailstorm": {"name": "极巨寒冰", "effect": "极巨化宝可梦使出的冰属性攻击。\n在５回合内会下雪"}, "maxOoze": {"name": "极巨酸毒", "effect": "极巨化宝可梦使出的毒属性攻击。\n会提高我方的特攻"}, "maxGeyser": {"name": "极巨水流", "effect": "极巨化宝可梦使出的水属性攻击。\n可在５回合内降下大雨"}, "maxAirstream": {"name": "极巨飞冲", "effect": "极巨化宝可梦使出的飞行属性攻击。\n会提高我方的速度"}, "maxStarfall": {"name": "极巨妖精", "effect": "极巨化宝可梦使出的妖精属性攻击。\n可在５回合内将脚下变成薄雾场地"}, "maxWyrmwind": {"name": "极巨龙骑", "effect": "极巨化宝可梦使出的龙属性攻击。\n会降低对手的攻击"}, "maxMindstorm": {"name": "极巨超能", "effect": "极巨化宝可梦使出的超能力属性攻击。\n可在５回合内将脚下变成精神场地"}, "maxRockfall": {"name": "极巨岩石", "effect": "极巨化宝可梦使出的岩石属性攻击。\n可在５回合内卷起沙暴"}, "maxQuake": {"name": "极巨大地", "effect": "极巨化宝可梦使出的地面属性攻击。\n会提高我方的特防"}, "maxDarkness": {"name": "极巨恶霸", "effect": "极巨化宝可梦使出的恶属性攻击。\n会降低对手的特防"}, "maxOvergrowth": {"name": "极巨草原", "effect": "极巨化宝可梦使出的草属性攻击。\n可在５回合内将脚下变成青草场地"}, "maxSteelspike": {"name": "极巨钢铁", "effect": "极巨化宝可梦使出的钢属性攻击。\n会提高我方的防御"}, "clangorousSoul": {"name": "魂舞烈音爆", "effect": "削减少许自己的ＨＰ，\n使所有能力都提高"}, "bodyPress": {"name": "扑击", "effect": "用身体撞向对手进行攻击。\n防御越高，给予的伤害就越高"}, "decorate": {"name": "装饰", "effect": "通过装饰，大幅提高对方的攻击和特攻"}, "drumBeating": {"name": "鼓击", "effect": "用鼓点来控制鼓的根部进行攻击，\n从而降低对手的速度"}, "snapTrap": {"name": "捕兽夹", "effect": "使用捕兽夹，在４～５回合内，\n夹住对手进行攻击"}, "pyroBall": {"name": "火焰球", "effect": "点燃小石子，形成火球攻击对手。\n有时会使对手陷入灼伤状态"}, "behemothBlade": {"name": "巨兽斩", "effect": "以全身力气举起强大的剑，\n猛烈地劈向对手进行攻击"}, "behemothBash": {"name": "巨兽弹", "effect": "将全身变化为坚固的盾，\n猛烈地撞向对手进行攻击"}, "auraWheel": {"name": "气场轮", "effect": "用储存在颊囊里的能量进行攻击，\n并提高自己的速度。如果由莫鲁贝可使用，\n其属性会随着它的样子而改变"}, "breakingSwipe": {"name": "广域破坏", "effect": "用坚韧的尾巴猛扫对手进行攻击，\n从而降低对手的攻击"}, "branchPoke": {"name": "木枝突刺", "effect": "使用尖锐的树枝刺向对手进行攻击"}, "overdrive": {"name": "破音", "effect": "奏响吉他和贝斯，释放出发出巨响\n的剧烈震动攻击对手"}, "appleAcid": {"name": "苹果酸", "effect": "使用从酸苹果中提取出来的酸性液\n体进行攻击。降低对手的特防"}, "gravApple": {"name": "万有引力", "effect": "从高处落下苹果，给予对手伤害。\n可降低对手的防御"}, "spiritBreak": {"name": "灵魂冲击", "effect": "用足以让对手一蹶不振的气势进行攻击。\n会降低对手的特攻"}, "strangeSteam": {"name": "神奇蒸汽", "effect": "喷出烟雾攻击对手。有时会使对手混乱"}, "lifeDew": {"name": "生命水滴", "effect": "喷洒出神奇的水，回复自己和场上\n同伴的ＨＰ"}, "obstruct": {"name": "拦堵", "effect": "完全抵挡对手的攻击。\n连续使出则容易失败。一旦触碰，\n防御就会大幅降低"}, "falseSurrender": {"name": "假跪真撞", "effect": "装作低头认错的样子，\n用凌乱的头发进行突刺。攻击必定会命中"}, "meteorAssault": {"name": "流星突击", "effect": "大力挥舞粗壮的茎进行攻击。\n但同时自己也会被晃晕，下一回合自己\n将无法动弹"}, "eternabeam": {"name": "无极光束", "effect": "无极汰那变回原来的样子后，\n发动的最强攻击。\n下一回合自己将无法动弹"}, "steelBeam": {"name": "铁蹄光线", "effect": "将从全身聚集的钢铁化为光束，\n激烈地发射出去。自己也会受到伤害"}, "expandingForce": {"name": "广域战力", "effect": "利用精神力量攻击对手。\n在精神场地上威力会有所提高，\n能对所有对手造成伤害"}, "steelRoller": {"name": "铁滚轮", "effect": "在破坏场地的同时攻击对手。\n如果脚下没有任何场地状态存在，\n使出此招式时便会失败"}, "scaleShot": {"name": "鳞射", "effect": "发射鳞片进行攻击。连续攻击２～５次。\n速度会提高但防御会降低"}, "meteorBeam": {"name": "流星光束", "effect": "第１回合聚集宇宙之力提高特攻，\n第２回合攻击对手"}, "shellSideArm": {"name": "臂贝武器", "effect": "从物理攻击和特殊攻击中选择可造\n成较多伤害的方式进行攻击。\n有时会让对手陷入中毒状态"}, "mistyExplosion": {"name": "薄雾炸裂", "effect": "对自己周围的所有宝可梦进行攻击，\n但使出后，自己会陷入昏厥。\n在薄雾场地上，招式威力会提高"}, "grassyGlide": {"name": "青草滑梯", "effect": "仿佛在地面上滑行般地攻击对手。\n在青草场地上，必定能够先制攻击"}, "risingVoltage": {"name": "电力上升", "effect": "用从地面升腾而起的电击进行攻击。\n当对手处于电气场地上时，\n招式威力会变成２倍"}, "terrainPulse": {"name": "大地波动", "effect": "借助场地的力量进行攻击。\n视使出招式时场地状态不同，\n招式的属性和威力会有所变化"}, "skitterSmack": {"name": "爬击", "effect": "从对手背后爬近后进行攻击。\n会降低对手的特攻"}, "burningJealousy": {"name": "妒火", "effect": "用嫉妒的能量攻击对手。\n会让在该回合内能力有所提高\n的宝可梦陷入灼伤状态"}, "lashOut": {"name": "泄愤", "effect": "攻击对手以发泄对其感到的恼怒情绪。\n如果在该回合内自身能力遭到降低，\n招式的威力会变成２倍"}, "poltergeist": {"name": "灵骚", "effect": "操纵对手的持有物进行攻击。\n当对手没有携带道具时，使出此招式时\n便会失败"}, "corrosiveGas": {"name": "腐蚀气体", "effect": "用具有强酸性的气体包裹住自己周\n围所有的宝可梦，并融化其所携带的道具"}, "coaching": {"name": "指导", "effect": "通过进行正确合理的指导，\n提高我方全员的攻击和防御"}, "flipTurn": {"name": "快速折返", "effect": "在攻击之后急速返回，\n和后备宝可梦进行替换"}, "tripleAxel": {"name": "三旋击", "effect": "连续３次踢对手进行攻击。\n每踢中一次，威力就会提高"}, "dualWingbeat": {"name": "双翼", "effect": "将翅膀撞向对手进行攻击。\n连续２次给予伤害"}, "scorchingSands": {"name": "热沙大地", "effect": "将滚烫的沙子砸向对手进行攻击。\n有时会让对手陷入灼伤状态"}, "jungleHealing": {"name": "丛林治疗", "effect": "与丛林融为一体，回复自己和场上\n同伴的ＨＰ和状态"}, "wickedBlow": {"name": "暗冥强击", "effect": "将恶之流派修炼至大成的猛烈一击。\n必定会击中要害"}, "surgingStrikes": {"name": "水流连打", "effect": "将水之流派修炼至大成的仿若行云\n流水般的３次连击。必定会击中要害"}, "thunderCage": {"name": "雷电囚笼", "effect": "将对手困在电流四溅的囚笼中，\n在４～５回合内进行攻击"}, "dragonEnergy": {"name": "巨龙威能", "effect": "把生命力转换为力量攻击对手。\n自己的ＨＰ越少，招式的威力越小"}, "freezingGlare": {"name": "冰冷视线", "effect": "从双眼发射精神力量进行攻击。\n有时会让对手陷入冰冻状态"}, "fieryWrath": {"name": "怒火中烧", "effect": "将愤怒转化为火焰般的气场进行攻击。\n有时会使对手畏缩"}, "thunderousKick": {"name": "雷鸣蹴击", "effect": "以雷电般的动作\n戏耍对手的同时使出脚踢。\n可降低对手的防御"}, "glacialLance": {"name": "雪矛", "effect": "向对手投掷掀起暴风雪的冰矛进行攻击"}, "astralBarrage": {"name": "星碎", "effect": "用大量的小灵体向对手发起攻击"}, "eerieSpell": {"name": "诡异咒语", "effect": "用强大的精神力量攻击。\n让对手最后使用的招式减少３ＰＰ"}, "direClaw": {"name": "克命爪", "effect": "以破灭之爪进行攻击。\n有时还会让对手陷入中毒、麻痹、\n睡眠之中的一种状态"}, "psyshieldBash": {"name": "屏障猛攻", "effect": "让意念的能量覆盖全身，\n撞向对手进行攻击。会提高自己的防御"}, "powerShift": {"name": "力量转换", "effect": "将自己的攻击与防御互相交换"}, "stoneAxe": {"name": "岩斧", "effect": "用岩石之斧进行攻击。\n散落的岩石碎片会飘浮在对手周围"}, "springtideStorm": {"name": "阳春风暴", "effect": "用交织着爱与恨的烈风席卷对手\n进行攻击。有时会降低对手的攻击"}, "mysticalPower": {"name": "神秘之力", "effect": "放出不可思议的力量攻击。\n会提高自己的特攻"}, "ragingFury": {"name": "大愤慨", "effect": "在２～３回合内，一边放出火焰，\n一边疯狂乱打。大闹一番后自己会\n陷入混乱"}, "waveCrash": {"name": "波动冲", "effect": "让水覆盖全身后撞向对手。\n自己也会受到不少伤害"}, "chloroblast": {"name": "叶绿爆震", "effect": "将自己的叶绿素凝聚起来后放出去\n进行攻击。自己也会受到伤害"}, "mountainGale": {"name": "冰山风", "effect": "将冰山般巨大的冰块砸向对手进行攻击。\n有时会使对手畏缩"}, "victoryDance": {"name": "胜利之舞", "effect": "激烈地跳起唤来胜利的舞蹈，\n提高自己的攻击、防御和速度"}, "headlongRush": {"name": "突飞猛扑", "effect": "向对手使出灌注了全心全力的撞击。\n自己的防御和特防会降低"}, "barbBarrage": {"name": "毒千针", "effect": "用无数的毒针进行攻击。\n有时还会让对手陷入中毒状态。\n攻击处于中毒状态的对手时，\n威力会变成２倍"}, "esperWing": {"name": "气场之翼", "effect": "用经过气场强化的翅膀撕裂对手。\n容易击中要害。会提高自己的速度"}, "bitterMalice": {"name": "冤冤相报", "effect": "用令人毛骨悚然的怨念进行攻击。\n会降低对手的攻击"}, "shelter": {"name": "闭关", "effect": "将皮肤变得坚硬如铁盾，\n从而大幅提高自己的防御"}, "tripleArrows": {"name": "三连箭", "effect": "使出一记腿技后同时发射３箭。\n有时会降低对手的防御或使对手畏缩。\n容易击中要害"}, "infernalParade": {"name": "群魔乱舞", "effect": "用无数的火球进行攻击。有时会让对手陷\n入灼伤状态。攻击处于异常状态\n的对手时，威力会变成２倍"}, "ceaselessEdge": {"name": "秘剑・千重涛", "effect": "用贝壳之剑进行攻击。\n散落的贝壳碎片会散落\n在对手脚下成为撒菱"}, "bleakwindStorm": {"name": "枯叶风暴", "effect": "用足以让身心都止不住颤抖的冰冷\n狂风进行攻击。有时会降低对手的速度"}, "wildboltStorm": {"name": "鸣雷风暴", "effect": "呼唤雷云引起风暴，用雷与风进行\n激烈的攻击。有时会让对手陷入麻痹状态"}, "sandsearStorm": {"name": "热沙风暴", "effect": "用灼热的沙子和强烈的风席卷对手\n进行攻击。有时会让对手陷入灼伤状态"}, "lunarBlessing": {"name": "新月祈祷", "effect": "向新月献上祈祷，回复自己和场上\n同伴的ＨＰ和状态"}, "takeHeart": {"name": "勇气填充", "effect": "鼓起冲劲，治愈自己的异常状态，\n同时提高自己的特攻和特防"}, "gMaxWildfire": {"name": "超极巨深渊灭焰", "effect": "超极巨化的喷火龙使出的火属性攻击。\n可在４回合内给予对手伤害"}, "gMaxBefuddle": {"name": "超极巨蝶影蛊惑", "effect": "超极巨化的巴大蝶使出的虫属性攻击。\n会让对手陷入中毒、麻痹或睡眠状态"}, "gMaxVoltCrash": {"name": "超极巨万雷轰顶", "effect": "超极巨化的皮卡丘使出的电属性攻击。\n会让对手陷入麻痹状态"}, "gMaxGoldRush": {"name": "超极巨特大金币", "effect": "超极巨化的喵喵使出的一般属性攻击。\n会让对手陷入混乱状态，\n并可获得金钱"}, "gMaxChiStrike": {"name": "超极巨会心一击", "effect": "超极巨化的怪力使出的格斗属性攻击。\n会变得容易击中要害"}, "gMaxTerror": {"name": "超极巨幻影幽魂", "effect": "超极巨化的耿鬼使出的幽灵属性攻击。\n会踩住对手的影子，\n让其无法被替换"}, "gMaxResonance": {"name": "超极巨极光旋律", "effect": "超极巨化的拉普拉斯使出的冰属性攻击。\n可在５回合内减弱受到的伤害"}, "gMaxCuddle": {"name": "超极巨热情拥抱", "effect": "超极巨化的伊布使出的一般属性攻击。\n会让对手陷入着迷状态"}, "gMaxReplenish": {"name": "超极巨资源再生", "effect": "超极巨化的卡比兽使出的一般属性攻击。\n会让吃掉的树果再生"}, "gMaxMalodor": {"name": "超极巨臭气冲天", "effect": "超极巨化的灰尘山使出的毒属性攻击。\n会让对手陷入中毒状态"}, "gMaxStonesurge": {"name": "超极巨岩阵以待", "effect": "超极巨化的暴噬龟使出的水属性攻击。\n会发射无数锐利的岩石"}, "gMaxWindRage": {"name": "超极巨旋风袭卷", "effect": "超极巨化的钢铠鸦使出的飞行属性攻击。\n可消除反射壁和光墙"}, "gMaxStunShock": {"name": "超极巨异毒电场", "effect": "超极巨化的颤弦蝾螈使出的电属性攻击。\n会让对手陷入中毒或麻痹状态"}, "gMaxFinale": {"name": "超极巨幸福圆满", "effect": "超极巨化的霜奶仙使出的妖精属性攻击。\n可回复我方的ＨＰ"}, "gMaxDepletion": {"name": "超极巨劣化衰变", "effect": "超极巨化的铝钢龙使出的龙属性攻击。\n可减少对手最后使用的招式的ＰＰ"}, "gMaxGravitas": {"name": "超极巨天道七星", "effect": "超极巨化的以欧路普使出的超能力\n属性攻击。在５回合内重力会产生变化"}, "gMaxVolcalith": {"name": "超极巨炎石喷发", "effect": "超极巨化的巨炭山使出的岩石属性攻击。\n可在４回合内给予对手伤害"}, "gMaxSandblast": {"name": "超极巨沙尘漫天", "effect": "超极巨化的沙螺蟒使出的地面属性攻击。\n在４～５回合内会狂刮沙暴"}, "gMaxSnooze": {"name": "超极巨睡魔降临", "effect": "超极巨化的长毛巨魔使出的恶属性攻击。\n会通过打大哈欠让对手产生睡意"}, "gMaxTartness": {"name": "超极巨酸不溜丢", "effect": "超极巨化的苹裹龙使出的草属性攻击。\n会降低对手的闪避率"}, "gMaxSweetness": {"name": "超极巨琼浆玉液", "effect": "超极巨化的丰蜜龙使出的草属性攻击。\n会治愈我方的异常状态"}, "gMaxSmite": {"name": "超极巨天谴雷诛", "effect": "超极巨化的布莉姆温使出的\n妖精属性攻击。\n会让对手陷入混乱状态"}, "gMaxSteelsurge": {"name": "超极巨钢铁阵法", "effect": "超极巨化的大王铜象使出的钢属性攻击。\n会发射无数锐利的刺"}, "gMaxMeltdown": {"name": "超极巨液金熔击", "effect": "超极巨化的美录梅塔使出的钢属性攻击。\n会让对手无法连续使出相同的招式"}, "gMaxFoamBurst": {"name": "超极巨激漩泡涡", "effect": "超极巨化的巨钳蟹使出的水属性攻击。\n会大幅降低对手的速度"}, "gMaxCentiferno": {"name": "超极巨百火焚野", "effect": "超极巨化的焚焰蚣使出的火属性攻击。\n可在４～５回合内将对手困在火焰中"}, "gMaxVineLash": {"name": "超极巨灰飞鞭灭", "effect": "超极巨化的妙蛙花使出的草属性攻击。\n可在４回合内给予对手伤害"}, "gMaxCannonade": {"name": "超极巨水炮轰灭", "effect": "超极巨化的水箭龟使出的水属性攻击。\n可在４回合内给予对手伤害"}, "gMaxDrumSolo": {"name": "超极巨狂擂乱打", "effect": "超极巨化的轰擂金刚猩使出的\n草属性攻击。\n不会受到对手特性的干扰"}, "gMaxFireball": {"name": "超极巨破阵火球", "effect": "超极巨化的闪焰王牌使出的火属性攻击。\n不会受到对手特性的干扰"}, "gMaxHydrosnipe": {"name": "超极巨狙击神射", "effect": "超极巨化的千面避役使出的水属性攻击。\n不会受到对手特性的干扰"}, "gMaxOneBlow": {"name": "超极巨夺命一击", "effect": "超极巨化的武道熊师使出的恶属性攻击。\n是可以无视极巨防壁的一击"}, "gMaxRapidFlow": {"name": "超极巨流水连击", "effect": "超极巨化的武道熊师使出的水属性攻击。\n是可以无视极巨防壁的连击"}, "teraBlast": {"name": "太晶爆发", "effect": "太晶化时，会放出太晶属性的能量攻击。\n比较自己的攻击和特攻，\n用数值相对较高的一项给予对方伤害。\n（其他属性）／用攻击和特攻数\n值较高的一项给予伤害。\n对正处于太晶化的对手效果绝佳。\n自己的攻击和特攻会降低。（星晶"}, "silkTrap": {"name": "线阱", "effect": "用丝设置陷阱。防住对方攻击的同时，\n能够降低所接触到的对手的速度"}, "axeKick": {"name": "下压踢", "effect": "将踢起的脚跟往下劈向对手进行攻击。\n有时会使对手混乱。\n如果劈偏则自己会受到伤害"}, "lastRespects": {"name": "扫墓", "effect": "为了化解伙伴的悔恨而进行攻击。\n被打倒的我方宝可梦越多，\n招式的威力越高"}, "luminaCrash": {"name": "琉光冲激", "effect": "放出连精神都能影响到的奇妙怪光\n进行攻击。会大幅降低对方的特防"}, "orderUp": {"name": "上菜", "effect": "以潇洒的身手进行攻击。\n若口中有米立龙，会按其样子提高能力"}, "jetPunch": {"name": "喷射拳", "effect": "将激流覆盖于拳头，以肉眼无法辨\n识的速度打出拳击。必定能够先制攻击"}, "spicyExtract": {"name": "辣椒精华", "effect": "放出极为辛辣的精华。\n对手的攻击会大幅提高，防御会大幅降低"}, "spinOut": {"name": "疾速转轮", "effect": "通过往腿上增加负荷，\n以激烈的旋转给予对手伤害。\n自己的速度会大幅降低"}, "populationBomb": {"name": "鼠数儿", "effect": "伙伴们会纷纷赶来集合，\n以群体行动给予对手攻击。\n连续命中１～１０次"}, "iceSpinner": {"name": "冰旋", "effect": "脚上覆盖薄冰，旋转着撞击对手。\n通过旋转的动作破坏场地"}, "glaiveRush": {"name": "巨剑突击", "effect": "有勇无谋的舍身突击。使出招式后，\n对手的攻击必定会命中，\n且伤害会变成２倍"}, "revivalBlessing": {"name": "复生祈祷", "effect": "通过以慈爱之心祈祷，\n让陷入昏厥的后备宝可梦\n以回复一半ＨＰ的状态复活"}, "saltCure": {"name": "盐腌", "effect": "使对手陷入盐腌状态，\n每回合给予对手伤害。\n对手为钢或水属性时会更痛苦"}, "tripleDive": {"name": "三连钻", "effect": "以默契的跳跃溅起水花击向对手。\n连续３次给予伤害"}, "mortalSpin": {"name": "晶光转转", "effect": "通过旋转来攻击对手。\n可以摆脱绑紧、紧束、寄生种子等招式。\n还能让对手陷入中毒状态"}, "doodle": {"name": "描绘", "effect": "把握并映射出对手的本质，\n让自己和同伴宝可梦的特性\n变得和对手相同"}, "filletAway": {"name": "甩肉", "effect": "削减自己的ＨＰ，大幅提高攻击和\n特攻以及速度"}, "kowtowCleave": {"name": "仆刀", "effect": "下跪让对手大意后发起袭击劈向对手。\n攻击必定会命中"}, "flowerTrick": {"name": "千变万花", "effect": "将做了手脚的花束扔向对手进行攻击。\n必定会命中，且会击中要害"}, "torchSong": {"name": "闪焰高歌", "effect": "如唱歌一样喷出熊熊燃烧的火焰\n烧焦对手。会提高自己的特攻"}, "aquaStep": {"name": "流水旋舞", "effect": "以盈盈欲滴的轻快步伐戏耍对手并\n给予其伤害。会提高自己的速度"}, "ragingBull": {"name": "怒牛", "effect": "狂怒暴牛的猛烈冲撞。\n招式的属性随形态改变，\n光墙和反射壁等招式也能破坏"}, "makeItRain": {"name": "淘金潮", "effect": "扔出大量硬币攻击。自己的特攻会降低，\n战斗后还可以拿到钱"}, "psyblade": {"name": "精神剑", "effect": "用无形的利刃劈开对手。\n处于电气场地时，\n招式威力会变成１．５倍"}, "hydroSteam": {"name": "水蒸气", "effect": "将煮得翻滚的开水猛烈地喷向对手。\n日照强烈时，招式威力不但不会降低，\n还会变成１．５倍"}, "ruination": {"name": "大灾难", "effect": "引发毁灭性的灾厄，使对手的ＨＰ减半"}, "collisionCourse": {"name": "全开猛撞", "effect": "边变形边凶暴地落下，\n并引发起古老的大爆炸。若针对到弱点，\n威力会进一步"}, "electroDrift": {"name": "闪电猛冲", "effect": "边变形边高速奔走，并以未知的电\n击贯穿对手。若针对到弱点，\n威力会进一步"}, "shedTail": {"name": "断尾", "effect": "削减自己的ＨＰ，制造分身后会返回，\n并和后备宝可梦进行替换"}, "chillyReception": {"name": "冷笑话", "effect": "留下冷场的冷笑话后，\n和后备宝可梦进行替换。\n在５回合内会下雪"}, "tidyUp": {"name": "大扫除", "effect": "将撒菱、隐形岩、黏黏网、毒菱、\n替身全部扫除掉。自己的攻击和速\n度会提高"}, "snowscape": {"name": "雪景", "effect": "在５回合内会下雪。冰属性的防御会提高"}, "pounce": {"name": "虫扑", "effect": "飞扑向对手攻击。会降低对手的速度"}, "trailblaze": {"name": "起草", "effect": "跳出草丛进行攻击。通过轻快的步\n伐会提高自己的速度"}, "chillingWater": {"name": "泼冷水", "effect": "泼洒冰冷得足以让对手失去活力的\n水进行攻击。会降低对手的攻击"}, "hyperDrill": {"name": "强力钻", "effect": "急速旋转尖锐的身体部位贯穿对手。\n可以无视守住和看穿等招式"}, "twinBeam": {"name": "双光束", "effect": "从两眼发射出神奇的光线攻击。\n连续２次给予伤害"}, "rageFist": {"name": "愤怒之拳", "effect": "将愤怒化为力量攻击。\n受到攻击的次数越多，招式的威力越高"}, "armorCannon": {"name": "铠农炮", "effect": "熊熊燃烧自己的铠甲，\n将其做成炮弹射出攻击。\n自己的防御和特防会降低"}, "bitterBlade": {"name": "悔念剑", "effect": "将对世间的留恋聚集于剑尖，\n并斩击对手。可以回复给予对手伤害的\n一半ＨＰ"}, "doubleShock": {"name": "电光双击", "effect": "将全身所有的电力放出，\n给予对手大大的伤害。\n自己的电属性将会消失"}, "gigatonHammer": {"name": "巨力锤", "effect": "连同身体转起巨大的锤子进行攻击。\n这个招式无法连续使出２次"}, "comeuppance": {"name": "复仇", "effect": "使出招式前，将最后受到的招式的\n伤害大力返还给对手"}, "aquaCutter": {"name": "水波刀", "effect": "如刀刃般喷射出加压的水切开对手。\n容易击中要害"}, "blazingTorque": {"name": "灼热暴冲", "effect": "攻击目标造成伤害，有30%的几\n率使目标陷入灼伤状态。"}, "wickedTorque": {"name": "黑暗暴冲", "effect": "攻击目标造成伤害，有30%的几\n率使目标陷入睡眠状态。"}, "noxiousTorque": {"name": "剧毒暴冲", "effect": "攻击目标造成伤害，有30%的几\n率使目标陷入中毒状态。"}, "combatTorque": {"name": "格斗暴冲", "effect": "攻击目标造成伤害，有30%的几\n率使目标陷入麻痹状态。"}, "magicalTorque": {"name": "魔法暴冲", "effect": "攻击目标造成伤害，有30%的几\n率使目标陷入混乱状态。"}, "bloodMoon": {"name": "血月", "effect": "从赤红如血的满月发射出全部的气势。\n这个招式无法连续使出２次"}, "matchaGotcha": {"name": "刷刷茶炮", "effect": "发射经搅拌的茶的大炮，\n可以回复给予对手伤害的一半ＨＰ，\n有时会让对手陷入灼伤状态"}, "syrupBomb": {"name": "糖浆炸弹", "effect": "使粘稠的麦芽糖浆爆炸，\n让对手陷入满身糖状态，\n在３回合内持续降\n低其速度"}, "ivyCudgel": {"name": "棘藤棒", "effect": "用缠有藤蔓的棍棒殴打。\n属性会随所戴的面具而改变。\n容易击中要害"}, "electroShot": {"name": "电光束", "effect": "第１回合收集电力提高特攻，\n第２回合将高压的电力发射出去。\n下雨天气时能立刻发射"}, "teraStarstorm": {"name": "晶光星群", "effect": "照射出结晶的力量来驱逐敌人。\n太乐巴戈斯在星晶形态下使出时，\n能对所有对手造成伤害"}, "fickleBeam": {"name": "随机光", "effect": "发射光线进行攻击。有时其他的头\n也会合力发射镭射，让招式威力变成２倍"}, "burningBulwark": {"name": "火焰守护", "effect": "用超高温的体毛防住对手攻击的同时，\n让接触到自己的对手灼伤"}, "thunderclap": {"name": "迅雷", "effect": "可以比对手先使出电击进行攻击。\n对手使出的招式如果不是攻击招式\n则会失败"}, "mightyCleave": {"name": "强刃攻击", "effect": "用积蓄在头部的光来斩切对手。\n可以无视守护进行攻击"}, "tachyonCutter": {"name": "迅子利刃", "effect": "接连发射出粒子的利刃，\n连续２次给予伤害。攻击必定会命中"}, "hardPress": {"name": "硬压", "effect": "用手臂或钳子压迫对手。\n对手剩余的ＨＰ越多，威力越大"}, "dragonCheer": {"name": "龙声鼓舞", "effect": "以龙之鼓舞提高士气，\n让我方的招式变得容易击中要害。\n对龙属性的鼓舞效果会更强"}, "alluringVoice": {"name": "魅诱之声", "effect": "用天使般的歌声攻击对手。\n会让此回合内能力有提高的\n宝可梦陷入混乱状态"}, "temperFlare": {"name": "豁出去", "effect": "以自暴自弃的气势进行攻击。\n如果上一回合招式没有命中，\n威力就会翻倍"}, "supercellSlam": {"name": "闪电强袭", "effect": "让身体带电后压向对手。\n如果没有命中则自己会受到伤害"}, "psychicNoise": {"name": "精神噪音", "effect": "用令对手不舒服的音波进行攻击。\n让对手在２回合内无法通过招式、\n特性或携带的道具回复ＨＰ"}, "upperHand": {"name": "快手还击", "effect": "察觉到对手的动作后用掌根攻击，\n让对手畏缩。如果对手使出的招式\n不是先制攻击，则会失败"}, "malignantChain": {"name": "邪毒锁链", "effect": "用由毒形成的锁链缠住对手注入毒\n素加以侵蚀。有时会让对手陷入剧毒状态"}}