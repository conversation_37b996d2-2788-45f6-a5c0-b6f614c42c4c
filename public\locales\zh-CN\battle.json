{"bossAppeared": "{{boss<PERSON><PERSON>}} 出现了。", "trainerAppeared": "{{<PERSON><PERSON><PERSON>}}\n想要和你对战！", "trainerAppearedDouble": "{{<PERSON><PERSON><PERSON>}}\n想要和你对战！", "trainerSendOut": "{{trainerName}}派出了\n{{pokemonName}}！", "singleWildAppeared": "一只野生的{{pokemonName}}出现了！", "multiWildAppeared": "野生的{{pokemonName1}}\n和{{pokemonName2}}出现了！", "playerComeBack": "回来吧，{{pokemonName}}！", "trainerComeBack": "{{trainerName}}收回了{{pokemonName}}！", "playerGo": "去吧！{{pokemonName}}！", "trainerGo": "{{trainerName}}派出了\n{{pokemonName}}！", "pokemonDraggedOut": "{{pokemon<PERSON>ame}}\n被拖进了战斗！", "switchQuestion": "要更换\n{{pokemonName}}吗？", "trainerDefeated": "你击败了\n{{trainer<PERSON>ame}}！", "moneyWon": "你赢得了\n₽{{moneyAmount}}！", "moneyPickedUp": "捡到了₽{{moneyAmount}}！", "pokemonCaught": "{{pokemon<PERSON>ame}}被抓住了！", "pokemonCaughtButChallenge": "抓到了{{pokemonName}}！\n但是因为挑战限制无法加入队伍。", "pokemonObtained": "你获得了{{pokemonName}}！", "pokemonBrokeFree": "噢不！\n宝可梦挣脱了！", "pokemonFled": "野生的{{pokemonName}}逃走了！", "playerFled": "你从{{pokemon<PERSON>ame}}那里逃走了！", "addedAsAStarter": "增加了{{pokemonName}}作为\n一个新的初始宝可梦！", "partyFull": "你的队伍已满员。是否放生其他宝可梦\n为{{pokemonName}}腾出空间？", "pokemon": "宝可梦", "sendOutPokemon": "上吧！\n{{pokemonName}}！", "hitResultCriticalHit": "击中了要害！", "hitResultSuperEffective": "效果拔群！", "hitResultNotVeryEffective": "收效甚微…", "hitResultNoEffect": "对{{pokemon<PERSON>ame}}没有效果！！", "hitResultImmune": "对于{{pokemonName}}，\n完全没有效果！", "hitResultOneHitKO": "一击必杀！", "attackFailed": "但是失败了！", "attackMissed": "没有命中{{pokemonNameWithAffix}}！", "attackHitsCount": "击中{{count}}次！", "rewardGain": "你获得了\n{{modifierName}}！", "rewardGainCount": "你获得了\n{{count}}个{{modifierName}}!", "expGain": "{{pokemonName}}获得了{{exp}} 点经验值！", "levelUp": "{{pokemonName}}\n上升到了等级{{level}}！", "learnMove": "{{pokemonName}}学会了{{moveName}}！", "learnMovePrompt": "{{pokemonName}}想要学习{{moveName}}。", "learnMoveLimitReached": "但是，{{pokemonName}}已经学会了\n四个技能", "learnMoveReplaceQuestion": "要忘记一个技能并学习{{moveName}}吗？", "learnMoveStopTeaching": "不再尝试学习{{moveName}}？", "learnMoveNotLearned": "{{pokemonName}}没有学会{{moveName}}。", "learnMoveForgetQuestion": "要忘记哪个技能？", "learnMoveForgetSuccess": "{{pokemonName}}忘记了\n如何使用{{moveName}}。", "countdownPoof": "@d{32}1, @d{15}2 @d{15}… @d{15}… @d{15}@s{se/pb_bounce_1}空！", "learnMoveAnd": "然后……", "levelCapUp": "等级上限提升到{{levelCap}}！", "moveNotImplemented": "{{moveName}}尚未实装，无法选择。", "moveNoPP": "这个技能的PP用完了", "moveDisabled": "{{moveName}}被禁用！", "moveDisabledTorment": "{{pokemonNameWithAffix}}遭到了无理取闹，\n因此无法继续使出相同的招式！", "moveDisabledTaunt": "{{pokemonNameWithAffix}}受到了挑衅，无法使出{{moveName}}！", "moveDisabledHealBlock": "{{pokemonNameWithAffix}}由于{{healBlockName}}而无法使出{{moveName}}！", "moveDisabledImprison": "{{pokemonNameWithAffix}}因封印而无法使出{{moveName}}！", "moveDisabledConsecutive": "不能连续使出２次{{moveName}}！", "moveDisabledBelch": "{{pokemonNameWithAffix}}\n因没有吃树果而无法使出招式！", "moveDisabledNoBerry": "没有携带树果，无法使出招式！", "moveDisabledGravity": "{{pokemonNameWithAffix}}因重力太强\n而无法使出{{moveName}}！", "canOnlyUseMove": "{{pokemonName}}只能使出{{moveName}}！", "moveCannotBeSelected": "无法选择{{moveName}}！", "moveCannotUseChallenge": "由于挑战，{{moveName}}不可使用！", "disableInterruptedMove": "{{pokemonNameWithAffix}}的{{moveName}}\n被无效化了！", "throatChopInterruptedMove": "{{pokemonName}}因深渊突刺的效果无法使出招式！", "noPokeballForce": "一股无形的力量阻止了你使用精灵球。", "noPokeballForceFinalBoss": "一股无法抵抗的力量\n让你无法使用精灵球！", "noPokeballForceFinalBossCatchable": "一股无法抵抗的力量\n让你无法使用精灵球！需要先削弱它！", "noPokeballTrainer": "你不能捕捉其他训练家的宝可梦！", "noPokeballMulti": "只能在剩下一只宝可梦时才能扔出精灵球！", "noPokeballStrong": "目标宝可梦太强了，无法捕捉！\n你需要先削弱它！", "noPokeballMysteryEncounter": "你无法\n捕捉这只宝可梦！", "noEscapeForce": "一股无形的力量阻止你逃跑。", "noEscapeTrainer": "你不能从与训练家的战斗中逃跑！", "noEscapePokemon": "{{pokemonName}}的{{moveName}}\n阻止了你{{escapeVerb}}！", "noEscapeSwitch": "这只宝可梦不能被交换！", "noEscapeFlee": "这只宝可梦无法逃脱！", "runAwaySuccess": "成功逃走了！", "runAwayCannotEscape": "无法逃走！", "escapeVerbSwitch": "切换", "escapeVerbFlee": "逃跑", "notDisabled": "{{moveName}}不再被禁用！", "turnEndHpRestore": "{{pokemon<PERSON>ame}}的体力恢复了。", "hpIsFull": "{{pokemon<PERSON>ame}}的体力已满！", "skipItemQuestion": "你确定要跳过拾取道具吗？", "itemStackFull": "{{fullItemName}}持有数达到上限，\n你获得了{{itemName}}作为替代。", "eggHatching": "咦？", "eggSkipPrompt": "{{eggsToHatch}}个蛋已孵化。\n是否快进到孵化总结？", "ivScannerUseQuestion": "对{{pokemon<PERSON>ame}}使用个体值扫描仪？", "wildPokemonWithAffix": "野生的{{pokemonName}}", "foePokemonWithAffix": "对手的{{pokemonName}}", "useMove": "{{pokemonNameWithAffix}}使用了\n{{moveName}}！", "magicCoatActivated": "{{pokemonNameWithAffix}}将{{moveName}}反射了回去！", "drainMessage": "{{pokemon<PERSON>ame}}\n吸取了体力！", "regainHealth": "{{pokemon<PERSON>ame}}\n回复了体力！", "stealEatBerry": "{{pokemonName}}夺取并吃掉了\n{{targetName}}的{{berryName}}！", "ppHealBerry": "{{pokemonNameWithAffix}}用{{berryName}}\n回复了{{moveName}}的PP！", "hpHealBerry": "{{pokemonNameWithAffix}}用{{berryName}}\n回复了体力！", "fainted": "{{pokemonNameWithAffix}}\n倒下了！", "statsAnd": "和", "stats": "能力", "statRose_one": "{{pokemonNameWithAffix}}的{{stats}}提高了！", "statRose_other": "{{pokemonNameWithAffix}}的{{stats}}提高了！", "statSharplyRose_one": "{{pokemonNameWithAffix}}的{{stats}}大幅提高了！", "statSharplyRose_other": "{{pokemonNameWithAffix}}的{{stats}}大幅提高了！", "statRoseDrastically_one": "{{pokemonNameWithAffix}}的{{stats}}极大幅提高了！", "statRoseDrastically_other": "{{pokemonNameWithAffix}}的{{stats}}极大幅提高了！", "statWontGoAnyHigher_one": "{{pokemonNameWithAffix}}的{{stats}}已经无法再提高了！", "statWontGoAnyHigher_other": "{{pokemonNameWithAffix}}的{{stats}}已经无法再提高了！", "statFell_one": "{{pokemonNameWithAffix}}的{{stats}}降低了！", "statFell_other": "{{pokemonNameWithAffix}}的{{stats}}降低了！", "statHarshlyFell_one": "{{pokemonNameWithAffix}}的{{stats}}大幅降低了！", "statHarshlyFell_other": "{{pokemonNameWithAffix}}的{{stats}}大幅降低了！", "statSeverelyFell_one": "{{pokemonNameWithAffix}}的{{stats}}极大幅降低了！", "statSeverelyFell_other": "{{pokemonNameWithAffix}}的{{stats}}极大幅降低了！", "statWontGoAnyLower_one": "{{pokemonNameWithAffix}}的{{stats}}已经无法再降低了！", "statWontGoAnyLower_other": "{{pokemonNameWithAffix}}的{{stats}}已经无法再降低了！", "transformedIntoType": "{{pokemonName}}变成了\n{{type}}属性！", "retryBattle": "你要从对战开始时重试么？", "unlockedSomething": "{{unlockedThing}}\n已解锁。", "congratulations": "恭喜！", "beatModeFirstTime": "{{speciesName}}首次击败了{{gameMode}}！\n你获得了{{newModifier}}！", "ppReduced": "降低了{{targetName}}的\n{{moveName}}的PP{{reduction}}点！", "mysteryEncounterAppeared": "这是什么？", "battlerTagsHealBlock": "{{pokemonNameWithAffix}}无法回复其生命！", "battlerTagsHealBlockOnRemove": "{{pokemonNameWithAffix}}可以回复其生命了！", "pokemonTerastallized": "{{pokemonNameWithAffix}} 太晶化成了 {{type}}属性！"}