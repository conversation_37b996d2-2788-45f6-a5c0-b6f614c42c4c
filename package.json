{"name": "workflow-test", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"check-locales": "node scripts/locales-key-format/main.js"}, "repository": {"type": "git", "url": "git+https://github.com/fabske0/workflow-test.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/fabske0/workflow-test/issues"}, "homepage": "https://github.com/fabske0/workflow-test#readme", "dependencies": {"@actions/core": "^1.11.1", "@actions/github": "^6.0.1"}}