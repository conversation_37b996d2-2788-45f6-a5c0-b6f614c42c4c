{"hitWithRecoil": "{{pokemon<PERSON>ame}}\n受到了反作用力造成的傷害！", "cutHpPowerUpMove": "{{pokemon<PERSON>ame}}\n削減體力並提升了招式威力！", "absorbedElectricity": "{{pokemon<PERSON>ame}}\n吸收了电力！", "switchedStatChanges": "{{pokemon<PERSON>ame}}和對手互換了\n自身的能力變化！", "switchedTwoStatChanges": "{{pokemonName}} 和對手互換了自身的{{firstStat}}和{{secondStat}}的能力變化！", "switchedStat": "{{pokemonName}} 互換了各自的{{stat}}！", "sharedGuard": "{{pokemon<PERSON>ame}} 平分了各自的防守！", "sharedPower": "{{pokemon<PERSON>ame}} 平分了各自的力量！", "shiftedStats": "{{pokemonName}}\n互換了{{statToSwitch}}和{{statToSwitchWith}}！", "goingAllOutForAttack": "{{pokemon<PERSON>ame}}拿出全力了！", "regainedHealth": "{{pokemon<PERSON>ame}}的\n體力回復了！", "keptGoingAndCrashed": "{{pokemon<PERSON>ame}}因勢頭過猛\n而撞到了地面！", "fled": "{{pokemon<PERSON>ame}}\n逃走了！", "cannotBeSwitchedOut": "{{pokemon<PERSON>ame}}\n無法被收回！", "swappedAbilitiesWithTarget": "{{pokemon<PERSON>ame}}\n互換了各自的特性！", "coinsScatteredEverywhere": "金幣散落一地！", "attackedByItem": "{{pokemonName}}被\n{{itemName}}襲擊了！", "whippedUpAWhirlwind": "{{pokemon<PERSON>ame}}周圍的\n空氣產生了旋渦！", "flewUpHigh": "{{pokemon<PERSON>ame}}\n飛向了高空！", "tookInSunlight": "{{pokemon<PERSON>ame}}\n吸收了光線！", "dugAHole": "{{pokemon<PERSON>ame}}\n鑽進了地下！", "loweredItsHead": "{{pokemon<PERSON>ame}}\n把頭縮了進去！", "isGlowing": "強光包圍了\n{{pokemonName}}！", "bellChimed": "鈴聲響徹四周！", "foresawAnAttack": "{{pokemon<PERSON>ame}}\n預知了未來的攻擊！", "isTighteningFocus": "{{pokemon<PERSON>ame}}正在集中注意力！", "lostFocus": "{{pokemonName}}聚氣時\n受到干擾，無法使出招式！", "hidUnderwater": "{{pokemon<PERSON>ame}}\n潛入了水中！", "soothingAromaWaftedThroughArea": "怡人的香氣擴散了開來！", "sprangUp": "{{pokemon<PERSON>ame}}\n高高地跳了起來！", "choseDoomDesireAsDestiny": "{{pokemon<PERSON>ame}}\n將破滅之願託付給了未來！", "vanishedInstantly": "{{pokemon<PERSON>ame}}的身影\n瞬間消失了！", "tookTargetIntoSky": "{{pokemonName}}將{{targetName}}\n帶上了高空！", "becameCloakedInFreezingLight": "{{pokemon<PERSON>ame}}\n被冷光包圍了！", "becameCloakedInFreezingAir": "{{pokemon<PERSON>ame}}\n被冰凍的空氣包圍了！", "isChargingPower": "{{pokemon<PERSON>ame}}\n正在積蓄力量！", "burnedItselfOut": "{{pokemon<PERSON>ame}}的火焰燃盡了！", "startedHeatingUpBeak": "{{pokemon<PERSON>ame}}\n開始給鳥嘴加熱了！", "setUpShellTrap": "{{pokemon<PERSON>ame}}\n設下了陷阱甲殼！", "isOverflowingWithSpacePower": "{{pokemon<PERSON>ame}}湧起了宇宙的力量！", "usedUpAllElectricity": "{{pokemon<PERSON>ame}}\n用盡了電力！", "stoleItem": "{{pokemonName}}从{{targetName}}那裏\n奪取了{{itemName}}！", "incineratedItem": "{{pokemonName}}燒掉了\n{{targetName}}的{{itemName}}！", "knockedOffItem": "{{pokemonName}}拍落了\n{{targetName}}的{{itemName}}！", "tookMoveAttack": "{{pokemonName}}\n受到了{{moveName}}的攻擊！", "cutOwnHpAndMaximizedStat": "{{pokemonName}}\n削減體力並釋放了全部{{statName}}！", "copiedStatChanges": "{{pokemonName}}複製了\n{{targetName}}的能力變化！", "magnitudeMessage": "震級{{magnitude}}！", "tookAimAtTarget": "{{pokemonName}}將目標對準了\n{{targetName}}！", "transformedIntoType": "{{pokemonName}} \n變成了{{typeName}}屬性！", "copiedMove": "{{pokemonName}}\n複製了{{moveName}}！", "sketchedMove": "{{pokemonName}}\n對{{moveName}}進行了寫生！", "acquiredAbility": "{{pokemonName}}的特性\n變为{{abilityName}}了！", "copiedTargetAbility": "{{pokemonName}}複製了\n{{targetName}}的{{abilityName}}！", "transformedIntoTarget": "{{pokemonName}}\n變身成了{{targetName}}！", "tryingToTakeFoeDown": "{{pokemon<PERSON>ame}}\n想和對手同歸於盡！", "addType": "{{pokemonName}}\n增加了{{typeName}}屬性！", "cannotUseMove": "{{pokemonName}}\n無法使用{{moveName}}！", "healHp": "{{pokemon<PERSON>ame}}的\n體力回復了！", "sacrificialFullRestore": "{{pokemon<PERSON>ame}}的\n治癒之願實現了！", "invertStats": "{{pokemon<PERSON>ame}}的\n能力變化顛倒過來了！", "resetStats": "{{pokemon<PERSON>ame}}的\n能力變化復原了！", "statEliminated": "所有能力都復原了！", "faintCountdown": "{{pokemonName}}\n將在{{turnCount}}回合後滅亡！", "copyType": "{{pokemonName}}變成了{{targetPokemonName}}的屬性！", "suppressAbilities": "{{pokemon<PERSON>ame}}的特性\n變得無效了！", "revivalBlessing": "{{pokemon<PERSON>ame}}復活了！", "swapArenaTags": "{{pokemon<PERSON>ame}}\n交換了雙方的場地效果！", "chillyReception": "{{pokemon<PERSON>ame}}\n說了冷笑話！", "exposedMove": "{{pokemonName}}識破了\n{{targetPokemonName}}的原形！", "safeguard": "{{targetName}}\n正受到神秘之幕的保護！", "restBecameHealthy": "{{pokemon<PERSON>ame}}睡著了，\n變得精力充沛！", "substituteOnOverlap": "但是，{{pokemonName}}的\n替身已經出現了。", "substituteNotEnoughHp": "但是，體力已經不夠\n放出替身了！", "afterYou": "{{targetName}}\n接受了對手的好意！", "combiningPledge": "兩個招式合而為一！\n這是合體招式！", "awaitingPledge": "{{userPokemonName}}正在等候\n{{allyPokemonName}}……", "corrosiveGasItem": "{{pokemonName}}把{{targetName}}的\n{{itemName}}融化了！", "instructingMove": "根據{{userPokemonName}}的指示，\n{{targetPokemonName}}使出了招式！", "lunarDanceRestore": "{{pokemon<PERSON>ame}}\n被神秘的月光包圍了！", "stealPositiveStats": "{{pokemonName}}奪取了\n{{targetName}}提高的那部分能力！", "naturePowerUse": "{{pokemonName}}的自然之力\n變成了{{moveName}}！", "forceLast": "延後了{{targetPokemonName}}的順序！", "splash": "但是什麼事也沒發生！", "fallDown": "{{targetPokemonName}}\n被擊落，掉到了地面！", "celebrate": "恭喜恭喜！\n{{playerName}}！！", "struggle": "{{pokemon<PERSON>ame}}\n沒有可用來施展的招式！"}