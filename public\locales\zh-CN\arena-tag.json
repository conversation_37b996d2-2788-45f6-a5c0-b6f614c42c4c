{"yourTeam": "我方队伍", "opposingTeam": "敌方队伍", "arenaOnRemove": "{{moveName}}的效果消失了！", "arenaOnRemovePlayer": "{{moveName}}在我方的效果消失了！", "arenaOnRemoveEnemy": "{{moveName}}在敌方的效果消失了！", "mistOnAdd": "{{pokemonNameWithAffix}}的一方被\n白雾包围了！", "mistApply": "正受到白雾的保护\n能力不会被降低！", "reflectOnAdd": "反射壁使\n物理抗性提高了！", "reflectOnAddPlayer": "反射壁使我方的\n物理抗性提高了！", "reflectOnAddEnemy": "反射壁使敌方的\n物理抗性提高了！", "lightScreenOnAdd": "光墙使\n特殊抗性提高了！", "lightScreenOnAddPlayer": "光墙使我方的\n特殊抗性提高了！", "lightScreenOnAddEnemy": "光墙使敌方的\n特殊抗性提高了！", "auroraVeilOnAdd": "极光幕使\n物理和特殊抗性提高了！", "auroraVeilOnAddPlayer": "极光幕使我方的\n物理和特殊抗性提高了！", "auroraVeilOnAddEnemy": "极光幕使敌方的\n物理和特殊抗性提高了！", "conditionalProtectOnAdd": "{{moveName}}\n保护了！", "conditionalProtectOnAddPlayer": "{{moveName}}\n保护了我方！", "conditionalProtectOnAddEnemy": "{{moveName}}\n保护了敌方！", "conditionalProtectApply": "{{moveName}}\n保护了{{pokemonNameWithAffix}}！", "matBlockOnAdd": "{{pokemonNameWithAffix}}正在\n伺机使出掀榻榻米！", "noCritOnAddPlayer": "{{moveName}}保护了你的\n队伍不被击中要害！", "noCritOnAddEnemy": "{{moveName}}保护了对方的\n队伍不被击中要害！", "noCritOnRemove": "{{pokemonNameWithAffix}}的{{moveName}}\n效果消失了！", "wishTagOnAdd": "{{pokemonNameWithAffix}}的\n祈愿实现了！", "mudSportOnAdd": "电气的威力减弱了！", "mudSportOnRemove": "玩泥巴的效果消失了！", "waterSportOnAdd": "火焰的威力减弱了！", "waterSportOnRemove": "玩水的效果消失了！", "plasmaFistsOnAdd": "等离子雨倾盆而下！", "spikesOnAdd": "{{opponentDesc}}脚下\n散落着{{moveName}}！", "spikesActivateTrap": "{{pokemonNameWithAffix}}\n受到了撒菱的伤害！", "toxicSpikesOnAdd": "{{opponentDesc}}脚下\n散落着{{moveName}}！", "toxicSpikesActivateTrapPoison": "{{pokemonNameWithAffix}}\n吸收了{{moveName}}！", "stealthRockOnAdd": "{{opponentDesc}}周围\n开始浮现出尖锐的岩石！", "stealthRockActivateTrap": "尖锐的岩石扎进了\n{{pokemonNameWithAffix}}的体内！", "stickyWebOnAdd": "{{opponentDesc}}的脚下\n延伸出了{{moveName}}！", "stickyWebActivateTrap": "{{pokemon<PERSON>ame}}\n被黏黏网粘住了！", "trickRoomOnAdd": "{{pokemonNameWithAffix}}\n扭曲了时空！", "trickRoomOnRemove": "扭曲的时空复原了！", "gravityOnAdd": "重力变强了！", "gravityOnRemove": "重力复原了！", "tailwindOnAdd": "从身后\n吹起了顺风！", "tailwindOnAddPlayer": "从我方身后\n吹起了顺风！", "tailwindOnAddEnemy": "从敌方身后\n吹起了顺风！", "tailwindOnRemove": "顺风停止了！", "tailwindOnRemovePlayer": "我方的顺风停止了！", "tailwindOnRemoveEnemy": "敌方的顺风停止了！", "happyHourOnAdd": "大家被欢乐的\n气氛包围了！", "happyHourOnRemove": "气氛回复到平常了。", "safeguardOnAdd": "整个场地被\n神秘之幕包围了！", "safeguardOnAddPlayer": "我方被\n神秘之幕包围了！", "safeguardOnAddEnemy": "对手被\n神秘之幕包围了！", "safeguardOnRemove": "包围整个场地的\n神秘之幕消失了！", "safeguardOnRemovePlayer": "包围我方的\n神秘之幕消失了！", "safeguardOnRemoveEnemy": "包围对手的\n神秘之幕消失了！", "fireGrassPledgeOnAdd": "场地被火海包围了！", "fireGrassPledgeOnAddPlayer": "我方周围被火海包围了！", "fireGrassPledgeOnAddEnemy": "对手周围被火海包围了！", "fireGrassPledgeLapse": "{{pokemonNameWithAffix}}受到了火海的伤害！", "waterFirePledgeOnAdd": "彩虹出现在了上空！", "waterFirePledgeOnAddPlayer": "彩虹出现在了我方上空！", "waterFirePledgeOnAddEnemy": "彩虹出现在了对手上空！", "grassWaterPledgeOnAdd": "场地周围延伸出了湿地！", "grassWaterPledgeOnAddPlayer": "在我方周围延伸出了湿地！", "grassWaterPledgeOnAddEnemy": "在对手周围延伸出了湿地！", "fairyLockOnAdd": "下回合任何人都无法逃脱！", "neutralizingGasOnAdd": "周围充满了\n{{pokemonNameWithAffix}}的化学变化气体！", "neutralizingGasOnRemove": "化学变化气体的效果消失了！"}